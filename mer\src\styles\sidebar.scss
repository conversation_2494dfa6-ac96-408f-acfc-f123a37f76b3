#app {

  .main-container {
    min-height: 100%;

    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
    &.leftBar {
      margin-left: $leftBarWidth !important;
    }
    &.leftBar130 {
      margin-left: 130px !important;
    }
    &.leftBar210 {
      margin-left: 180px !important;
    }
    &.leftBar270 {
      margin-left: 270px !important;
    }
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 790;
    overflow: hidden;
    &.leftBar130,&.leftBar270 {
      width: 130px !important;
    }
    &.leftBar210 {
      width: 180px !important;
    }
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-y: scroll !important;
      height: calc(100vh - 50px);

    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100%;
    }

    // menu hover
    // .submenu-title-noDropdown,
    // .el-submenu__title {
    //   &:hover {
    //     background-color: $menuHover;
    //   }
    // }

    // .is-active>.el-submenu__title {
    //   // color: $subMenuActiveText;
    // }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      // min-width: $sideBarWidth;
      background-color: $subMenuBg;

      &:hover {
        background-color: $subMenuHover;
      }
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
      &.leftBar270,&.leftBar130{
        width: 130px !important;
      }
    }

    .main-container {
      margin-left: 54px !important;
      &.leftBar270,&.leftBar130{
        margin-left: 130px !important;
      }
    }
    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;
        text-align: center !important;
        .svg-icon {
          margin-left: 20px;
        }
      }
    }
    .el-submenu {
      overflow: hidden;
      &>.el-submenu__title {
        padding: 0 !important;
        text-align: center !important;
        .svg-icon {
          margin-left: 20px;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover;
    }
  }
  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    // overflow-y: auto;
    min-width: 140px;
    width: 150px;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

.container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.el-submenu .el-menu-item{
  min-width: 150px;
}