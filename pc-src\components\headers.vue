<template>
  <div class="headerss">
    <div >
      <div class="header min_wrapper_1200">
      <div class="headerCon acea-row row-between-wrapper">
        <div>
          <a href="javascript:void(0);" rel="sidebar" @click="AddFavorite()"><span
            class="iconfont icon-shoucang2"></span>收藏本站</a>
          <a href="javascript:void(0);" v-if="$route.path !== '/'">
            <nuxt-link :to="{path:'/'}"><span class="iconfont icon-shouye4"></span>商城首页</nuxt-link>
          </a>
        </div>
        <div class="user acea-row row-middle">
          <nuxt-link :to="{path:'/user',query:{type:0}}" class="item acea-row row-middle" v-if="$auth.loggedIn">
            <div class="pictrue"><img :src="$auth.user.avatar || '/static/f.png'"></div>
            <p class="line1" style="max-width: 135px">{{$auth.user.nickname}}</p>
            <img src="~/assets/images/svip.png" class="king" v-if="$auth.user.is_svip>0 && svip_switch_status == 1">
          </nuxt-link>
          <div @click="showLogin" class="item" v-else>登录/注册</div>
          <nuxt-link :to="{path:'/user/order_list',query:{type:1}}" class="item">我的订单</nuxt-link>
          <nuxt-link v-if="balance_func_status == 1" :to="{path:'/user/balance',query:{type:3}}" class="item">我的余额</nuxt-link>
          <nuxt-link :to="{path:'/merchant_settled'}" v-if="openSettled == 1" class="item" >商户入驻</nuxt-link>
          <nuxt-link :to="{path:'/news_list'}" class="item">资讯信息</nuxt-link>
          <div class="item mobile-store">手机商城
              <div class="gzhaocode">
                <div class="code">
                    <img :src="$store.state.code" alt="">
                    <span>扫码关注公众号</span>
                </div>
                </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    <div class="nav min_wrapper_1200" v-if="$route.path !== '/goods_search'">
      <div class="navCon wrapper_1200 acea-row row-between-wrapper">
        <div class="textPic acea-row row-middle">
          <div class="icon" @click="goHome">
            <img :src="$store.state.logoUrl" v-if="$store.state.logoUrl">
          </div>
        </div>
        <div class="search acea-row">
          <div class="text acea-row row-middle"><span class="iconfont icon-xiazai5"></span>
            <input @keydown.enter.stop="submit" type="text" placeholder="搜索商品" v-model="search" />
          </div>
          <div class="bnt bg-color" @click.stop="submit">搜索</div>
          <div class="search-hot">
              <span v-for="(item, index) in hotSearchList" :key="index" @click="(search = item.keyword) && submit()">{{ item.keyword }}</span>
          </div>
        </div>
        <nuxt-link to="/shopping_cart" class="cartNum">
          <span class="iconfont icon-dingbu-gouwuche"></span>购物车 ({{$store.state.cartnumber}})
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {mapGetters} from "vuex";
export default {
      name: "headers",
      mixins: [],
      data(){
        return {
          headerList:[],
          search:this.$route.query.title,
          userInfo:{},
          showCode: false,
          hotSearchList: [],
          dialogVisible:false,
          current: 1,
          account: "",
          password: "",
          captcha: "",
          keyCode: "",
          info:'',
          isShow:true,
          appidNum:'',
          hosts:'',
          codes:'',
          fromPath:'',
          disabled: false,
          svip_switch_status: 0,
          balance_func_status: 0
        }
      },
      computed: mapGetters(['openSettled']),
      watch:{
        $route: {
          handler: function(newVal, oldVal){
            this.search = newVal.query.title ? newVal.query.title : '';
          },
          // 深度观察监听
          deep: true
        }
      },
      head() {
        return {
          title: this.$store.state.titleCon
        }
      },
      beforeMount(){
        if(this.$auth.loggedIn){
          this.gainCount();
          this.getSvipData();
        }
        this.getHotSearchList();
       
      },
      mounted(){
        window.addEventListener('keydown',this.keyDown);
        this.hosts = location.origin + location.pathname;
        this.fromPath = this.$cookies.get("fromPath");
        if(this.codes){
          this.loginCode();
        }
      },
       destroyed(){
        window.removeEventListener('keydown',this.keyDown,false);
      },
      methods:{
        getHotSearchList(){
            this.$axios.get("/api/common/hot_keyword").then(res=>{
                this.hotSearchList = res.data;
            })
        },
        //获取入驻协议内容
        getSvipData() {
          let that = this;
          that.$axios.get("/api/config").then(res => {
           that.svip_switch_status = res.data.svip_switch_status
           that.balance_func_status = res.data.balance_func_status
           that.$store.commit('sysService', res.data.service_type);
          }).catch(err => {
            that.$message.error(err);
          });
        },
        AddFavorite() {
          let url = window.location;
          let title = document.title;
          let ua = navigator.userAgent.toLowerCase();
          if (ua.indexOf("360se") > -1) {
            this.$message("由于360浏览器功能限制，请按 Ctrl+D 手动收藏！");
          }
          else if (ua.indexOf("msie 8") > -1) {
            window.external.AddToFavoritesBar(url, title); //IE8
          }
          else if (document.all) {
            try{
              window.external.addFavorite(url, title);
            }catch(e){
              this.$message("您的浏览器不支持,请按 Ctrl+D 手动收藏!");
            }
          }
          else if (window.sidebar) {

            this.$message("您的浏览器不支持,请按 Ctrl+D 手动收藏!");
          }
          else {
            this.$message("您的浏览器不支持,请按 Ctrl+D 手动收藏!");
          }
        },
        gainCount: function() {
          let that = this;
          that.$axios.get('/api/user/cart/count').then(res=>{
            that.$store.commit('cartNum', res.data[0].count || 0);
          });
        },
        submit(){
          if(this.$route.path == '/goods_cate'){
            this.$router.push({path: '/goods_cate',query:{title:this.search ? this.search.trim() : ''}});
            // this.search = '';
          }else{
            this.$router.push({path: '/goods_search',query:{title:this.search ? this.search.trim() : ''}});
            // this.search = '';
          }
        },
        handleClose(){
          this.dialogVisible = false
        },
        showLogin(){
          this.$store.commit("isLogin", true);
        },
        keyDown(e){
          // this.submit()
        },
        goHome(){
          this.$router.push({path: '/'});
        },
        async loginCode(){
          let that = this;
          await that.$auth.loginWith('local3', {params:{ code: this.codes }}).then(()=>{
            that.isShow = false;
            if(this.fromPath){
              let path = this.fromPath.split(that.$router.history.base);
              let fromPath = path.join('');
              that.$router.push(fromPath);
            }else {
              that.$router.push('/');
            }
            that.$cookies.remove("fromPath");
          }).catch(err=>{
            // that.$layer.msg('登录失败');
          })
        },
        async code() {
          let that = this;
          if (!that.account) return that.$message.error('请填写手机号码');
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error('请输入正确的手机号码');
          await this.$axios.post("/api/auth/verify",{
            phone: that.account,
            type: 'mobile',
            key: that.keyCode
          }).then(res=>{
            that.$message.success(res.message);
            that.sendCode();
          }).catch(err => {
            that.$message.error(err);
          });
        },
      }
    }
</script>
<style scoped lang="scss">
.headerss{
  position: sticky;
  top: 0;
  z-index: 100;
}
.header{
  flex: 1;
  width: 100%;
  height: 40px;
  background-color: #282828;
  font-size: 12px;
  color: #B4B4B4;
  cursor: pointer;
  .gzhaocode{
    width: 100px;
    height: 122px;
    position: absolute;
    right: 0;
    top: 30px;
    display: none;
    .code{
      width: 100px;
      height: 140px;
      padding: 12px 5px 5px 5px;
      background: #fff;
      box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.08);
      text-align: center;
    }
    img{
      width: 90px;
      height: 90px;
    }
    span{
      display: inline-block;
      font-size: 9px;
      color: #666666;
    }
  }
  .headerCon{
    height: 100%;
    position: relative;
    width: 1200px;
    margin: 0 auto;
    a{
      color: #B4B4B4;
      &:hover{
        color: #fff;
      }
    }
    .iconfont{
      margin-right: 5px;
    }
    .user{
      .item{
        margin-right: 8px;
        position: relative;
        padding-left: 8px;
        color: #B4B4B4;
        &:hover{
          color: #fff;
        }
        &~.item:before{
          position: absolute;
          content: ' ';
          width: 1px;
          height: 14px;
          background-color: rgba(255, 255, 255, 0.11);
          left:0;
          top:50%;
          margin-top: -7px;
        }
        &.mobile-store{
          position: relative;
          &:hover{
            .gzhaocode{
              display: inline;
            }
          }
        }
        .pictrue{
          width: 22px;
          height: 22px;
          border-radius: 50%;
          margin-right: 8px;
          img{
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .king{
          width: 35px;
          height: 15px;
          display:block;
          margin-left: 6px;
          margin-top: 2px;
        }
      }
    }
  }
}
.nav{
  width: 100%;
  height: 140px;
  background: #FFFFFF;
  .cartNum{
    padding: 0 15px;
    height: 40px;
    border: 1px solid #DDDDDD;
    text-align: center;
    line-height: 40px;
    color: #E93323;
    cursor: pointer;
    &.on{
      color: #E93323;
      &:hover{
        opacity: 1;
      }
    }
    .iconfont{
      margin-right: 7px;
    }
  }
  .navCon{
    height:140px;
    padding-bottom: 30px;
    .textPic{
      height: 100%;
      .icon{
        cursor: pointer;
        width: auto;
        height: auto;
        img{
          width: auto;   
          height: auto;     /* 宽度自适应 */
          max-height: 70px;   /* 高度不超过容器 */
          object-fit: cover;  /* 填充方式：cover（覆盖）或 contain（包含） */ 
        }
      }
    }
    .search{
      width: 640px;
      height: 40px;
      border: 1px solid #E93323;
      border-radius: 2px;
      cursor: pointer;
      margin-left: 80px;
      position: relative;
      padding-right: 90px;
      .text{
        width: 548px;
        padding-left: 24px;
        color: #C1C1C1;
        position: relative;
        input{
          width: 548px;
          height: 38px;
          border:none;
          outline: none;
          padding-left: 10px;
        }
        .iconfont{
          font-size: 15px;
          margin-right: 5px;
          position: absolute;
          left: 14px;
          top: 12px;
        }
      }
      .bnt{
        width: 90px;
        height: 100%;
        text-align: center;
        line-height: 38px;
        color: #fff;
        position: absolute;
        right: -1px;
      }
      .search-hot{
          width: 560px;
          height: 21px;
          overflow: hidden;
          position: absolute;
          bottom: -35px;
          left: 0;
          span{
              display: inline-block;
              padding: 0 10px;
              color: #999999;
              &:hover{
                  color: #E93323;
              }
          }
      }
    }
  }
}
</style>
