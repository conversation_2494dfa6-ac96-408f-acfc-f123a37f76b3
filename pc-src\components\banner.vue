<template>
  <div>
    <div class="index_banner" :class="showBanner ? 'on' : ''">
      <img :src="banner"/>
      <div class="close_btn" @click="close">关闭<span class="iconfont icon-guanbi"></span></div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: "header_banner",
  mixins: [],
  data() {
    return {
      showBanner: false,
      id: 0,
      banner: ""
    }
  },
  head() {
    return {}
  },
  beforeMount() {

  },
  mounted() {
    this.getBanner();
  },
  methods: {
    close() {
      this.$cookies.set('top_banner' + this.id, 1);
      this.showBanner = false;
    },
    getBanner() {
      let that = this;
      that.$axios.get("/api/pc/home").then(res => {
        if (res.data.pc_top_banner && !this.$cookies.get('top_banner' + res.data.pc_top_banner.group_data_id)) {
          that.banner = res.data.pc_top_banner.image;
          that.id = res.data.pc_top_banner.group_data_id;
          this.showBanner = true;
        }else{
          this.showBanner = false;
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.index_banner {
  width: 100%;
  min-width: 1200px;
  position: relative;
  line-height: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  transform: translate3d(0, -100%, 0);
  position: fixed;
  top: 0;
  left: 0;

  &.on {
    transform: translate3d(0, 0, 0);
    position: static;

    .close_btn {
      display: block;
      cursor: pointer;
    }
  }
}

.index_banner img {
  width: 100%;
  height: auto;
  animation: all .3s ease;
}

.index_banner .close_btn {
  font-size: 16px;
  position: absolute;
  line-height: 20px;
  right: 35px;
  top: 35px;
  color: #fff;
  padding: 3px 10px;
  border: 1px solid #fff;
  background-color: rgba(0, 0, 0, .6);
  border-radius: 24px;
  display: none;

  .iconfont {
    font-size: 14px;
    margin-left: 6px;
    cursor: pointer;
  }
}
</style>
