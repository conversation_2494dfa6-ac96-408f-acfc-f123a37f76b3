# 服务包模式Bug修复总结

## 问题描述
1. **缓存问题**: 服务包配置无法缓存，点击下一步再返回上一步时，信息没有保存
2. **表单提交错误**: 提交时报错 "spec_type必须在 0,1范围内"

## 修复内容

### 1. 修复缓存问题

#### 父组件 (mer/src/views/product/addProduct/index.vue)
- **添加事件监听**: 为productSpecs组件添加了`@updatePackageConfig`和`@updateExtraServices`事件监听
- **添加数据属性**: 在data中添加了`packageConfig`和`extraServices`用于存储服务包配置缓存
- **添加处理方法**: 
  ```javascript
  updatePackageConfig(config) {
    this.packageConfig = { ...config };
  },
  updateExtraServices(services) {
    this.extraServices = { ...services };
  }
  ```
- **传递缓存数据**: 通过props将缓存的配置传递给子组件

#### 子组件 (mer/src/views/product/addProduct/components/productSpecs.vue)
- **添加props**: 添加了`cachedPackageConfig`和`cachedExtraServices`props接收父组件的缓存数据
- **添加恢复方法**: 
  ```javascript
  restoreFromParentCache() {
    if (this.cachedPackageConfig && Object.keys(this.cachedPackageConfig).length > 0) {
      this.packageConfig = { ...this.packageConfig, ...this.cachedPackageConfig };
      if (this.packageConfig.standard.enabled || this.packageConfig.premium.enabled) {
        this.showAdvancedPackages = true;
      }
    }
    if (this.cachedExtraServices && Object.keys(this.cachedExtraServices).length > 0) {
      this.extraServices = { ...this.extraServices, ...this.cachedExtraServices };
    }
  }
  ```
- **添加watch监听**: 监听父组件传入的缓存配置变化，自动恢复数据
- **修改mounted**: 在组件挂载时调用恢复方法

### 2. 修复spec_type验证问题

#### 父组件验证规则修复
将原来的简单验证：
```javascript
spec_type: [
  { required: true, message: "请选择商品规格", trigger: "change" }
]
```

修改为支持0、1、2三个值的自定义验证器：
```javascript
spec_type: [
  { 
    required: true, 
    message: "请选择商品规格", 
    trigger: "change",
    validator: (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('请选择商品规格'));
      } else if (![0, 1, 2].includes(value)) {
        callback(new Error('规格类型必须在 0,1,2 范围内'));
      } else {
        callback();
      }
    }
  }
]
```

## 修复效果

### 缓存功能
- ✅ 服务包配置现在可以正确缓存
- ✅ 切换tab后返回时配置信息得到保留
- ✅ 高级包的显示状态也会被正确恢复

### 表单验证
- ✅ spec_type现在接受0（单规格）、1（多规格）、2（服务包模式）三个值
- ✅ 表单提交不再报错"spec_type必须在 0,1范围内"

## 数据流程

1. **用户配置服务包** → 子组件触发`updatePackageConfig`/`updateExtraServices`事件
2. **父组件接收事件** → 更新本地缓存数据
3. **用户切换tab** → 配置数据保存在父组件中
4. **用户返回规格设置** → 子组件从父组件props接收缓存数据并恢复界面状态
5. **表单提交** → spec_type验证通过，支持服务包模式(值为2)

### 3. 修复表单处理逻辑

#### 提交和预览处理
- **handleSubmit方法**: 添加了对spec_type === 2的处理逻辑
- **handlePreview方法**: 修改条件判断支持服务包模式
- **watch监听**: 修改formValidate.attr的监听条件，支持服务包模式
- **created生命周期**: 修改watch设置条件，支持服务包模式

#### 具体修改
```javascript
// 原来只支持多规格(spec_type === 1)
if (this.formValidate.spec_type === 1) {
  // 处理逻辑
}

// 现在同时支持多规格和服务包模式
if (this.formValidate.spec_type === 1 || this.formValidate.spec_type === 2) {
  // 处理逻辑
}
```

## 测试建议

1. 测试缓存功能：
   - 配置服务包信息
   - 切换到其他tab
   - 返回规格设置tab
   - 验证配置是否保留

2. 测试表单提交：
   - 选择服务包模式
   - 配置完整的服务包信息
   - 提交表单
   - 验证不再报spec_type错误

3. 测试预览功能：
   - 配置服务包模式
   - 点击预览按钮
   - 验证预览功能正常工作

4. 测试数据持久化：
   - 配置服务包后保存
   - 重新编辑商品
   - 验证服务包配置正确加载
