<template>
  <div class="c_radio mb15" v-if="configData">
    <div class="c_row-item" :class="{on:configData.type=='ranges'}">
      <el-col class="c_label on" :span="configData.type=='ranges'?'':4">
        {{ $t(configData.title) }}
      </el-col>
      <el-col class="color-box" :span="configData.type=='ranges'?24:19">
        <div>
          <el-radio-group v-model="configData.tabVal" @on-change="radioChange($event)">
            <el-radio :label="key" v-for="(radio,key) in configData.tabList" :key="key">
              <span>{{radio.name}}</span>
            </el-radio>
          </el-radio-group>
        </div>
        <div>
          <el-radio-group v-model="configData.tabData" @on-change="radioDataChange($event)" v-if="configData.tabVal==0">
            <el-radio :label="key" v-for="(radio,key) in configData.dataList" :key="key+'data'">
              <span>{{radio.name}}</span>
            </el-radio>
          </el-radio-group>
        </div>
        <el-date-picker type="date" v-model="configData.specifyDate" value-format="yyyy/MM/dd" format="yyyy/MM/dd" :placeholder="$t('请选择')" size="small" style="margin-top: 6px;" v-if="configData.tabData==1 && configData.tabVal==0 && configData.type == 'data'" />
        <el-time-picker type="datetime" value-format="HH:mm" format="HH:mm" v-model="configData.specifyDate" :placeholder="$t('请选择')" size="small" style="margin-top: 6px;" v-else-if="configData.tabData==1 && configData.tabVal==0 && configData.type == 'time'" />
        <el-date-picker type="daterange" placement="bottom-end" v-model="configData.specifyDate" value-format="yyyy/MM/dd" format="yyyy/MM/dd" :placeholder="$t('请选择')" size="small" style="margin-top: 6px;" v-else-if="configData.tabData==1 && configData.tabVal==0 && configData.type == 'daterange'" @change="getDaterange"/>
        <el-time-picker is-range value-format="HH:mm" format="HH:mm" type="datetimerange" v-model="configData.specifyDate" placement="bottom-end" :placeholder="$t('请选择')" size="small" style="margin-top: 6px;" v-else-if="configData.tabData==1 && configData.tabVal==0 && configData.type == 'timerange'" />  
      </el-col>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: 'c_comb_data',
  props: {
    configObj: {
      type: Object
    },
    configNme: {
      type: String
    }
  },
  data () {
    return {
      defaults: {},
      configData: {}
    }
  },
  created () {
    this.defaults = this.configObj
    this.configData = this.configObj[this.configNme]
  },
  watch: {
    configObj: {
      handler (nVal, oVal) {
        this.defaults = nVal
        this.configData = nVal[this.configNme]
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getDaterange(e){
      this.$emit('getConfig', {type:2,val:e})
    },
    radioChange (e) {
      this.$emit('getConfig', {type:0,val:e})
    },
    radioDataChange(e) {
      this.$emit('getConfig', {type:1,val:e})
    }
  }
}
</script>

<style scoped lang="scss">
.c_radio{
  .c_row-item{
    align-items: unset;
    &.on{
      display: block;
      .c_label{
        text-align: left;
        margin-bottom: 3px;
      }
    }
  }
  .c_label{
    color: #000;
    margin-right: 15px;
    margin-top: 4px;
    &.on{
      text-align: right;
      color: #666;
    }
  }
  
}
::v-deep .el-radio{
  margin: 5px 25px 5px 0;
}
::v-deep .el-date-editor{
  width: 100%;
}
</style>