<template>
    <div class="box" v-if="configData">
		<WangEditor
		  :content="configData.val"
		  @editorContent="getEditorContent"
		  style="width: 100%; height: 60%"
		></WangEditor>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import WangEditor from "@/components/wangEditor/index.vue";
export default {
    name: 'c_page_ueditor',
    props: {
        configObj: {
            type: Object
        },
        configNme: {
            type: String
        }
    },
    components: { WangEditor },
    data () {
        return {
            description: '',
            defaults: {},
            configData:{}
        }
    },
    created () {
        this.defaults = this.configObj
        this.configData = this.configObj[this.configNme]
    },
    watch: {
        configObj: {
            handler (nVal, oVal) {
                this.defaults = nVal
                this.configData = nVal[this.configNme]
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        getEditorContent(data) {
            this.configData.val = data;
        },
        // 添加自定义弹窗
        addCustomDialog (editorId) {
            window.UE.registerUI('test-dialog', function (editor, uiName) {
                // 创建 dialog
                let dialog = new window.UE.ui.Dialog({
                    iframeUrl: '/admin/widget.images/index.html?fodder=dialog',
                    editor: editor,
                    name: uiName,
                    title: this.$t(this.$t('上传图片')),
                    cssRules: 'width:1200px;height:500px;padding:20px;'
                });
                this.dialog = dialog;
                let btn = new window.UE.ui.Button({
                    name: 'dialog-button',
                    title: this.$t(this.$t('上传图片')),
                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,
                    onclick: function () {
                        // 渲染dialog
                        dialog.render();
                        dialog.open();
                    }
                });
                return btn;
            }, 37);
            window.UE.registerUI('video-dialog', function (editor, uiName) {
                let dialog = new window.UE.ui.Dialog({
                    iframeUrl: '/admin/widget.video/index.html?fodder=video',
                    editor: editor,
                    name: uiName,
                    title: this.$t(this.$t('上传视频')),
                    cssRules: 'width:1000px;height:500px;padding:20px;'
                });
                this.dialog = dialog;
                let btn = new window.UE.ui.Button({
                    name: 'video-button',
                    title: this.$t(this.$t('上传视频')),
                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -320px -20px;`,
                    onclick: function () {
                        // 渲染dialog
                        dialog.render();
                        dialog.open();
                    }
                });
                return btn;
            }, 38);
        }
    }
}
</script>

<style scoped>

</style>
