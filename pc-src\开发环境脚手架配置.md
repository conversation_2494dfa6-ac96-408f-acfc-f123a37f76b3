# 开发环境脚手架配置

## 配置目的
- 开发时连接远程服务器 `https://leaveukey.com`
- 生产构建时保持原始配置 `https://mer1.crmeb.net`

## 修改的文件

### 1. nuxt.config.js
```javascript
// 第11-13行，原来的：
const VUE_APP_API_URL = 'https://mer1.crmeb.net'

// 修改为：
// 开发环境读取 .env.development，生产环境使用默认值
const VUE_APP_API_URL = process.env.VUE_APP_API_URL || 'https://mer1.crmeb.net'
const VUE_APP_WS_URL = process.env.VUE_APP_WS_URL || 'wss://mer1.crmeb.net/ws'
```

```javascript
// 第22-24行，原来的：
env: {
  BASE_URL: VUE_APP_API_URL,
},

// 修改为：
env: {
  BASE_URL: VUE_APP_API_URL,
  WS_URL: VUE_APP_WS_URL,
},
```

### 2. components/ChatRoom.vue
```javascript
// 第311行，原来的：
let ws = process.env.BASE_URL || location.origin;

// 修改为：
let ws = process.env.WS_URL || process.env.BASE_URL || location.origin;
```

## 需要创建的文件

### .env.development
```bash
VUE_APP_API_URL=https://leaveukey.com
VUE_APP_WS_URL=wss://leaveukey.com/ws
```

## 使用方法

### 开发环境
```bash
npm run dev
```
- 自动连接到 `https://leaveukey.com`
- WebSocket 连接到 `wss://leaveukey.com/ws`

### 生产构建
```bash
npm run build
# 或
npm run generate
```
- 忽略 `.env.development`
- 使用默认配置 `https://mer1.crmeb.net`

## 工作原理
- **开发环境**: 读取 `.env.development` 环境变量
- **生产环境**: 使用 `||` 后的默认值，确保不受环境变量影响 