<template>
    <div class="goods_cate">
      <category-List ref="cateGory"></category-List>
      <div class="title wrapper_1200">
        <nuxt-link class="home" to="/">首页  </nuxt-link>
        <span v-for="(item, index) in titleName" class="cate_name" @click="goCate(item,index)"> > {{ item.split('_')[0] }}</span> <span v-if="searchVal"> > {{searchVal}} </span>
         <div v-if="is_multiple && search" class="search-count acea-row"><span class="search-title">品牌：</span> <span class="search-selected">{{search}}</span>
           <span class="el-icon-close" @click="handleClose"></span>
         </div>
      </div>
      <div class="search-store acea-row" v-if="storeList.length > 0">
            <ul class="store-count" :class="storeList.length >= 2 ? 'moreStore' : ''">
                <li class="item acea-row" v-for="(item, index) in storeList" :key="index">
                    <div class="store-name acea-row">
                        <div class="image">
                            <img :src="item.mer_avatar" alt="">
                        </div>
                        <div class="name">
                            {{ item.mer_name }}
                            <span>{{ item.care_count < 10000 ? item.care_count : (item.care_count/10000).toFixed(1)+'万'  || 0 }}人关注</span>
                        </div>
                    </div>
                    <div class="store-go acea-row">
                        <button class="store-btn goIn" @click="goStore(item.mer_id)">进店逛逛</button>
                      <!--<button v-if="item.isCollect" class="store-btn collect hasCollect">已收藏</button>
                          <button v-else class="store-btn collect">未收藏</button>-->
                    </div>
                </li>
            </ul>
            <nuxt-link v-if="storeLength" :to="{path:'/shop_street',query:{title:searchVal}}" class="moreBtn">
              更<br/>多<br/>店<br/>铺
              <span class="iconfont icon-you"></span>
            </nuxt-link>
      </div>
      <div class="navCount" :class="is_showMore ? 'showMore' : ''">
        <div class="navCon wrapper_1200 acea-row">
          <div class="list-count acea-row">
            <div class="name">品牌：</div>
            <div class="lists acea-row row-middle">
              <div class="items" :class="item.checked?'font-color':''" v-for="(item, index) in brandList" :key="index" @click="getSearchData(item,index,brandList)">{{item.brand_name}}</div>
            </div>
          </div>
          <div class="moreCon">
            <div class="more" @click="is_showMore=!is_showMore">
              更多
                <i v-if="is_showMore" class="iconfont icon-xiangshang"></i>
                <i v-else class="iconfont icon-xiangxia"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="navCount" :class="showMore ? 'showMore' : ''">
        <div class="navCon wrapper_1200 acea-row">
          <div class="list-count acea-row">
            <div class="name">店铺类型：</div>
            <div class="lists acea-row row-middle">
              <div class="items" :class="item.checked?'font-color':''" v-for="(item, index) in typeList" :key="index" @click="getSearchData(item,index,typeList)">{{item.type_name}}</div>
            </div>
          </div>
          <div class="moreCon">
            <div class="more" @click="showMore=!showMore">
              更多
                <i v-if="showMore" class="iconfont icon-xiangshang"></i>
                <i v-else class="iconfont icon-xiangxia"></i>
            </div>
          </div>
        </div>
      </div>
      <div v-for="(item, index) in parmasList" :key="index">
        <div class="navCount" :class="item.is_showMore ? 'showMore' : ''">
          <div class="navCon wrapper_1200 acea-row">
            <div class="list-count acea-row">
              <div class="name">{{item.name}}:</div>
              <div class="lists acea-row row-middle">
                <div class="items" :class="itm.checked?'font-color':''" v-for="(itm, idx) in item.value" :key="idx" @click="getParmasSearch(item,itm,idx)">{{itm.value}}</div>
              </div>
            </div>
            
            <div class="moreCon">
              <div class="more" @click="item.is_showMore=!item.is_showMore">
                更多
                <i v-if="item.is_showMore" class="iconfont icon-xiangshang"></i>
                <i v-else class="iconfont icon-xiangxia"></i>
              </div>
            </div>
          </div>
         </div>
      </div>
      <div class="wrapper sort-count">
        <div class="sort acea-row">
          <div class="acea-row">
            <div class="name">排序：</div>
            <div class="acea-row">
              <div class="item" :class="iSdefaults === 0?'font-color':''" @click="defaults">默认</div>
              <div class="item" :class="iSdefaults === 1?'font-color':''" @click="salesSort('sales')">
                销量
              </div>
              <div class="item" :class="iSdefaults === 3?'font-color':''" @click="salesRate('rate')">
                评分
              </div>
              <div class="item" :class="iSdefaults === 2?'font-color':''" @click="priceSort('price_desc')" v-if="priceOrder === 'price_asc'">
                价格
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-jiageshaixuanshang"></use>
                </svg>
              </div>
              <div class="item" :class="iSdefaults === 2?'font-color':''" @click="priceSort('price_asc')" v-else-if="priceOrder === ''">
                价格
                <span class="iconfont icon-jiageshaixuan"></span>
              </div>
              <div class="item" :class="iSdefaults === 2?'font-color':''" @click="priceSort('price_asc')" v-else>
                价格
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-jiageshaixuanxia"></use>
                </svg>
              </div>
              <div class="item price-range">
                  <div class="price-count">
                      <el-input class="input" v-model.number="price_on" @input="change($event)" @keydown.native="btKeyDown" @keyup.native="btKeyUp"></el-input> -
                      <el-input class="input" v-model.number="price_off" @input="change($event)" @keydown.native="btKeyDown" @keyup.native="btKeyUp"></el-input>
                      <!-- @input="change($event)" -->
                  </div>
                  <!-- <div class="action-box">
                      <span class="action-btn clear" @click="clearPrice">清空</span>
                      <span class="action-btn submit" @click="getProductslist('')">确定</span>
                  </div> -->
              </div>
            </div>
          </div>
          <div class="searchBtn">
            <div class="cancel" @click="resetParmas">取消</div>
            <div class="confirm" @click="goSearch">确定</div>
          </div>
        </div>
      </div>
      <div class="wrapper_1200">
        <div v-if="productslist.length">
          <div class="goods acea-row row-middle">
            <div class="item" v-for="(item, index) in productslist" :key="index" @click="goDetail(item)">
              <div class="pictrue">
                <img :src="item.image">
                <div v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></div>
              </div>
              <div class="money acea-row row-between-wrapper">
                <div v-if="item.show_svip_info&&item.show_svip_info.show_svip_price && item.svip_price" class="svip acea-row">
                <span class="font-color">¥{{item.svip_price}}</span>
                <img src="@/assets/images/svip.png">
                </div>
                <div v-else><span class="font-color">¥{{item.price}}</span></div>
                <div class="label font-color" v-if="item.issetCoupon">券</div>
              </div>
             
              <div class="name line2"> <span v-if="item.merchant.is_trader && item.product_type == 0" class="trader">自营</span><span v-else-if="item.product_type == 1" class="trader">秒杀</span> {{item.store_name}}</div>
              <div class="bottom acea-row row-between-wrapper">
                <div>{{item.sales}}人付款</div>
                <div>{{item.rate}}分</div>
              </div>
            </div>
          </div>
          <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
        </div>
      </div>
      <div v-if="!productslist.length">
        <div class="noGoods">
          <div class="pictrue">
            <img src="../assets/images/noGoods.png">
          </div>
          <div class="name">亲，该分类暂无商品哟~</div>
        </div>
      </div>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import categoryList from "@/components/categoryList";
export default {
      name: "goods_cate",
      auth: false,
      components: {
        categoryList
      },
      data(){
        return {
          isCollect: false,
          storeList: [],
          brandList:[],
          brandCurrent:[],
          current:0,
          moreCurrent:0,
          seen:false,
          titleName:'',
          erCurrent:0,
          iSdefaults:0,
          productslist:[],
          pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
          page: 1, //代表页面的初始页数
          limit:15,
          scollY: null,// 离底部距离有多少
          total: 0, //总页数
          title:'下拉加载更多',
          cid:0,//一级分类
          sid:'',//二级分类
          order: '',
          priceOrder: '',
          price_on: '',
          price_off: '',
          news:0,
          is_multiple: false,
          checkedBrands: [],
          is_showMore: false,
          showMore: false,
          searchVal: '',
          search: '',
          storeLength: false,
          parmasList: [],
          typeList: []
        }
      },
      async asyncData({app,query}){
        return {
          keyword: query.title ? query.title : '',
          sid: query.sid ? query.sid:'',
          titleName: query.name ? decodeURI(query.name).split(',') : '',
        }
      },
      watch:{
        $route: {
          handler: function(newVal, oldVal){
            if(newVal){
              this.keyword = newVal.query.title ? newVal.query.title : '';
              this.searchVal = newVal.query.title ? newVal.query.title : '';
              this.sid = newVal.query.sid ? newVal.query.sid : '';
              this.titleName = newVal.query.name ? decodeURI(newVal.query.name).split(',') : ''; 
            }
            this.page = 1;
            this.getStoreList();
            this.getProductslist(1);
            this.getBrandList();
            this.getParmasList();
          },
          // 深度观察监听
          deep: true
        },
        is_multiple(n){
          this.checkedBrands = [];
          if(!n) {
            this.current = 0;
          }
        },
      },
      fetch({ store}) {
        store.commit("isBanner", false);
        store.commit('isHeader', true);
        store.commit('isFooter', true);
      },
      head() {
        return {
          title: "商品分类-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
        this.searchVal = this.$route.query.title;
        this.sid = this.$route.query.sid || '';
        this.getBrandList();
        this.getParmasList();
        this.getTypeList();
        this.getProductslist('');
        this.getStoreList();
      },
      mounted(){},
      beforeDestroy() {},
      methods:{
        handleClose(){
          this.checkedBrands = [];
          this.searchVal = this.search = "";
          this.toggleMultiple();
          this.getBrandList();
          this.getParmasList();
          this.getTypeList();
          this.getProductslist(1);
          this.getStoreList();
        },     
        toggleMultiple(){
          this.is_multiple = !this.is_multiple
          if(!this.is_multiple) this.search = ''
        },
        clearPrice(){
          this.price_on = this.price_off = "";
        },
        change(e){
          this.$forceUpdate(e) 
        },
        btKeyDown(e) {
          e.target.value = e.target.value.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g,"");
        },
        btKeyUp(e) {
          e.target.value = e.target.value.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g,"")
        },
        /**获取品牌*/
        getBrandList(){
          let _this = this;
          _this.$axios.get('/api/store/product/brand/lst',{
            params:{
              limit: 999,
              keyword: _this.keyword,
              pid: _this.sid
            }
          }).then(function (res) {
            res.data.list.forEach((item, index) => {
              item.checked = false
            })
            res.data.list.unshift({
              'brand_id': '',
              'brand_name': '全部',
              'checked': true
            })
            _this.brandList = res.data.list
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        /**获取参数列表 */
        getParmasList(){
          let _this = this;
          _this.$axios.get('/api/product/spu/params',{
            params:{keyword: _this.keyword, cate_id: _this.sid, is_pc: 1}
          }).then(function (res) {
            if(res.data.length>0){
              res.data.forEach((item, index) => {
               item.is_showMore = false
               item.value.forEach((itm, idx) => {
                itm.checked = false
              })
              item.value.unshift({
                'name': item.name,
                'parameter_id': '',
                'value': '全部',
                'checked': true
              }) 
             });
              _this.parmasList = res.data
            }else{
              _this.parmasList =[]
            }
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        /**获取店铺类型列表 */
        getTypeList(){
          let _this = this;
          _this.$axios.get('api/intention/type').then(function (res) {
            res.data.forEach((item, index) => {
              item.checked = false
            })
            res.data.unshift({
              'mer_type_id': '',
              'type_name': '全部',
              'checked': true
            })
            _this.typeList = res.data
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
         /**获取参数筛选条件 */
        getParmasSearch(item,itm,idx){
          if(idx == 0){
            if(itm.checked){
              return
            }else{
              item.value.forEach((el, i) => {
                el.checked = false
              })
              itm.checked = !itm.checked
            }
          }else{
            itm.checked = !itm.checked
            item['value'][0]['checked']=this.isAllSelected(item.value)  
          }  
          this.goSearch()
        },
        /**判断参数值是否全选 */
        isAllSelected(arr){
          let isAll = true;
          for (let i = 0; i < arr.length; i++) {
            if (arr[i]['checked'] === true) {
              isAll = false
              break;
            }
          }
          return isAll
        },
        /**获取筛选条件 */
        getSearchData(item,index,list){
          if(index == 0){
            if(item.checked){
              return
            }else{
              list.forEach((el, i) => {
                el.checked = false
              })
              item.checked = !item.checked
            }
          }else{
            item.checked = !item.checked
            list[0]['checked']=this.isAllSelected(list)  
          }  
          this.goSearch()
        },
        resetParmas(){
          this.iSdefaults = 0
          this.priceOrder = ""
          this.brandList.forEach((el, i) => {
            el.checked = false
          })
          this.typeList.forEach((el, i) => {
            el.checked = false
          })
          if(this.brandList.length>0)this.brandList[0]['checked'] = true
          if(this.typeList.length>0)this.typeList[0]['checked'] = true
          this.parmasList.forEach((item, index) => {
            item.value.forEach((el, i) => {
              el.checked = false
            })
            if(item['value'].length>0)item['value'][0]['checked']=true
          })
          this.getProductslist(1);
        },
        goCate(item, index) {
          if((index + 1) === this.titleName.length){
            return ;
          }
          const name = this.titleName.reduce((initial, val, idx) => {
            if (idx > index) {
              return initial;
            } else {
              initial.push(val);
              return initial;
            }
          }, []);
          this.$router.push({
            path: `/goods_cate`,
            query: {
              sid: item.split('_')[1],
              name: encodeURI(name.join(',')),
              // name: encodeURI(name.substring(0,name.length-1))
            }
          })
        },
        /**取出多选选中的项 */
        getSelectedItem(){
          if(this.checkedBrands.length > 0){
            this.search = '';
            for(var i=0;i<this.checkedBrands.length;i++){
              for(var j=0;j<this.brandList.length;j++){
                if(this.checkedBrands[i] == this.brandList[j]['brand_id']){
                  this.search += this.brandList[j]['brand_name']+',';
                  this.search =  this.subStr(this.search,10)
                }
              }
            }
            this.search = this.search.substr(0, this.search.length - 1);
          }
        },
        /**超出省略号代替 */
        subStr(str,max){
          if(str.length > max){
            str = str.substring(0,max) + '...';
          }
          return str;
        },
        goDetail: function (item) {
          if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
          }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
          }
        },
        goSearch: function() {
          this.productslist = [];
          this.page = 1;
          this.getStoreList();
          this.getProductslist(1);
        },
        /**进店逛逛 */
        goStore: function(id){
          this.$router.push({ path: `/store?id=${id}` });
        },
        /**店铺列表 */
        getStoreList(){
          let _this = this;
          let checkedType = _this.getCheckedType(_this.typeList,'mer_type_id');
          _this.$axios.get('/api/store/merchant/lst',{
            params:{
              keyword: _this.keyword || '',
              type_id: checkedType.length>0 ? checkedType.toString() : '',
              page: 1,
              limit: 3
            }
          }).then(function (res) {
            _this.storeList = res.data.list;
            _this.storeLength = res.data.list.length > 2 ? true : false;
            if(_this.storeLength)_this.storeList.length = 2;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        getProductslist(num){
          let _this = this;
          let parmas = {},checkedBrands=_this.getCheckedType(_this.brandList,'brand_id'),checkedType=_this.getCheckedType(_this.typeList,'mer_type_id')
          _this.parmasList.forEach((item,index)=>{
            parmas[item.parameter_id]=[]
            if(!_this.isAllSelected(item.value)){
              item.value.forEach((el,idx)=>{
                if(el.checked && idx!=0){
                 parmas[item.parameter_id].push(el.value)
                }
              })
            }
				  })
         _this.brandList.forEach((item,index)=>{
          if(item.checked){
           checkedBrands.push(item.brand_id)
          }
				})
          checkedBrands = checkedBrands.length>0 ? checkedBrands.toString() : ''
          checkedType = checkedType.length>0 ? checkedType.toString() : ''
          _this.page = num ? num : _this.page;
          let currentPage = {page: _this.page,limit:_this.limit,keyword: _this.keyword,brand_id:checkedBrands,mer_type_id:checkedType,
          filter_params: parmas.length > 0 ? parmas.toString() : '',price_on: _this.price_on,price_off: _this.price_off,order: _this.order,cate_pid: _this.sid,common: 1};
          _this.$axios.get('/api/product/spu/lst', {
            params: currentPage
          }).then(function (res) {
            _this.total = res.data.count;
            _this.productslist = res.data.list;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        getCheckedType(list,id){
          let checkedArr = []
          list.forEach((item,index)=>{
            if(index>0&&item.checked){
              checkedArr.push(item[id])
            }
          })
          return checkedArr
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getProductslist('');
        },
        category(index){
          this.checkedBrands = [];
          this.current = index;
          this.moreCurrent = index;
          this.brandCurrent = this.brandList[index].children;
          this.productslist = [];
          this.page = 1;
          this.erCurrent = 0;
          this.order = '';
          this.checkedBrands[0] = this.brandList[index]['brand_id'];
          this.getProductslist('');
        },
        defaults(){
          this.iSdefaults = 0;
          this.productslist = [];
          this.page = 1;
          this.order = '';
          this.priceOrder = '';
          this.getProductslist('');
        },
        priceSort(sort){
          this.iSdefaults = 2;
          this.productslist = [];
          this.page = 1;
          this.order = sort;
          this.priceOrder = sort;
          this.getProductslist('');
        },
        salesSort(sort){
          this.iSdefaults = 1;
          this.productslist = [];
          this.page = 1;
          this.order = sort;
          this.priceOrder = '';
          this.getProductslist('');
        },
        salesRate(rate){
          this.iSdefaults = 3;
          this.productslist = [];
          this.page = 1;
          this.order = rate;
          this.priceOrder = '';
          this.getProductslist('');
        }
      }
    }
</script>

<style scoped lang="scss">
  .trader{
    color: #fff;
    background-color: #e93323;
    display: inline-block;
    width: 32px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 2px;
    margin-right: 5px;
    font-size: 12px;
  }
  .cate_name{
    cursor: pointer;
    &.on{
      color: #E93323
    }
  }
  .goods_cate{
    margin-top: 2px;
    .noGoods{
      text-align: center;
      .pictrue{
        width: 274px;
        height: 174px;
        margin: 130px auto 0 auto;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 14px;
        color: #969696;
        margin-top: 20px;
        margin-bottom: 290px;
      }
    }
    .navCount{
      width: 100%;
      .lists{
        height: 50px;
        width: 900px;
        overflow: hidden;
        .items{
          line-height: 50px;
          overflow: hidden;
          margin-right: 40px;
          &:hover{
            color: #E93323;
          }
        }
      }
      &.showMore{
        height: auto;
        .lists{
          height: auto;
        }      
      }
      cursor: pointer;
      .navCon{
        padding: 0 17px;
        position: relative;
        &:after{
          content: '';
          display: inline-block;
          position: absolute;
          width: 1140px;
          border-bottom: 1px dotted #EFEFEF;
          bottom: 0;
          left: 60px;
        }
        &.wrapper_1200{
          overflow: unset!important;
          background-color: #fff;
        }
        .list-count{
          width: 1120px;
        }
        .name{
          color: #969696;
          margin-right: 10px;
          line-height: 50px;
          width: 70px;
        }
        .moreCon{
          float: right;
          height: 50px;
          .moreCategory{
            padding: 44px 44px 16px 44px;
            position: absolute;
            top:50px;
            right: -17px;
            width: 1200px;
            background-color: #fff;
            box-shadow: 0 3px 16px rgba(0, 0, 0, 0.06);
            .item{
              margin: 0 40px 28px 0;
              &:hover{
                color: #E93323;
              }
            }
          }
          .multiBtn{
            display: inline-block;
            width: 38px;
            height: 18px;
            line-height: 18px;
            border: 1px solid #C8C8C8;
            text-align: center;
            border-radius: 2px;
            margin-right: 18px;
          }
        }   
        .more{
          position: relative;
          line-height: 50px;
          text-align: center;
          font-size: 14px;
          .iconfont{
            font-size: 10px;
            color: #666666;
            display: inline-block;
            transition: all .1s ease;
            &.shouqi{
              -moz-transform: rotateX(180deg);
              -o-transform: rotateX(180deg);
              -ms-transform: rotateX(180deg);
              transform: rotateX(180deg);
            }
          }
        }
      }
    }
    .el-checkbox{
      height: 50px;
      line-height: 50px;
      color: #282828;
    }
    .title{
      color: #999999;
      height: 46px;
      line-height: 46px;
      .home{
        color: #282828;
      }
    }
    .sort-count{
      width: 1200px;
      margin: 0 auto;
    }
    .wrapper{
      background-color: #fff;
      padding: 20px 17px 10px;
      cursor: pointer;
      .list{
        width: 1100px;
        border-bottom: 1px dotted #EFEFEF;
        padding-bottom: 10px;
        .item{
          margin-right: 30px;
          margin-bottom: 10px;
          &:hover{
            color: #E93323;
          }
        }
      }
      .sort{
        justify-content: space-between;
        .item{
          margin-right: 30px;
          &:hover{
            color: #E93323;
          }
          .icon{
            font-size: 15px;
            margin-left: 5px;
          }
          .iconfont{
            font-size: 15px;
            color: #E2E2E2;
            margin-left: 5px;
          }
        }
        .name{
          color: #969696;
          margin-right: 10px;
          width: 70px;
        }
      }
    }
    .goods{
      width: 1220px;
      .item{
        background-color: #fff;
        padding: 16px;
        width: 224px;
        height: 340px;
        margin: 20px 20px 0 0;
        cursor: pointer;
        &:nth-child(5n){
          margin-right: 0;
        }
        &:hover{
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
        .pictrue{
          width: 192px;
          height: 192px;
          position: relative;
          .border-picture {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: center/cover no-repeat;
          }
          img{
            width: 100%;
            height: 100%;
          }
        }
        .svip{
          align-items: center;
          margin-top: 3px;
          img{
            width: 35px;
            height: 15px;
            margin: 4px 0 0 7px;
          }
        }
        .money{
          margin-top: 12px;
          .font-color{
            font-weight: bold;
            font-size: 22px;
          }
          .y_money{
            font-size: 12px;
            color: #AAAAAA;
            text-decoration: line-through;
            margin-left: 8px;
          }
          .label{
            width: 20px;
            height: 20px;
            background: linear-gradient(330deg, rgba(231, 85, 67, 0.15) 0%, rgba(244, 103, 83, 0.15) 100%);
            font-size: 12px;
            text-align: center;
            line-height: 20px;
          }
        }
        .name{
          color: #5A5A5A;
          margin-top: 8px;
          height: 40px;
        }
        .bottom{
          font-size: 12px;
          color: #AAAAAA;
          margin-top: 10px;
        }
      }
    }
    .searchBtn{
      display: flex;
      >div{
        display: flex;
        width: 60px;
        height: 30px;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
      }
      .cancel{
        margin-right: 14px;
        border: 1px solid #D4D4D4;
        background: #ffffff;
        color: #666666;
      }
      .confirm{
        color: #ffffff;
        background: #E93323;
        border-color: #E93323;
      }
    }
    .price-range{
      position: relative;
      top: -4px;
      .action-box{
        display: none;
        position: absolute;
        left: 0;
        bottom: -50px;
        text-align: center;
        background: #F9F9F9;
        height: 50px;
        line-height: 50px;
        width: 100%;
        box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.08);
        padding: 0 15px;
        overflow: hidden;
        .clear{
          float: left;
          color: #999999;
        }
        .submit{
          float: right;
          color: #666666;
          width: 60px;
          height: 30px;
          line-height: 30px;
          margin-top: 10px;
          background-color: #F1F1F1;
          border-radius: 2px;
          border: 1px solid #D4D4D4;
        }
      }
      .price-count{
        .input{
          width: 80px;
          height: 32px;
          position: relative;
            &:before{
              content: '¥';
              display: inline-block;
              color: #D4D4D4;
              position: absolute;
              left: 5px;
              top: 0;
              line-height: 32px;
            }
        }
      }
    }
    .search-count{
      display: inline-block;
      background-color: #fff;
      height: 26px;
      line-height: 26px;
      color: #282828;
      padding: 0 10px;
      margin-bottom: 15px;
      .search-selected{
        display: inline-block;
        min-width: 100px;
        max-width: 200px;
      }
      .el-icon-close{
        cursor: pointer;
        color: #969696;
        font-size: 12px;
      }
    }
  }
  .price-count .input ::v-deep .el-input__inner{
    height: 32px;
    line-height: 32px;
    padding-left: 18px;
  }
  .search-count .search-selected ::v-deep .el-input__inner{
    border: none;
    height: 26px;
    line-height: 26px;
    color: #969696;
  }
  .search-store{
      width: 1200px;
      margin: 0 auto 20px;
      border-radius: 4px;
      overflow: hidden;
      .moreBtn{
        width: 32px;
        height: 76px;
        padding: 10px 0;
        background-color: #fff;
        color: #999999;
        text-align: center;
        font-size: 12px;
        float: left;
            .iconfont{
                display: block;
                font-size: 8px;
                border: 1px solid #8B8B8B;
                border-radius: 100%;
                width: 11px;
                height: 11px;
                line-height: 11px;
                text-align: center;
                margin: 3px auto 0;
            }
        }
      .store-count{
          overflow: hidden;
          width: 100%;
          .item{
              height: 96px;
              width: 100%;
              -webkit-justify-content: space-between;
              justify-content: space-between;
              padding: 0 17px;
              background-color: #fff;
          }
          &.moreStore{
               float: left;
               width: auto;
              .item{
                width: 570px;
                float: left;
                margin-right: 14px;
              }
          }
          .store-name{
              align-items: center;
              .image{
                width: 61px;
                height: 61px;
                border-radius: 100%;
              img{
                width: 100%;
                height: 100%;
              }
            }
            .name{
                margin-left: 15px;
                color: #282828;
                line-height: 26px;
                >span{
                    display: block;
                    color: #999999;
                }
            }
          }
          .store-go{
              align-items: center;
              .store-btn{
                  width: 78px;
                  height: 30px;
                  line-height: 30px;
                  text-align: center;
                  border-radius: 4px;
                  font-size: 12px;
                  &.goIn{
                      color: #fff;
                      background-color: #E93323;
                      margin-right: 12px;
                      border: 1px solid #E93323;
                  }
                  &.collect{
                     color: #E93323;
                     border: 1px solid #E93323;
                     background-color: #fff;
                  }
                  &.hasCollect{
                      color: #999999;
                      border-color: #999999;
                  }
              }
          }

      }
  }
  .select-footer{
      text-align: center;
      margin: 30px 0;
  }
</style>
