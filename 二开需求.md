# CRMEB系统Fiverr风格服务包选择功能二开需求

## 项目概述

本次二开旨在为现有CRMEB电商系统增加类似Fiverr平台的服务包选择功能，实现多层次套餐展示（Basic/Standard/Premium）及额外服务选项，在不改变数据库结构的前提下，巧妙利用现有商品规格系统来实现新功能。

## 修改范围明确

- **后台管理（mer项目）**：仅修改商品列表→添加商品→规格设置页面
- **前端商城（pc-src项目）**：仅修改商品详情页的规格展示区域
- **涉及文件**：
  - `mer/src/views/product/addProduct/components/productSpecs.vue`
  - `pc-src/pages/goods_detail/_id/index.vue`

## 核心功能需求

### 1. 服务包配置功能（后台管理）

#### 1.1 修改范围说明
- **修改页面**：商品列表 → 添加商品 → 规格设置 页面（如用户提供图片所示）
- **对应文件**：`mer/src/views/product/addProduct/components/productSpecs.vue`
- **修改位置**：规格设置tab页内的规格配置区域
- **注意**：仅修改此一个页面，不涉及其他商品管理页面

#### 1.2 初始表单界面
- **参考**：Fiverr图1的初始化表单
- **功能**：
  - 在现有"规格类型"基础上，新增"服务包模式"选项
  - 默认显示单个基础包配置
  - 包含包名称、描述、交付时间、价格等基础字段
  - 右侧显示"Create Packages"按钮

#### 1.3 三层套餐扩展
- **参考**：Fiverr图2的三列套餐展示
- **实现位置**：在现有规格列表区域下方扩展
- **实现方式**：
  - 选择"服务包模式"后，点击"Create Packages"展开为三列：Basic、Standard、Premium
  - 每列包含独立的配置项：
    - 包名称（Name your package）
    - 包描述（Describe the details）
    - 交付时间（Delivery Time）
    - 修改次数（Revisions）
    - 价格（Price）
  - 保持与现有Element UI风格一致

#### 1.4 下拉选择组件
- **参考**：Fiverr图3的交付时间下拉框
- **实现位置**：交付时间和修改次数字段
- **选项内容**：
  - 交付时间：1天、2天、3天、4天、5天等
  - 修改次数：1次、2次、3次、无限次等

#### 1.5 额外服务配置
- **参考**：Fiverr图4的额外服务选项
- **实现位置**：在三列套餐配置区域下方
- **限制**：最多选择两个大类
- **类别**：
  1. 额外快速交付时间（Extra fast delivery）
  2. 追加修改次数（Additional revisions）
- **实现**：每个额外服务包含名称、价格配置

### 2. 数据结构映射方案

#### 2.1 规格名称映射
- **第1个规格名称**："Packages" 
  - 规格值：["Basic", "Standard", "Premium"]
  - 存储：完整的包配置信息（价格、交付时间、修改次数等）

- **第2个规格名称**："extra services (交付时间)"
  - 规格值：["3天之内", "4天之内", "5天之内"]
  - 用途：额外快速交付选项

- **第3个规格名称**："extra services (追加修改次数)"
  - 规格值：["1次修改", "2次修改", "3次修改"]
  - 用途：额外修改次数选项

- **第4个规格名称**："extra services保过期"
  - 规格值：["3个月", "半年", "一年"]
  - 用途：服务保障期选项

#### 2.2 数据存储策略
```javascript
// 示例数据结构
{
  "规格名称": "Packages",
  "规格值": {
    "Basic": {
      "price": 30,
      "delivery_time": "3天",
      "revisions": 2,
      "description": "基础服务包",
      "features": ["基础功能1", "基础功能2"]
    },
    "Standard": {
      "price": 60,
      "delivery_time": "5天", 
      "revisions": 5,
      "description": "标准服务包",
      "features": ["标准功能1", "标准功能2", "标准功能3"]
    },
    "Premium": {
      "price": 120,
      "delivery_time": "7天",
      "revisions": "无限",
      "description": "高级服务包",
      "features": ["高级功能1", "高级功能2", "高级功能3", "高级功能4"]
    }
  }
}
```

### 3. 前端展示功能（商城界面）

#### 3.1 修改范围说明
- **修改页面**：仅商品详情页的规格展示区域
- **对应文件**：`pc-src/pages/goods_detail/_id/index.vue`
- **修改位置**：第161-194行的`attribute`规格展示部分
- **注意**：仅修改商品详情页的展示逻辑，不涉及其他页面

#### 3.2 商品详情页改造
- **目标效果**：参考用户提供的图8、图9，实现Fiverr风格的服务包选择界面
- **改造内容**：
  - 识别服务包类型商品
  - 替换传统规格选择为服务包选择界面
  - 显示三层套餐选项卡
  - 实现额外服务多选功能

#### 3.3 展示逻辑
```vue
<!-- 新的服务包展示组件 -->
<div class="service-packages" v-if="isServiceProduct">
  <!-- 主要服务包选择 -->
  <div class="package-selector">
    <div class="package-title">选择服务包</div>
    <div class="package-options">
      <div 
        v-for="(pkg, key) in servicePackages" 
        :key="key"
        class="package-option"
        :class="{ active: selectedPackage === key }"
        @click="selectPackage(key)"
      >
        <div class="package-name">{{ key }}</div>
        <div class="package-price">￥{{ pkg.price }}</div>
        <div class="package-delivery">{{ pkg.delivery_time }}交付</div>
        <div class="package-revisions">{{ pkg.revisions }}次修改</div>
      </div>
    </div>
  </div>
  
  <!-- 额外服务选择 -->
  <div class="extra-services" v-if="extraServices.length">
    <div class="extra-title">额外服务（可选）</div>
    <div class="extra-options">
      <label 
        v-for="service in extraServices" 
        :key="service.id"
        class="extra-option"
      >
        <input 
          type="checkbox" 
          v-model="selectedExtras"
          :value="service.id"
          :disabled="selectedExtras.length >= 2 && !selectedExtras.includes(service.id)"
        />
        <span class="extra-name">{{ service.name }}</span>
        <span class="extra-price">+￥{{ service.price }}</span>
      </label>
    </div>
  </div>
</div>
```

#### 3.4 价格计算逻辑
- 基础价格：选中的服务包价格
- 额外费用：选中的额外服务价格总和
- 总价显示：基础价格 + 额外服务费用

## 技术实现方案

### 4. 后台开发任务

#### 4.1 规格配置组件改造
**主要修改文件**：`mer/src/views/product/addProduct/components/productSpecs.vue`
**页面路径**：商品管理 → 商品列表 → 添加商品 → 规格设置tab

**改造点**：
1. 在现有"单规格/多规格"基础上，新增"服务包模式"选项
2. 实现三列套餐配置界面（替换原有规格列表区域）
3. 添加额外服务配置区域
4. 实现数据验证和保存逻辑
5. 保持与现有页面风格的一致性

**新增组件**：
```vue
<template>
  <div class="service-package-config">
    <!-- 模式切换 -->
    <el-radio-group v-model="packageMode">
      <el-radio :label="0">传统规格模式</el-radio>
      <el-radio :label="1">服务包模式</el-radio>
    </el-radio-group>
    
    <!-- 服务包配置界面 -->
    <div v-if="packageMode === 1" class="package-config">
      <!-- 基础配置 + Create Packages 按钮 -->
      <div v-if="!packagesExpanded" class="basic-config">
        <!-- 基础表单 -->
      </div>
      
      <!-- 三列套餐配置 -->
      <div v-else class="packages-expanded">
        <div class="package-column" v-for="tier in ['Basic', 'Standard', 'Premium']" :key="tier">
          <!-- 每列的配置表单 -->
        </div>
      </div>
      
      <!-- 额外服务配置 -->
      <div class="extra-services-config">
        <!-- 额外服务表单 -->
      </div>
    </div>
  </div>
</template>
```

#### 4.2 数据处理逻辑
**功能**：将服务包配置转换为现有规格数据结构

**实现**：
```javascript
// 数据转换函数
convertServicePackageToAttr(packageData) {
  const attrs = [];
  
  // 主服务包规格
  attrs.push({
    value: 'Packages',
    detail: Object.keys(packageData.packages).map(key => ({
      value: key,
      data: packageData.packages[key]
    }))
  });
  
  // 额外服务规格
  packageData.extraServices.forEach((service, index) => {
    attrs.push({
      value: `extra services (${service.category})`,
      detail: service.options
    });
  });
  
  return attrs;
}
```

### 5. 前端开发任务

#### 5.1 商品详情页改造
**主要修改文件**：`pc-src/pages/goods_detail/_id/index.vue`
**页面路径**：商城前端 → 商品详情页 → 规格选择区域（第161-194行）

**改造点**：
1. 识别服务包类型商品（通过规格名称"Packages"判断）
2. 渲染服务包选择界面（替换传统规格选择）
3. 实现价格计算逻辑
4. 处理购物车添加逻辑
5. 保持与现有商城页面风格的一致性

**识别逻辑**：
```javascript
computed: {
  isServiceProduct() {
    return this.productAttr.some(attr => attr.attr_name === 'Packages');
  },
  
  servicePackages() {
    const packagesAttr = this.productAttr.find(attr => attr.attr_name === 'Packages');
    if (!packagesAttr) return {};
    
    const packages = {};
    packagesAttr.attr_values.forEach(value => {
      // 解析存储的包数据
      packages[value] = this.parsePackageData(value);
    });
    return packages;
  }
}
```

#### 5.2 交互逻辑实现
**功能**：
1. 服务包选择切换
2. 额外服务多选（最多2个）
3. 实时价格计算
4. 购物车数据构建

### 6. 样式设计要求

#### 6.1 后台界面样式
- 参考Fiverr的简洁设计风格
- 使用Element UI组件保持一致性
- 响应式布局支持

#### 6.2 前端界面样式
- 符合现有商城设计规范
- 清晰的包层级展示
- 突出价格和核心信息
- 移动端适配

## 开发优先级

### 第一阶段：核心功能
1. 后台服务包配置界面
2. 数据存储和转换逻辑
3. 前端基础展示功能

### 第二阶段：增强功能
1. 额外服务配置
2. 复杂交互逻辑
3. 样式优化

### 第三阶段：完善和测试
1. 边界情况处理
2. 性能优化
3. 全面测试

## 注意事项

1. **修改范围限制**：严格控制修改范围，仅修改指定的两个文件，不影响其他页面功能
2. **数据兼容性**：确保改动不影响现有商品的正常展示
3. **性能考虑**：避免复杂的数据转换影响页面加载速度
4. **用户体验**：保持界面简洁，操作直观
5. **扩展性**：预留后续功能扩展的接口
6. **风格一致性**：新增功能界面风格与现有系统保持一致

## 验收标准

1. 后台能够正确配置服务包信息
2. 前端能够正确展示服务包选择界面
3. 价格计算准确无误
4. 购物车和订单流程正常
5. 现有功能不受影响
6. 界面美观，用户体验良好

---

**项目预估工期**：15-20个工作日
**技术难点**：数据结构映射、复杂交互逻辑实现
**风险评估**：中等（主要风险在于数据兼容性）
