// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { login, logout, getInfo, getMenusApi } from "@/api/user";
import { getToken, setToken, removeToken } from "@/utils/auth";
import router, { resetRouter } from "@/router";
import Cookies from "js-cookie";
import {
  formatFlatteningRoutes,
  findFirstNonNullChildren
} from "@/utils/system.js";
const state = {
  token: getToken(),
  name: "",
  avatar: "",
  introduction: "",
  marginStatus: 0,
  marginDays: "",
  roles: [],
  menuList: JSON.parse(localStorage.getItem("MerMenuList")),
  sidebarWidth: window.localStorage.getItem("sidebarWidth"),
  sidebarStyle: window.localStorage.getItem("sidebarStyle"),
  oneLvMenus: [],
  oneLvRoutes: JSON.parse(localStorage.getItem("oneLvRoutes")),
  childMenuList: []
};

const mutations = {
  SET_MENU_LIST: (state, menuList) => {
    state.menuList = menuList;
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  },
  SET_SIDEBAR_WIDTH: (state, width) => {
    state.sidebarWidth = width;
  },
  SET_SIDEBAR_STYLE: (state, style) => {
    state.sidebarStyle = style;
    window.localStorage.setItem("sidebarStyle", style);
  },
  SET_MARGIN_STATUS: (state, status) => {
    state.marginStatus = status;
  },
  SET_MARGIN_DAYS: (state, days) => {
    state.marginDays = days;
  },
  setOneLvMenus(state, oneLvMenus) {
    state.oneLvMenus = oneLvMenus;
  },
  setOneLvRoute(state, oneLvRoutes) {
    state.oneLvRoutes = oneLvRoutes;
  },
  childMenuList(state, list) {
    state.childMenuList = list;
  }
};

const actions = {
  // user login
  login({ commit }, userInfo) {
    // const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login(userInfo)
        .then(response => {
          const { data } = response;
          let arr = formatFlatteningRoutes(router.options.routes);
          let routes = formatFlatteningRoutes(response.data);
          localStorage.setItem("oneLvRoutes", JSON.stringify(routes));
          commit("setOneLvMenus", arr);
          commit("setOneLvRoute", routes);
          commit("SET_TOKEN", data.token);
          setToken(data.token);
          resolve(data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  getMenus({ commit }) {
    return new Promise((resolve, reject) => {
      getMenusApi()
        .then(response => {
          commit("SET_MENU_LIST", response.data);
          localStorage.setItem("MerMenuList", JSON.stringify(response.data));
          resolve(response);
        })
        .catch(error => {
          this.$message.error(error.message);
          reject(error);
        });
    });
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token)
        .then(response => {
          const { data } = response;
          if (!data) {
            reject("Verification failed, please Login again.");
          }
          const { roles, name, avatar, introduction } = data;
          // roles must be a non-empty array
          if (!roles || roles.length <= 0) {
            reject("getInfo: roles must be a non-null array!");
          }
          commit("SET_ROLES", roles);
          commit("SET_NAME", name);
          commit("SET_AVATAR", avatar);
          commit("SET_INTRODUCTION", introduction);
          resolve(data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit("SET_TOKEN", "");
          commit("SET_ROLES", []);
          removeToken();
          resetRouter();
          Cookies.remove();
          // reset visited views and cached views
          // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
          dispatch("tagsView/delAllViews", null, { root: true });

          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit("SET_TOKEN", "");
      commit("SET_ROLES", []);
      removeToken();
      resolve();
    });
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    return new Promise(async resolve => {
      const token = role + "-token";

      commit("SET_TOKEN", token);
      setToken(token);

      const { roles } = await dispatch("getInfo");

      resetRouter();

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch("permission/generateRoutes", roles, {
        root: true
      });

      // dynamically add accessible routes
      router.addRoutes(accessRoutes);

      // reset visited views and cached views
      dispatch("tagsView/delAllViews", null, { root: true });

      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
