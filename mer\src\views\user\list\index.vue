<template>
<div class="divBox">
    <div class="selCard">
        <el-form :model="userFrom" ref="searchForm" inline size="small" label-width="85px">
            <!-- <el-form-item label="用户昵称：" prop="nickname">
                <el-input v-model="userFrom.nickname" placeholder="请输入昵称" clearable class="selWidth" @keyup.enter.native="getList(1)" />
            </el-form-item>
            <el-form-item label="用户ID：" prop="uid">
                <el-input v-model="userFrom.uid" placeholder="请输入用户ID" clearable class="selWidth" @keyup.enter.native="getList(1)" />
            </el-form-item> -->
            <select-search 
                ref="selectSearch" 
                :select="select" 
                :searchSelectList="searchSelectList" 
                @search="searchList" />
            <el-form-item :label="$t('用户标签：')" prop="label_id">
                <el-select v-model="userFrom.label_id" :placeholder="$t('请选择')" class="selWidth" clearable filterable @change="getList(1)">
                    <el-option value="">{{ $t('全部') }}</el-option>
                    <el-option v-for="(item, index) in labelLists" :key="index" :value="item.label_id" :label="item.label_name" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('用户性别：')" prop="sex">
                <el-select v-model="userFrom.sex" :placeholder="$t('请选择')" class="selWidth" clearable filterable @change="getList(1)">
                    <el-option value="">{{ $t('全部') }}</el-option>
                    <el-option value="1" :label="$t('男')" />
                    <el-option value="2" :label="$t('女')" />
                    <el-option value="0" :label="$t('保密')" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('用户身份：')" prop="is_promoter">
                <el-select v-model="userFrom.is_promoter" :placeholder="$t('请选择')" class="selWidth" clearable filterable  @change="getList(1)">
                    <el-option value="">{{ $t('全部') }}</el-option>
                    <el-option value="1" :label="$t('推广员')" />
                    <el-option value="0" :label="$t('普通用户')" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('访问情况：')" prop="user_time_type">
                <el-select v-model="userFrom.user_time_type" :placeholder="$t('请选择')" class="selWidth" clearable  @change="getList(1)">
                    <el-option value="visit" :label="$t('最后访问')" />
                    <el-option value="add_time" :label="$t('首次访问')" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('消费情况：')" prop="pay_count">
                <el-select v-model="userFrom.pay_count" :placeholder="$t('请选择')" class="selWidth" clearable  @change="getList(1)">
                    <el-option value="-1" :label="$t('0次')"></el-option>
                    <el-option value="0" :label="$t('1次及以上')"></el-option>
                    <el-option value="1" :label="$t('2次及以上')"></el-option>
                    <el-option value="2" :label="$t('3次及以上')"></el-option>
                    <el-option value="3" :label="$t('4次及以上')"></el-option>
                    <el-option value="4" :label="$t('5次及以上')"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('访问时间：')">
                <el-date-picker v-model="timeVal" value-format="yyyy/MM/dd" align="right" unlink-panels format="yyyy/MM/dd" size="small" type="daterange" placement="bottom-end" :placeholder="$t('自定义时间')" style="width: 280px;" :picker-options="pickerOptions" @change="onchangeTime" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="small" @click="getSearchList">{{ $t('搜索') }}</el-button>
                <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
            </el-form-item>
        </el-form>
    </div>
    <el-card class="mt14">
        <el-tabs v-model="user_type" @tab-click="getList(1)">
            <el-tab-pane :label="$t('全部用户')" name="" />
            <el-tab-pane :label="$t('微信用户')" name="wechat" />
            <el-tab-pane :label="$t('小程序用户')" name="routine" />
            <el-tab-pane :label="$t('H5用户')" name="h5" />
            <el-tab-pane label="APP" name="app" />
            <el-tab-pane label="PC" name="pc" />
        </el-tabs>
        <div class="mt5 mb20">
            <el-button type="primary" icon="ios-search" label="default" class="mr15" size="small" @click="sendCoupon">{{ $t('发送优惠券') }}</el-button>
            <el-alert v-if="checkedIds.length>0 || allCheck" :title="allCheck ? `已选择  ${tableData.total}  项` : `已选择  ${checkedIds.length}  项`" type="info" show-icon class="mt10" />
        </div>
        <el-table v-loading="listLoading" :data="tableData.data" size="small" >
          <el-table-column  width="50">
            <template slot="header" slot-scope="scope">
              <el-popover placement="top-start" width="100" trigger="hover" class="tabPop">
                <div>
                  <span class="spBlock onHand" :class="{'check': chkName === 'dan'}" @click="onHandle('dan',scope.$index)">{{ $t('选中本页') }}</span>
                  <span class="spBlock onHand" :class="{'check': chkName === 'duo'}" @click="onHandle('duo')">{{ $t('选中全部') }}</span>
                </div>
                <el-checkbox slot="reference" :value="(chkName === 'dan' && checkedPage.indexOf(userFrom.page) > -1) || chkName === 'duo'" @change="changeType" />
              </el-popover>
            </template>
            <template slot-scope="scope">
              <el-checkbox :value="checkedIds.indexOf(scope.row.uid) > -1 || (chkName === 'duo' && noChecked.indexOf(scope.row.uid) === -1)" @change="(v)=>changeOne(v,scope.row)" />
            </template>
          </el-table-column>
          <el-table-column prop="uid" label="ID" min-width="60" />
          <el-table-column :label="$t('头像')" min-width="50">
            <template slot-scope="scope">
                <div class="demo-image__preview">
                    <el-image style="width: 36px; height: 36px" :src="scope.row.avatar ? scope.row.avatar : moren" :preview-src-list="[scope.row.avatar || moren]" />
                </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('昵称')" min-width="90">
            <template slot-scope="{row}">
                <div class="acea-row">
                    <i v-show="row.sex===1" class="el-icon-male mr5" style="font-size: 15px; margin-top: 3px; color:#2db7f5; " />
                    <i v-show="row.sex===2" class="el-icon-female mr5" style="font-size: 15px; margin-top: 3px; color:#ed4014; " />
                    <div v-text="row.nickname" />
                </div>
                <div v-show="row.vip_name" class="vipName">{{ row.vip_name }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="is_svip" :label="$t('付费会员')" min-width="120">
            <template slot-scope="{row}">
                <span>{{row.is_svip > 0 ? "是" : "否"}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="phone" :label="$t('手机号')" min-width="120" />
          <el-table-column :label="$t('首次访问时间')" prop="create_time" min-width="120" />
          <el-table-column :label="$t('用户类型')" min-width="100">
            <template slot-scope="{row}">
              <span>{{ row.user_type === 'routine' ? '小程序' : row.user_type === 'wechat' ? '公众号' : row.user_type === 'app' ? 'App' : row.user_type === 'pc' ? 'PC' : 'H5' }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('首次消费时间')" prop="first_pay_time" min-width="120" />
          <el-table-column :label="$t('最近消费时间')" prop="last_pay_time" min-width="120" />
            <el-table-column :label="$t('标签')" min-width="100">
                <template slot-scope="{row}">
                    <span> {{ row.label.join('、') }}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('操作')" min-width="130" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="small" class="mr10" @click="onDetails(scope.row)">{{ $t('详情') }}</el-button>
                  <el-button type="text" size="small" class="mr10" @click="setLabel(scope.row.user_merchant_id)">{{ $t('设置标签') }}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block">
            <el-pagination background :page-size="userFrom.limit" :current-page="userFrom.page" layout="total, prev, pager, next, jumper" :total="tableData.total" @size-change="handleSizeChange" @current-change="pageChange" />
        </div>
    </el-card>
    <!--会员详情-->
    <el-dialog v-if="visibleDetail" :title="$t('用户详情')" :visible.sync="visibleDetail" width="1000px" :before-close="Close">
      <user-details v-if="visibleDetail" ref="userDetails" :uid="uid" :row="row" />        
    </el-dialog>
    <!-- 选择优惠券 -->
     <el-dialog v-if="visibleCoupon" :title="$t('优惠券列表')" :visible.sync="visibleCoupon" width="1000px">
      <coupon-List v-if="visibleCoupon" ref="couponList" :couponForm="couponForm" :checkedIds="checkedIds" :allCheck="allCheck" :userFrom="userFrom" @sendSuccess="sendSuccess" />        
    </el-dialog>
</div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { userLstApi, changeGroupApi, labelLstApi } from '@/api/user'
import userDetails from './userDetails'
import couponList from './couponList'
import selectSearch from '@/components/base/selectSearch';
export default {
    name: 'UserList',
    components: {
      userDetails,
      couponList,
      selectSearch
    },
    data() {
        return {
            moren: require("@/assets/images/f.png"),
            pickerOptions: {
                shortcuts: [{
                        text: leaveuKeyTerms['今天'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()))
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: leaveuKeyTerms['昨天'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() - 1)))
                            end.setTime(end.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())))
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: leaveuKeyTerms['最近7天'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: leaveuKeyTerms['最近30天'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: leaveuKeyTerms['本月'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)))
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: leaveuKeyTerms['本年'],
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.setTime(new Date(new Date().getFullYear(), 0, 1)))
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            timeVal: [],
            collapse: false,
            visibleDetail: false,
            visibleCoupon: false,
            maxCols: 3,
            isShowSend: true,
            visible: false,
            user_type: '',
            tableData: {
                data: [],
                total: 0
            },
            listLoading: true,
            wechatIds: '',
            row: '',
            labelPosition: 'right',
            userProps: {
                children: 'children',
                label: 'name',
                value: 'name'
            },
            select: "nickname",
            searchSelectList: [
                {label: leaveuKeyTerms['昵称'], value: "nickname"},
                {label: leaveuKeyTerms['用户ID'], value: "uid"},
                {label: leaveuKeyTerms['手机号'], value: "phone"},
            ],
            userFrom: {
                label_id: '',
                user_type: '',
                sex: '',
                is_promoter: '',
                country: '',
                pay_count: '',
                user_time_type: '',
                user_time: '',
                nickname: '',
                province: '',
                city: '',
                page: 1,
                limit: 20,
                group_id: ''
            },
             couponForm: {
                用户标签: '',
                用户类型: '',
                性别: '',
                身份: '',
                消费情况: '',
                访问情况: '',
                访问时间: '',
                昵称: ''
            },
            address: [],
            grid: {
                xl: 8,
                lg: 12,
                md: 12,
                sm: 24,
                xs: 24
            },
            grid2: {
                xl: 18,
                lg: 16,
                md: 12,
                sm: 24,
                xs: 24
            },
            grid3: {
                xl: 8,
                lg: 12,
                md: 12,
                sm: 24,
                xs: 24
            },
            addresData: [],
            groupList: [],
            labelLists: [],
            chkName: '',
            checkedPage: [],
            checkedIds: [], // 订单当前页选中的数据
            noChecked: [], // 订单全选状态下当前页不选中的数据
            allCheck: false
        }
    },
    mounted() {
        this.getTagList()
        this.getList('')
    },
    methods: {
        /**重置 */
        searchReset(){
            this.timeVal = []
            this.userFrom.user_time = ""
            this.$refs.searchForm.resetFields()
            this.$refs.selectSearch.resetParmas()
        },
        // 用户
        onHandle(name) {
          this.chkName = this.chkName === name ? '' : name
          this.changeType(!(this.chkName === ''))
        },
        changeType(v) {
          if (v) {
            if (!this.chkName) {
              this.chkName = 'dan'
            }
          } else {
            this.chkName = ''
            this.allCheck = false;
          }
          const index = this.checkedPage.indexOf(this.userFrom.page)
          if (this.chkName === 'dan') {
            this.checkedPage.push(this.userFrom.page)
          } else if (index > -1) {
            this.checkedPage.splice(index, 1)
          }
          this.syncCheckedId()
        },
        syncCheckedId() {
          const ids = this.tableData.data.map(v => v.uid)
          if (this.chkName === 'duo') {
            this.checkedIds = []
            this.allCheck = true;
          } else if (this.chkName === 'dan') {
            this.allCheck = false;
            ids.forEach(id => {
              const index = this.checkedIds.indexOf(id)
              if (index === -1) {
                this.checkedIds.push(id)
              }
            })
          } else {
            ids.forEach(id => {
              const index = this.checkedIds.indexOf(id)
              if (index > -1) {
                this.checkedIds.splice(index, 1)
              }
            })
          }
        },
        // 发送优惠券
        sendCoupon(){
            if(this.checkedIds.length == 0 && this.allCheck == false){
                this.$message.warning('请选择用户')
            }else{
                this.visibleCoupon = true;
            }
        },
        // 分开选择
        changeOne(v, user) {
            if (v) {
                if (this.chkName === 'duo') {
                    const index = this.noChecked.indexOf(user.uid)
                    if (index > -1) this.noChecked.splice(index, 1)
                } else {
                    const index = this.checkedIds.indexOf(user.uid)
                    if (index === -1) this.checkedIds.push(user.uid)
                }
            } else {
                if (this.chkName === 'duo') {
                    const index = this.noChecked.indexOf(user.uid)
                    if (index === -1) this.noChecked.push(user.uid)
                } else {
                    const index = this.checkedIds.indexOf(user.uid)
                    if (index > -1) this.checkedIds.splice(index, 1)
                }
            }
        },
        sendSuccess(){
            this.visibleCoupon = false;
        },
        getCoupounParmas(){
            let label_id = this.userFrom.label_id == '' ? '' : this.getLabelValue(),
            user_type = this.findKey(this.userFrom.user_type,{'':'','微信用户':'wechat','小程序用户':'routine','H5用户':'h5'}),
            sex = this.findKey(this.userFrom.sex,{'男':'1','女':'2','保密':'0','':''}),
            pay_count = this.findKey(this.userFrom.sex,{'0次':'-1','1次及以上':'0','2次及以上':'1','3次及以上':'2','4次及以上':'3','5次及以上':'4','': ''}),          
            is_promoter = this.findKey(this.userFrom.is_promoter,{'推广员':'1','普通用户':'0','':''}),
            user_time_type = this.userFrom.user_time_type == 'visit' ? '最后访问' : this.userFrom.user_time_type == 'add_time' ? '首次访问' : ''
            this.couponForm =  {
                用户标签: label_id,
                用户类型: user_type,
                性别: sex,
                消费情况: pay_count,
                身份: is_promoter,
                访问情况: user_time_type,
                访问时间: this.userFrom.user_time,
                昵称: this.userFrom.nickname
            }
        },
        findKey(value,data, compare = (a, b) => a === b) {
            return Object.keys(data).find(k => compare(data[k], value))
        },
        getLabelValue(){
            let labelName = ''
            for(let i = 0; i < this.labelLists.length; i ++) {
                if(this.labelLists[i]['label_id'] === this.userFrom.label_id) {
                    labelName = this.labelLists[i]['label_name']
                    return labelName
                }
            }
        },
        // 具体日期
        onchangeTime(e) {
            this.timeVal = e
            this.userFrom.user_time = e ? this.timeVal.join('-') : ''
            this.getList(1)
        },
        userSearchs() {
          if(this.userFrom.user_time_type && (!this.userFrom.user_time)){
            this.$message.error('请选择访问时间')
          }else if(!this.userFrom.user_time_type && (this.userFrom.user_time)){
            this.$message.error('请选择访问情况')
          }else{
            this.getList(1)
          }          
        },
        // 标签列表
        getTagList() {
            labelLstApi({
                page: 1,
                limit: 9999,
                all: 1
            }).then(res => {
                this.labelLists = res.data.list
            }).catch(res => {
                this.$message.error(res.message)
            })
        },
        // 详情
        onDetails(row) {
            this.row = row
            this.uid = row.uid
            this.visibleDetail = true
        },
        Close() {
            this.visibleDetail = false
        },
        handleClose() {
            this.visible = false
        },
        // 修改标签
        setLabel(id) {
          this.$modalForm(changeGroupApi(id)).then(() => this.getList(''))
        },
        searchList(data) {
            this.userFrom = {...this.userFrom, ...data};
            this.getList(1)
        },
        getSearchList() {
            this.$refs.selectSearch.changeSearch()
        },
        // 列表
        getList(num) {
            this.listLoading = true
            this.userFrom.page = num ? num : this.userFrom.page;
            this.userFrom.user_type = this.user_type
            this.userFrom.province = this.address[0]
            this.userFrom.city = this.address[1]
            if (this.userFrom.user_type === '0') this.userFrom.user_type = ''
            userLstApi(this.userFrom).then(res => {
                this.tableData.data = res.data.list
                this.tableData.total = res.data.count
                this.listLoading = false
                this.getCoupounParmas();
            }).catch(res => {
                this.listLoading = false
                this.$message.error(res.message)
            })
        },
        pageChange(page) {
            this.userFrom.page = page
            this.getList('')
        },
        handleSizeChange(val) {
            this.userFrom.limit = val
            this.getList('')
        },
        handleClick() {
            this.getList('')
        },
        // 重置
        reset() {
            this.userFrom = {
                label_id: '',
                user_type: '',
                sex: '',
                is_promoter: '',
                country: '',
                pay_count: '',
                user_time_type: '',
                user_time: '',
                nickname: '',
                province: '',
                city: '',
                page: 1,
                limit: 20,
                group_id: '',
                date: ''
            },
            this.timeVal = []
            this.getList(1)
        }
    }
}
</script>

<style lang="scss" scoped>
.spBlock {
    cursor: pointer;
    display: block;
    padding: 5px 0;
}
.check {
    color: #00a2d4;
}
.dia ::v-deep .el-dialog__body {
    height: 700px !important;
}
::v-deep .el-date-editor{
    width: 300px;
}
.text-right {
    text-align: right;
}
.container {
    min-width: 821px;
}
.container ::v-deep .el-form-item {
    width: 100%;
}
.container ::v-deep .el-form-item__content {
    width: 72%;
}
.vipName {
    color: #dab176
}
.el-icon-arrow-down {
    font-size: 12px;
}
.demo-table-expand {
    font-size: 0;
}
.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 33.33%;
}
::v-deep [type=reset],
[type=submit],
button,
html [type=button] {
    -webkit-appearance: none !important;
}
.box-container {
  overflow: hidden;
}
.box-container .list {
  line-height: 40px;
  display: flex;
  align-items: center;
}

.box-container .list .name {
  display: inline-block;
  width: 100px;
  text-align: right;
  color: #606266;
}
.box-container .list .blue {
  color: var(--prev-color-primary);
}
</style>
