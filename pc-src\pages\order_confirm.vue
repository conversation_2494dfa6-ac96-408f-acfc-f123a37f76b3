<template>
  <div class="order_confirm wrapper_1200" >
    <div class="header">
      <span class="home">首页 > <span v-if="!news">购物车 > </span> </span>提交订单
    </div>
    <template v-if="allow_address&&order_model == 0">
      <div class="address" v-if="allow_address&&order_model == 0">
        <div class="title">收货地址</div>
        <div class="lines">
          <img src="../assets/images/line.png">
        </div>
        <div class="list acea-row row-middle" :class="isShow?'on':''">
          <div class="item" :class="current===index?'on':''" v-for="(item, index) in addressList" :key="index"
              @click="tapCurrent(index,item)">
            <div class="default bg-color" v-if="item.is_default">默认</div>
            <div class="name line1">{{ item.real_name }}</div>
            <div class="phone">{{ item.phone }}</div>
            <div class="details line4">{{ item.province }}{{ item.city }}{{ item.district }}{{ item.detail }}</div>
            <div class="iconfont icon-xuanzhong4 font-color" v-if="current===index"></div>
          </div>
          <div class="item add" @click="addAddress">
            <div class="iconfont icon-dizhi-tianjia"></div>
            <div class="tip">添加新地址</div>
          </div>
        </div>
      </div>
      <div class="isShow" @click="open" v-if="!isShow && addressList.length>3">显示更多收货地址<span
        class="iconfont icon-xiangxia"></span></div>
      <div style="margin-top: 10px" v-if="addressList.length<=3"></div>
      <div class="isShow" @click="close" v-if="isShow && addressList.length>3">隐藏更多收货地址<span
        class="iconfont icon-xiangshang"></span></div>
    </template>
    <div v-if="order_model == 4" class="address">
      <div class="title">{{service_type == 1 ? '到店服务' : '上门服务'}}</div>
      <div class="lines">
        <img src="../assets/images/line.png">
      </div>
    </div>
    <div class="wrapper wrapper_1200">
      <div class="wrapper_count">
        <div class="title">订单信息</div>
        <div class="order">
          <div class="list">
            <div class="cartCount" v-for="(item, index) in cartInfo" :key="index">
              <div class="storeInfo acea-row row-between-wrapper">
                <div class="name">{{ item.mer_name }}</div>
                <div class="service" @click="chatShow(item.mer_id)">联系客服 <span class="iconfont icon-lianxikefu"></span></div>
              </div>
              <div class="cartInfo">
                <div class="item" v-for="(itemn, indexn) in item.list" :key="indexn">
                  <div class="acea-row row-between-wrapper" v-if="itemn.product_type == 2">
                    <div class="txtPic acea-row row-middle">
                      <div class="pictrue">
                        <img :src='itemn.productPresellAttr.image' v-if="itemn.productPresellAttr.image">
                        <img :src='itemn.product.image' v-else>
                        <span>预售</span>
                      </div>
                      <div class="text">
                        <div class="name line1">{{ itemn.productPresell.store_name }}</div>
                        <div class="info" v-if="itemn.productAttr">{{ itemn.productAttr.sku }}</div>
                        <div class="info" v-else>默认</div>
                        <div class="err-txt acea-row" v-if="itemn.undelivered && addressInfo.real_name">
                          <span class="iconfont icon-tishi"></span>
                          <div class="txt">此商品不支持该区域配送</div>
                        </div>
                        <div class="ship_date">
                          <!--全款预售-->
										      <span
										      	v-if="itemn.productPresell.presell_type === 1">{{itemn.productPresell.delivery_type === 1 ? '支付后' : '预售结束后'}}{{ itemn.productPresell.delivery_day }}天内</span>
										      <!--定金预售-->
										      <span
										      	v-if="itemn.productPresell.presell_type === 2">{{itemn.productPresell.delivery_type === 1 ? '付尾款后' : '预售结束后' }}{{ itemn.productPresell.delivery_day }}天内</span>
                        </div>
                      </div>
                    </div>
                    <div class="acea-row row-middle">
                      <div class="money_count">
                        <span class="money" v-if="itemn.productPresellAttr && itemn.productPresellAttr.presell_price">¥{{ itemn.productPresellAttr.presell_price }}</span>
                        <span class="money" v-else>{{ itemn.product.price }}</span>
                        <span class="num">x{{ itemn.cart_num }}</span>
                      </div>
                      <div>
                        <span v-if="itemn.productPresell.presell_type === 1"  class="font-color">{{ itemn.productPresellAttr.presell_price }}</span>
                        <div v-if="itemn.productPresell.presell_type === 2">
                          <div class="acea-row row-middle">
                            <span class="money_down">定金</span><span class="font-color">¥{{ (itemn.productPresellAttr.down_price * itemn.cart_num).toFixed(2) }}</span>
                          </div>
                          <div class="money_final">(尾款¥{{ (itemn.productPresellAttr.final_price * itemn.cart_num).toFixed(2) }})</div>
                        </div>
                      </div>

                    </div>
                  </div>
                  <div v-else class="acea-row row-between-wrapper">
                    <div class="txtPic acea-row row-middle">
                      <div class="pictrue">
                        <img :src='itemn.productAttr.image' v-if="itemn.productAttr.image">
                        <img :src='itemn.product.image' v-else>
                      </div>
                      <div class="text">
                        <div class="name line2">{{ itemn.product.store_name }}</div>
                        <div class="info" v-if="itemn.productAttr">{{ itemn.productAttr.sku }}</div>
                        <div class="info" v-else>默认</div>
                        <div class="err-txt acea-row" v-if="itemn.undelivered && addressInfo.real_name">
                          <span class="iconfont icon-tishi"></span>
                          <div class="txt">此商品不支持该区域配送</div>
                        </div>
                      </div>
                    </div>
                    <div class="acea-row row-between-wrapper">
                      <div class="money acea-row row-middle">¥{{ itemn.productAttr && itemn.productAttr.price ? itemn.productAttr.price : itemn.product.price }}
                        <span v-if="itemn.productAttr.show_svip_price" class="svip-image"><img src="@/assets/images/svip.png" alt=""></span>
                        <span class="num">x{{ itemn.cart_num }}</span>
                      </div>
                      <!-- <span class="font-color">{{ itemn.productAttr.price }}</span> -->
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="order_type!=3 && order_type!=4  && item.coupon.length && item.order.enabledCoupon" class="coupon">
                <div v-if="order_type!=3 && order_type!=4  && item.coupon.length && item.order.enabledCoupon" class="couponTitle acea-row row-between-wrapper">
                  <div class="item-name">使用优惠券</div>
                  <div class="couponPrice font-color">-¥{{ item.order.coupon_price }}</div>
                  <!--<div class="couponPriceNo font-color" v-else>无可使用优惠券</div>-->
                </div>
                <div v-if="item.coupon.length>0" class="couponList acea-row row-middle">
                  <div class="item acea-row row-middle" :class="itemn.disabled ? 'disabled' : ''"
                       v-for="(itemn, indexn) in item.coupon" :key="indexn" @click="getCoupon(itemn)">
                    <div class="name" v-if="itemn.coupon.type">商品券</div>
                    <div class="name" v-else>店铺券</div>
                    <div class="money line1">满{{ itemn.use_min_price }}减{{ parseFloat(itemn.coupon_price) }}</div>
                    <div class="iconfont icon-xuanzhong4" v-if="itemn.checked"></div>
                  </div>
                </div>
              </div>
              <div class="coupon" v-if="order_model == 0">
                <div class="couponTitle acea-row row-between-wrapper">
                  <div class="item-name">运费
                    <span	v-if="item.list[0].productPresell && item.list[0].productPresell.presell_type == 2 && item.order.postage_price > 0">(尾款阶段）</span>
                  </div>
                  <div class="couponPrice font-color" v-if='item.order.postage_price > 0'>
                    +￥{{ item.order.postage_price }}
                  </div>
                  <div class="couponPriceNo font-color" v-else>免运费</div>
                </div>
              </div>
              <div v-if="order_type === 2 && item.list[0].productPresell.presell_type ==2" class="coupon">
                <div class="couponTitle acea-row row-between-wrapper">
                  <div class="presell_protocol" @click="getPresellAgree">
                  <span class="iconfont icon-wenhao1"></span>
                  我已同意定金不退等预售协议</div>
                  <div class="checkbox-wrapper check_protocal">
                    <label class="well-check">
                      <input
                        type="checkbox"
                        name=""
                        value=""
                        :checked="isAgree"
                        @change="changeIsAgree"/>
                      <i class="icon"></i>
                    </label>
                  </div>
                </div>
              </div>
              <template v-if="order_model != 4">
                <div class="coupon">
                  <div class="couponTitle acea-row">
                    <div class="item-name">配送方式</div>
                    <div class="acea-row list develivery" v-if="item.delivery_way.length == 2 && (item.order.allow_delivery && item.order.allow_take)">
                      <div class="deliviery_item" :class="!item.order.isTake ? 'checked' : ''" @click="getDevelivery(item,0,index,0)">
                        <div class="cont">
                          <div class="acea-row row-middle name">{{deliveryName}}</div>
                          <div class="iconfont icon-xuanzhong4" v-if="!item.order.isTake"></div>
                        </div>
                      </div>
                      <div v-if="order_model != 2" class="deliviery_item" :class="item.order.isTake ? 'checked' : ''" @click="getDevelivery(item,1,index,1)">
                        <div class="cont">
                          <div class="acea-row row-middle name">到店核销</div>
                          <div class="iconfont icon-xuanzhong4" v-if="item.order.isTake"></div>
                        </div>
                      </div>
                    </div>
                    <div class="acea-row list develivery" v-else>
                      <div class="deliviery_item checked">
                        <div class="cont">
                          <div class="acea-row row-middle name">{{item.order.isTake==0 ? deliveryName :'到店核销'}}</div>
                          <div class="iconfont icon-xuanzhong4"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="item.order.isTake" class="develivery_take">
                    <span class="iconfont icon-dizhi"></span>
                    <div>{{item.take.mer_take_address}}</div>
                  </div>
                </div>
                <div class="coupon invoice" v-if="item.openReceipt == 1">
                  <div class="couponTitle acea-row">
                    <div class="item-name">发票类型</div>
                    <div class="acea-row list develivery">
                      <div class="deliviery_item" :class="!item.invoiceData.checked ? 'checked' : ''" @click="noInvoice(item,index)">
                        <div class="cont">
                          <div class="acea-row row-middle name">不开发票</div>
                          <div class="iconfont icon-xuanzhong4" v-if="!item.invoiceData.checked"></div>
                        </div>
                      </div>
                      <div class="deliviery_item" :class="item.invoiceData && item.invoiceData.checked ? 'checked' : ''" @click="showInvoicePupon(item,index)">
                        <div class="cont">
                          <div class="acea-row row-middle name">电子发票</div>
                          <div class="iconfont icon-xuanzhong4" v-if="item.invoiceData && item.invoiceData.checked"></div>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.invoiceData && item.invoiceData.receipt_title && item.invoiceData.checked" class="invoice_data font-color">
                      <div v-if="item.invoiceData.receipt_title" class="data_item">
                      {{item.invoiceDatareceipt_type == 1 ? '电子普通发票 ' : '增值税发票'}}{{item.invoiceData.receipt_title}}
                      </div>
                      <div class="data_item">{{item.invoiceData.receipt_title_type == '1' ? '个人' : '企业'}}</div>
                      <div class="data_item modify" @click="changeInvoiceData(item)">修改</div>
                    </div>
                  </div>
                  <div class="invoice_info" @click="invoiceVisible = true; isPresell = false;">
                    <span class="iconfont icon-wenhao">?</span>
                    发票说明 >>
                  </div>
                </div>
                <!--虚拟商品-->
                <div v-if="order_form.length>0" class="wrapper virtual_form">
                  <el-form report-submit='true'>
                    <div
                      class="virtual-item"
                      v-for="(item, index) in order_form"
                      :key="index"
                    >
                      <div class='item acea-row'>
                        <div class="virtual-title"> <span v-if="item.titleShow.val" class="item-require">*</span>{{item.titleConfig.value}} </div>
                        <!-- checkboxs -->
                      <div v-if="item.name == 'checkboxs'" class="discount">
                        <el-checkbox-group v-model="item.values" size="medium">
                          <el-checkbox :label="j" v-for="(j,jindex) in item.wordsConfig.list" :key="jindex">{{j.val}}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <!-- radios -->
                      <div v-if="item.name == 'radios'" class="discount">
                        <el-radio-group v-model="item.values" size="medium" >
                          <el-radio
                            :label="j" v-for="(j,jindex) in item.wordsConfig.list" :key="jindex"
                          >{{j.val}}</el-radio>
                        </el-radio-group>
                      </div>
                      <!-- text -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 0" class='discount'>
                        <el-input v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- number -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 4" class="discount">
                        <el-input type="number" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- email -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 3" class="discount">
                        <el-input v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- selects -->
                      <div v-if="item.name == 'selects'" class="discount">
                        <el-select v-model="item.values" placeholder="请选择">
                          <el-option
                            v-for="(j,jindex) in item.wordsConfig.list"
                            :key="jindex"
                            :label="j.val"
                            :value="jindex">
                          </el-option>
                        </el-select>
                      </div>
                      <!-- data -->
                      <div v-if="item.name == 'dates'" class="discount">
                        <el-date-picker
                          class="confirm"
                          v-model="item.values"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :placeholder="item.tipConfig.value"
                        >
                        </el-date-picker>
                      </div>
                      <!-- dateranges -->
                      <div v-if="item.name == 'dateranges'" class="discount">
                        <el-date-picker
                          class="confirm"
                          v-model="item.values"
                          type="daterange"
                          format="yyyy/MM/dd"
                          value-format="yyyy/MM/dd"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                        >
                        </el-date-picker>
                      </div>
                      <!-- time -->
                      <div v-if="item.name == 'times'" class="discount">
                        <el-time-picker
                          format="HH:mm"
                          value-format="HH:mm"
                          class="confirm"
                          v-model="item.values"
                          :placeholder="item.tipConfig.value"
                        >
                        </el-time-picker>
                      </div>
                      <!-- timeranges -->
                      <div v-if="item.name == 'timeranges'" class="discount">
                        <el-time-picker
                          format="HH:mm"
                          value-format="HH:mm"
                          class="confirm"
                          is-range
                          v-model="item.values"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          placeholder="选择时间范围"
                        >
                        </el-time-picker>
                      </div>
                      <!-- id -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 2" class="discount">
                        <el-input type="idcard" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- phone -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 1" class="discount">
                        <el-input maxlength="11" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- city -->
                      <div v-if="item.name == 'citys'" @click="changeRegion(item)" class="discount">
                        <el-cascader ref="cascader" :placeholder="item.tipConfig.value" :props="cascaderProps" v-model="item.values" class="confirm"></el-cascader>
                      </div>
                      <!-- img -->
                      <div v-if="item.name == 'uploadPicture'">
                        <el-upload
                          class="upload"
                          list-type="picture-card"
                          :action="upLoadUrl"
                          :limit="8"
                          :on-remove="handleRemove"
                          :on-success="handleSuccess"
                          :on-exceed="handleExceed"
                          :before-upload="beforeUpload"
                        >
                          <i class="el-icon-plus"></i>
                        </el-upload>
                      </div>
                      </div>
                    </div>
                  </el-form>
                </div> 
              </template>
              <div class="coupon message">
                <div class="item-name">买家留言</div>
                <textarea
                  class="textarea"
                  placeholder="填写内容与商家协商并确认，限150字内~"
                  v-model="msgObj[item.mer_id]"
                  maxlength="150"
                ></textarea>
              </div> 
            </div>
            <!--平台券-->
            <div v-if="platformCoupon.length>0" class="cartCount">
              <div class="coupon">
                <div v-if="order_type != 3 && order_type != 4 && enabledPlatformCoupon" class="plantTitle acea-row row-between-wrapper">
                  <div class="acea-row title">平台券<span @click="getPlantCoupon" class="iconfont icon-wenhao">?</span></div>
                  <div class="couponPrice font-color">-¥{{ total_platform_coupon_price }}</div>
                </div>
                <div class="couponList acea-row row-middle">
                  <div class="item acea-row row-middle" :class="itemn.disabled ? 'disabled item'+itemn.coupon.send_type : 'item'+itemn.coupon.send_type"
                    v-for="(itemn, indexn) in platformCoupon" :key="indexn" @click="getCoupon(itemn)">
                    <div class="name" v-if="itemn.coupon">{{itemn.coupon.type == 0 ? '店铺券' : itemn.coupon.type == 1 ? '商品券' : itemn.coupon.type == 12 ? '跨店券' : itemn.coupon.type == 11 ? '品类券' : '通用券' }}</div>
                    <div class="money line1">满{{ itemn.use_min_price }}减{{ parseFloat(itemn.coupon_price) }}</div>
                    <div class="iconfont icon-xuanzhong4 font-color" v-if="itemn.checked"></div>
                  </div>
                </div>
              </div>
            </div>
            <!--核销人联系方式-->
            <div v-if="is_take && order_model == 1" class="cartCount">
              <div class="wrapper virtual_form" style="border: none;">
                <el-form report-submit='true'>
                  <div class="virtual-item">
                    <div class='item acea-row'>
                      <div class="virtual-title"><span class="item-require">*</span>收货人姓名：</div>
                      <div class='discount'>
                        <el-input type="text" v-model="post.real_name" placeholder="请填写收货人姓名" placeholder-class='placeholder' />
                      </div>
                    </div>
                  </div>
                  <div class="virtual-item">
                    <div class='item acea-row'>
                      <div class="virtual-title"><span class="item-require">*</span>收货人电话：</div>
                      <div class='discount'>
                        <el-input type="number" v-model="post.phone" placeholder="请填写收货人电话" placeholder-class='placeholder' />  
                      </div>
                    </div>
                   </div>
                </el-form>
              </div>  
            </div>
          </div>
        </div>
      </div>
      <div class="wrapper_count">
        <div class='integral_count' v-if="open_integral && order_type == 0 && userInfo.integral>0">
          <div class="integral_title"></div>
          <div class='money acea-row'>
            <div class="checkbox-wrapper">
              <label class="well-check">
                <input
                  type="checkbox"
                  name=""
                  value=""
                  :checked="use_integral"
                  @change="changeIntegral"/>
                <i class="icon"></i>
              </label>
            </div>
            <span v-if="userInfo.integral>0 && !use_integral">当前积分<span class="font-color">{{userInfo.integral}}</span></span>
            <span v-if="use_integral" class="integral_price">
              <span>使用了{{integral_count}}个积分，抵扣</span><span v-if="userInfo.integral>0" class="font-color">{{integral_price}}元</span>
              <span v-else class="font-color">无可使用积分</span>
            </span>
          </div>
        </div>
        <div class="totalCon">
          <div class="total acea-row row-middle row-right">
            <div><span class=font-color>{{ totalNum }} </span>件商品，总商品金额：</div>
            <div class="money">¥{{ proPrice || 0 }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.total_platform_coupon_price>0">
            <div>平台优惠金额：</div>
            <div class="money">-¥{{ priceGroup.total_platform_coupon_price }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_coupon_price>0">
            <div>店铺优惠金额：</div>
            <div class="money">-¥{{ priceGroup.order_coupon_price }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_svip_discount>0">
            <div>SVIP优惠金额：</div>
            <div class="money">-¥{{ priceGroup.order_svip_discount }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="total_coupon > 0">
            <div>共优惠：</div>
            <div class="money">-¥{{ total_coupon }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_total_postage > 0">
            <div>运费：</div>
            <div class="money">+¥{{ priceGroup.order_total_postage || 0 }}</div>
          </div>
          <div class="total acea-row row-middle row-right">
            <div>应付总额：</div>
            <div class="money font-color total-money">¥{{ totalPrice }}</div>
          </div>
        </div>
        
        <div class="bnt acea-row row-right">
          <button class="submit" @click="SubOrder($event)">提交订单</button>
        </div>
      </div>
    </div>
    <!-- 添加地址弹窗 -->
    <el-dialog
      title="添加收货地址"
      :visible.sync="dialogVisible"
      width="700"
      :before-close="handleClose">
      <div class="form-box">
        <div class="input-item" style="width: 48%;display:inline-block">
          <el-input v-model="formData.name" maxlength="25" placeholder="姓名"></el-input>
        </div>
        <div class="input-item" style="width: 48%;display:inline-block;margin-left: 3%">
          <el-input v-model="formData.phone" placeholder="手机号"></el-input>
        </div>
        <div class="input-item text-wrapper">
          <p @click="bindAdd(false)" v-if="!cityData.province.id">请选择省/市/区/街道</p>
          <p @click="bindAdd(true)" v-if="cityData.province.id" style="color: #333">
            <span v-if="cityData.province.name">{{ cityData.province.name }}</span>
            <span v-if="cityData.city.name">/{{ cityData.city.name }}</span>
            <span v-if="cityData.county.name">/{{cityData.county.name}}</span>
            <span v-if="cityData.district.name">/{{ cityData.district.name }}</span>
          </p>
          <div class="select-wrapper" v-if="isShowSelect">
            <div class="title-box" v-if="!cityData.province.id">选择省/自治区</div>
            <div class="title-box" v-if="cityData.step == 2">
              <span>{{ cityData.province.name }}</span>选择市
            </div>
            <div class="title-box" v-if="cityData.step == 3">
              <span>{{ cityData.county.name }}</span>
              <span>{{ cityData.city.name }}</span>选择区县
            </div>
            <div class="title-box" v-if="cityData.step == 4 && !stepStop">
              <span>{{ cityData.city.name }}</span>
              <span>{{ cityData.county.name }}</span>请选择配送区域
            </div>
            <div class="label-txt">
              <span v-for="(item,index) in cityData.list" :key="index" @click="bindCity(item)" :class="{on:cityData.pid == item.id}">{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="input-item">
          <el-input type="textarea" rows="3" v-model="formData.con" placeholder="详细地址"></el-input>
        </div>
        <div class="input-item">
          <el-checkbox v-model="formData.checked">设为默认</el-checkbox>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="bindSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 发票说明弹窗 -->
    <el-dialog
      :title="agrementTtile"
      :visible.sync="invoiceVisible"
      width="900px"
      center
      :before-close="dialogClose">
      <div class="invoice_container">
        <div class="invoice_description">
          <div v-html="protocal"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="width: 180px;" @click="dialogClose">确 定</el-button>
      </span>
    </el-dialog>
     <!-- 发票信息弹窗 -->
    <el-dialog
      title="发票信息"
      :visible.sync="invoiceDialog"
      width="600px"
      :before-close="closeInvoice">
      <div class="invoice_data_container">
       <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" @submit.native.prevent>
        <el-form-item label="发票类型：">
          <div class="acea-row">
             <div v-for="(item, index) in invoiceTypeList" :key="index" class="invoice_item" :class="ruleForm.receipt_type == item.type ? 'checked' : ''">
              <div class="acea-row cont" @click="changeInvoiceType(item)" >
                <div class="acea-row row-middle name">{{item.name}}</div>
                <div class="iconfont icon-xuanzhong4"></div>
              </div>
            </div>
          </div>
          <div class="invoice_type_info">
                {{ruleForm.receipt_type == 1 ?
                '默认发送至所提供的电子邮件' :
                '纸质发票开出后将以邮寄形式交付'}}
              </div>
        </el-form-item>
        <el-form-item label="发票抬头：">
          <div class="acea-row">
             <div v-for="(item, index) in invoiceHeaderList" :key="index" class="invoice_item" :class="ruleForm.receipt_title_type == item.type ? 'checked' : ''">
              <div class="acea-row cont" @click="changeInvoiceHeader(item)" >
                <div class="acea-row row-middle name">{{item.name}}</div>
                <div class="iconfont icon-xuanzhong4"></div>
              </div>
            </div>
            <div style="margin-top: 20px; width: 100%;">
              <el-input v-model="ruleForm.receipt_title" placeholder="请填写发票抬头"></el-input>
            </div>
          </div>
        </el-form-item>
         <el-form-item v-show="ruleForm.receipt_title_type == '2'" label="单位税号：">
          <el-input v-model="ruleForm.duty_paragraph" placeholder="请填写纳税人识别号"></el-input>
        </el-form-item>
        <el-form-item label="收票邮箱：" prop="email">
          <el-input v-model="ruleForm.email" placeholder="请填写收票人邮箱"></el-input>
        </el-form-item>
        <div v-show="ruleForm.receipt_title_type == '2' && ruleForm.receipt_type == '2'">
          <el-form-item label="开户银行：">
            <el-input v-model="ruleForm.bank_name" placeholder="请填写开户银行"></el-input>
          </el-form-item>
          <el-form-item label="银行账号：">
            <el-input v-model="ruleForm.bank_code" placeholder="请填写银行账号"></el-input>
          </el-form-item>
          <el-form-item label="企业地址：">
            <el-input v-model="ruleForm.address" placeholder="请填写企业地址"></el-input>
          </el-form-item>
          <el-form-item label="企业电话：">
            <el-input v-model="ruleForm.tel" placeholder="请填写企业电话"></el-input>
          </el-form-item>
        </div>
      </el-form>
      </div>
      <span slot="footer" center class="dialog-footer">
         <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirmInvoice">确 定</el-button>
          <el-button @click="closeInvoice">取 消</el-button>
      </span>
      </span>
    </el-dialog>
    <chat-room
      v-if="chatPopShow"
      :chatId="mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
import ChatRoom from "@/components/ChatRoom";
export default {
  name: "order_confirm",
  auth: "guest",
  components: { ChatRoom },
  data() {
    return {
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() < Date.now()
        }
      },
      cascaderProps:{
        value:'id',
        label:'name',
        lazy: true,
        lazyLoad:this.lazyLoad
      },
      upLoadUrl: process.env.BASE_URL + "/api/upload/image/file",
      msgObj: {},
      enabledPlatformCoupon: false,
      platformCoupon: [],
      total_platform_coupon_price: 0,
      total_coupon: 0,
      plantCoupon: false,
      chatPopShow: false,
      dialogVisible: false,
      invoiceVisible: false,
      invoiceDialog: false,
      isShowSelect: false,
      virtualIndex: 0,
      protocal: "",
      order_model: 2,
      allow_address: true,
      deliveryName: '快递配送',
      agrementTtile: '发票说明',
      order_extend: [],
      extend: {},
      timeVal: '',
      dateVal: '',
      imgUrl: [],
      uploadLimit: 10,
      currentLimit: 10,
      pics: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      ruleForm: {
        receipt_title_type: 1, //发票抬头类型
        receipt_type: 1, //发票类型
        receipt_title: "", //发票抬头
        duty_paragraph: "", //税号
        email: "", //邮箱
        bank_name: "", //开户银行
        bank_code: "", //银行账号
        address: "", //企业地址
        tel: "", //电话
      },
      invoiceData: {},
      post: {
        real_name: '',
        phone: ''
      }, //核销员信息
      invoiceTypeList: [
        {
          type: '1',
				  name: '增值税电子普通发票'
				},
				{
				  type: '2',
				  name: '增值税专用发票'
				}
			],
      invoiceHeaderList: [
        {
          type: '1',
				  name: '个人'
				},
				{
				  type: '2',
				  name: '企业'
				}
      ],
      rules: {
        receipt_title: [
          {required: true, message: '请填写发票抬头', trigger: 'blur'}
        ]
      },
      formData: {
        name: '',
        phone: '',
        con: '',
        checked: false
      },
      cityData: {
        pid: 0,
        step: 1,
        list: [],
        con: '',
        province: {},
        city: {},
        county: {},
        district: {}
      },
      cityDataOrg:[],
      addressList: [],
      current: 0,
      news: 1,
      cartInfo: [],
      addressId: 0,
      useIntegral: false,//是否使用积分
      couponId: 0,
      subCoupon: {},
      order_type: '',
      computeData: {},
      userInfo: {},
      mark: {},//备注信息
      totalNum: 0,
      priceGroup: {},
      totalPrice: 0,//最终商品金额；
      seckillId: 0,
      isShow:false,
      freight: 0,
      orderStatus: '',
      proPrice: '',
      coupon_price: '',
      addressInfo: {},
      open_integral: 0,
      coupon_number: 0,
      coupon_amount: '',
      integral_count: '',
      use_integral: false,
      integral_price: 0, //积分抵扣金额
      selectedIndex: -1,
      selectedArr: [],
      stepStop: false,
      couponIndex: 0, //选择商铺优惠券索引
      coupon: {
        status: false,
        list: [],
      },
      activeIndex: "",
      take: [],
      invoiceIndex: '',
      order_key: '',
      modifyInvoice: false,
      isAgree: false,
      isPresell: false,
      mer_id: 0,
      is_take: false,
      cityShow:1,
      order_form: [],
      service_type: 1,
      formId: ""
    }
  },
  async asyncData({app, query}) {
    let list = {data:[]};
    try{
      [list] = await Promise.all([
        app.$axios.get('/api/user/address/lst', {
          page: 1,
          limit: 50
        }),
      ])
    }catch (e){
    }
    return {
      addressList: list.data,
      cartId: query.cartId,
      news: query.new ? 1 : 0,
      service_type: query.serviceType,
      formId: query.formId,
      addressId: query.addressId || 0,
      mer_id: query.merId
    }
  },
  fetch({store}) {
    store.commit('isHeader', true);
    store.commit('isFooter', true);
  },
  head() {
    return {
      title: "确认订单-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getAddressList();
    this.getCityList();
    this.getUserInfo();
    this.getInvoiceData();
  },
  methods: {
     // 具体日期
    onchangeDate(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    onchangeTime(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    getTime(index){ 
			this.virtualIndex = index;
		},
    chatShow(mer_id) {
      if(this.$auth.loggedIn){
        this.mer_id = mer_id;
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    // 表单重置
    formReset() {
      this.formData.name = ''
      this.formData.phone = ''
      this.formData.con = ''
      this.formData.checked = false
      this.cityData.province = {}
      this.cityData.city = {}
      this.cityData.district = {}
      this.cityData.step = 1
      this.cityData.pid = 0
      this.selectedArr = []
    },
    handleClose() {
      this.formReset()
      this.dialogVisible = false
    },
    dialogClose() {
      this.invoiceVisible = false

    },
    closeInvoice (){
      this.invoiceDialog = false
      if(!this.modifyInvoice){
        this.cartInfo[this.invoiceIndex]['invoiceData'] = {}
        this.cartInfo[this.invoiceIndex].invoiceData.checked = false
      }
    },
    changeIsAgree: function(e) {
			this.isAgree = !this.isAgree;
		},
    bindAdd(isReset) {
      if(isReset){this.cityData.step = 1;this.stepStop = false}
      this.isShowSelect = !this.isShowSelect
      if(this.cityData.step == 4 || this.stepStop){
        return
      } else {
        this.cityData.city = {}
        this.cityData.district ={}
        this.cityData.province ={}
        this.cityData.county ={}
        this.getCityList(0,null)
      }
    },
    //获取用户信息
    getUserInfo(){
      this.$axios.get(`/api/user`).then(res => {
        this.userInfo = res.data
      })
    },
    /*获取发票信息*/
    getInvoiceData(){
      this.$axios.get('/api/agreement/'+'sys_receipt_agree').then(res => {
        this.protocal = res.data.sys_receipt_agree
        this.agrementTtile ='发票说明'
      })
    },
    /*预售协议*/
    getPresellAgree(){
        this.invoiceVisible = true;
        this.$axios.get('/api/store/product/presell/agree').then(res => {
        this.protocal = res.data.sys_product_presell_agree
        this.agrementTtile ='预售协议'
      })
    },
    /*平台优惠券*/
    getPlantCoupon(){
        this.invoiceVisible = true;
        this.$axios.get('/api/agreement/sys_coupon_agree').then(res => {
        this.protocal = res.data.sys_coupon_agree
        this.agrementTtile ='平台优惠券'
      })
    },
    /*选择快递配送方式*/
    getDevelivery(item,v,index,i){
      if((i == 0 && !item.order.isTake) || (i == 1 && item.order.isTake)) return;
      this.$set(item.order, 'isTake', v);
      this.activeIndex = index;
      this.getData(item);
    },
    /*不开发票*/
    noInvoice(item){
      this.$set(this.cartInfo[this.invoiceIndex], 'invoiceData', {checked: false})
      let value = {};
      this.getInvoiceDatas(value)
    },
    /*打开发票弹窗*/
    showInvoicePupon(item,index){
      this.isPresell = false;
      this.invoiceDialog = true;
      item.invoiceData.checked = true;
      this.invoiceIndex = index
    },
    /*选择发票类型*/
    changeInvoiceType(item) {
      this.ruleForm.receipt_type = item.type
      if(this.ruleForm.receipt_type == 2){
        this.invoiceHeaderList = [{
				  type: '2',
				  name: '企业'
				}]
        this.ruleForm.receipt_title_type = 2
      }else{
         this.invoiceHeaderList = [{
          type: '1',
				  name: '个人'
				},
				{
				  type: '2',
				  name: '企业'}]
      }
    },
    /*选择发票抬头*/
    changeInvoiceHeader(item) {
      this.ruleForm.receipt_title_type = item.type
    },
    beforeUpload(file) {
		  const isImage = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;
		  if (!isImage) {
		    this.$message.error("上传图片只能是 JPG、PNG 格式!");
		  }
		  if (!isLt2M){
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
		  return isImage && isLt2M;
		},
    /** 删除图片*/
		DelPic: function(index) {
			let that = this,
			pic = this.pics[index];
			that.pics.splice(index, 1);
			that.$set(that, 'pics', that.pics);
		},
    //上传图片前的图片验证回调
    beforeAvatarUpload(file) {
      //图片格式
      const isJPG = file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/jpeg';
      //图片大小
      const isLt2M = file.size / 1024 / 1024 <= 2;
      if (!isJPG) {
        this.$message.error('上传图片只能为jpg/jpeg或png格式');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB');
      }
      const _this = this;
      return isJPG && isLt2M;
    },
    handleExceed() {
      this.$message.warning("最多上传10张图片");
    },

    handleRemove(file, fileList) {
      this.pics = [];
      fileList.forEach(item => {
        this.pics.push(item.response.data.url);
      });
      this.currentLimit = this.uploadLimit - this.pics.length - this.imgUrl.length || -1;
    },
    handleRemove1(index) {
      this.imgUrl.splice(index, 1)
      this.currentLimit = this.uploadLimit - this.imgUrl.length || -1;
    },
    // 文件上传失败时的钩子
    handleError(err, file, fileList) {
    },
    handleSuccess(response) { 
      if (response.status === 200) {
        this.pics.push(response.data.path);
      } else if (response.status === 400) {
        this.$message.error(response.msg);
      }
    },
    /*提交发票信息*/
    confirmInvoice() {
      let that = this;
      let value = that.ruleForm;
			if (!value.receipt_title) return Message.error('请填写发票抬头');
			if (!value.email) return Message.error('请填写邮箱');
			if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value.email)) return Message.error('请输入正确的邮箱');
			if(value.receipt_title_type == 2){
				if (!value.duty_paragraph) return Message.error('请填写税号');
				if(value.receipt_type == '2'){
						if (!value.bank_name) return Message.error('请填写开户行');
						if (!value.bank_code) return Message.error('请填写银行账号');
						if (!value.address) return Message.error('请填写企业地址');
						if (!value.tel) return Message.error('请填写企业电话');
						if(!/^(\d{9}|\d{14}|\d{18})$/.test(value.bank_code)){
							return Message.error('请输入正确的银行账号');
						}

						if(!/(^(\d{3,4})?\d{7,8})$|(13[0-9]{9})/.test(value.tel)){
							return Message.error('请输入正确的电话号码');
						}
					}
				}
				value.mer_id = that.cartInfo[that.invoiceIndex]['mer_id']
        that.cartInfo[that.invoiceIndex]['invoiceData'] = value
        that.getInvoiceDatas(value)
        that.invoiceDialog = false
    },
    /*获取开票信息*/
    getInvoiceDatas(value){
      let that = this;
      that.invoiceData = {}
      that.cartInfo.forEach((item, i) => {
        if (value.mer_id && item.mer_id == value.mer_id) {
					that.$set(that.cartInfo[i], 'invoiceData', value)
          that.$set(that.cartInfo[i]['invoiceData'], 'checked', true)
						let mer_id = value.mer_id
				  	that.invoiceData[mer_id] = value;
					}

			});
    },
    /*修改发票信息*/
    changeInvoiceData(item){
      this.invoiceDialog = true;
      this.ruleForm = item.invoiceData
      this.modifyInvoice = true
    },
    getData(data){
      this.cartInfo[this.activeIndex] = data
			if (data.order.isTake) {
					this.take.push(data.mer_id)
				} else {
					this.take.forEach((item, i) => {
						if (data.mer_id == item) {
							this.take.splice(i, 1)
						}
					})
				}
				this.getConfirm(this.addressId);
    },
    getCoupon(coupon) {
      if (coupon.checked) {
        this.subCoupon[coupon.mer_id].forEach((item, i) => {
          if (coupon.coupon_user_id == item) {
            this.subCoupon[coupon.mer_id].splice(i, 1)
          }
        })
      } else {
        this.subCoupon[coupon.mer_id].push(coupon.coupon_user_id)
      }
      this.getConfirm(this.addressId)
    },
    getCityList(pid,fun) {
      // this.cityData.list = []
      // console.log(res.data);
      pid = pid || 0
      this.$axios.get('/api/v2/system/city/lst/'+pid).then(res => {
        this.cityDataOrg = res.data
        this.cityData.list = res.data
        fun && fun()
      })
    },
    addAddress() {
      this.dialogVisible = true
      this.getConfirm()
    },
    // 选择城市
    bindCity(item) {
      let that = this;
      if (that.cityData.step == 4) {
        that.cityData.district = item;
        that.selectedArr.push(item);
        that.isShowSelect = false
      } else {
        if (that.cityData.step == 1) {
          that.cityData.province = item;
          that.getCityList(item.id,null);
          that.selectedArr = [item];
          that.cityData.step++;
          return
        }
        if (that.cityData.step == 2) {
          that.cityData.city = item
          that.getCityList(item.id,null)
          that.cityData.step++
          that.selectedArr.push(item);
          return
        }
        if(that.cityData.step == 3){
          that.cityData.county = item
          that.selectedArr.push(item);
          that.cityData.step++
          that.getCityList(item.id,function(){
            if(that.cityData.list && that.cityData.list.length){
              that.stepStop = false
              return
            }else{
              that.stepStop = true
              that.isShowSelect = false
              return
            }
          })
        }
      }
    },
    bindSubmit() {
      if (!this.formData.name) {
        return Message.error('请填写姓名')
      }
      if (!this.formData.phone || !/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formData.phone)) {
        return Message.error('请填写正确的手机号码')
      }
      if (!this.cityData.province.name) {
        return Message.error('请选择省市区')
      }
      if (!this.formData.con) {
        return Message.error('请填写详细地址')
      }
      this.$axios.post('/api/user/address/create', {
        area: this.selectedArr,
        city_id: this.cityData.city.city_id,
        is_default: this.formData.checked ? 1 : 0,
        real_name: this.formData.name,
        phone: this.formData.phone,
        detail: this.formData.con,
        address_id: 0
      }).then(res => {
        this.addressId = res.data.id
        this.dialogVisible = false
        this.getAddressList()
        this.formReset()
        return Message.success('添加成功')
      }).catch(err => {
        return Message.error(err)
      })
    },
    SubOrder(event) {
      let that = this;
      if (that.orderStatus == 'noAddress')return Message.error('请选择收货地址')
      if (that.orderStatus != 'finish' && that.order_model == 0)return Message.error('收货地址不在配送区域')
      if (that.order_type == 2 && !that.isAgree && that.cartInfo[0].list[0].productPresell.presell_type == 2) {
				return Message.error('请阅读并勾选协议，否则无法进行操作');
			} 
      if(that.is_take){
        if(!that.post.real_name){
          return Message.error('请输入收货人姓名')
        }
        if(!that.post.phone){
          return Message.error('请输入收货人电话')
        }
        if(that.post.phone && !/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.post.phone)){
          return Message.error('电话格式不正确')
        }
      }
      let btn = event.target;
      btn.disabled = true;
      let extendArr = that.formId == 1 ? JSON.parse(localStorage.getItem('extendInfo')) : that.getExtendData();
      let data = {
        use_coupon: that.subCoupon,
        order_type: that.order_type,
        cart_id: that.cartId.split(","),
        receipt_data: that.invoiceData,
        key: that.order_key,
        mark: that.msgObj,
        takes: that.take,
        pay_type: 'pc',
        use_integral: that.use_integral,
        return_url: 'http://' + window.location.host + '/user/order_list?type=1',
        extend: extendArr,
      };
      data.takes=[]
      if(that.order_model != 4) {
        that.cartInfo.map(el => {
					if (el.isTake == 1 || el.order.isTake) {
						data.takes.push(el.mer_id)
					}
				})
        data.post = that.post
      }
      if(that.service_type == 1){ //到店
        data.post = JSON.parse(localStorage.getItem('customerInfo'));
        data.takes = [that.mer_id]
        
      }else{
        data.address_id = this.addressId
      }
      that.$axios.post('/api/v2/order/create', data).then(res => {
        if (that.totalPrice <= 0) {
          that.$router.replace({path: '/user/order_list?type=1'});
        } else {
          that.$router.replace({path: '/payment', query: {result: res.data.order_id}});
        }
      }).catch(err => {
        btn.disabled = false;
        return Message.error(err);
      })
    },
    getExtendData() {
      let that = this
      for(var i=0; i<that.order_form.length; i++){
        let curdata = that.order_form[i]
        if (curdata.name === 'uploadPicture') {
          curdata.values = that.pics;
          curdata.value = that.pics;
        }
        if (curdata.name === 'radios') {
          curdata.value = curdata.values.val
        }
        if (['radios'].indexOf(curdata.name) == -1 && (curdata.titleShow.val || (['uploadPicture','citys','timeranges','checkboxs','uploadPicture','dateranges'].indexOf(curdata.name) == -1 && curdata.values && curdata.values.trim())) || (['citys','timeranges','checkboxs','dateranges'].indexOf(curdata.name) != -1 && curdata.values[0])) {
          if ((curdata.name === 'texts' && curdata.valConfig.tabVal == 0) || ['dates','times'].indexOf(curdata.name) != -1) {
            if (!curdata.values || (curdata.values && !curdata.values.trim())){
              return Message.error(`请填写${curdata.titleConfig.value}`)
            }else{
              curdata.value = curdata.values
            }
          }
          if(curdata.name === 'timeranges'){
            if(!curdata.values[0]){
              return Message.error(`请选择${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values.join('-');
            }
          }
          if(curdata.name === 'dateranges'){
            if(!curdata.values[0]){
              return Message.error(`请选择${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values[0]+'/'+curdata.values[1]
            }
          }
          if (curdata.name === 'checkboxs') {
            if (!curdata.values.length) {
              return Message.error(`请选择${curdata.titleConfig.value}`);
            }else{
              let obj = '';
              curdata.values.forEach(j=>{
                obj = obj + (obj?',':'') + j.val
              })
              curdata.value = obj;
            }
          }
          if (curdata.name === 'citys') {
            if (!curdata.values.length) {
              return Message.error(`请选择${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values.join(',');
            }
          }
          if (curdata.name === 'selects') {
            if (typeof curdata.values == 'string' && curdata.values =='') {
              return Message.error(`请选择${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.wordsConfig.list[curdata.values].val
            }
          }
          if (curdata.name === 'texts' && curdata.valConfig.tabVal == 4) {
            if (!curdata.values || (curdata.values && !curdata.values.trim())){
              return Message.error(`请填写${curdata.titleConfig.value}`)
            }else if (curdata.values <= 0) {
              return Message.error(`请填写大于0的${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values;
            }
          }
          if (curdata.name === 'texts' && curdata.valConfig.tabVal == 3) {
            if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(curdata.values)) {
              return Message.error(`请填写正确的${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values;
            }
          }
          if (curdata.name === 'texts' && curdata.valConfig.tabVal == 1) {
            if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(curdata.values)) {
              return Message.error(`请填写正确的${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values;
            }
          }
          if (curdata.name === 'texts' && curdata.valConfig.tabVal == 2) {
            if (!/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/i.test(curdata.values)) {
              return Message.error(`请填写正确的${curdata.titleConfig.value}`);
            }else{
              curdata.value = curdata.values;
            }
          }
          if (curdata.name === 'uploadPicture') {
            curdata.values = that.pics;
            if (!curdata.values.length) {
              return Message.error(`请上传${curdata.titleConfig.value}`);
            }
          }
        }
        that.extend[curdata.key] = curdata.value
      }
      let extendArr = []
      extendArr.push(this.extend)
      return extendArr
    },
    /**获取备注 */
    getRemarks() {
      let that = this;
      that.cartInfo.forEach((item, i) => {
        let mer_id = item.mer_id
        if (item.mark) that.mark[mer_id] = item.mark;
      });
      return this.mark;
    },
    changeIntegral: function(e) {
      this.use_integral = !this.use_integral;
      this.getConfirm(this.addressId);
    },
    getConfirm() {
      let that = this;
      let data = {
					cart_id: that.cartId.split(","),
					takes: that.take,
					use_coupon: that.subCoupon,
					use_integral: that.use_integral
				}
				if(that.order_model != 4)data.takes = that.take
				if(that.service_type == 1){ //到店
					data.takes = [that.mer_id]
				}else{
					data.address_id = this.addressId
				}
      that.$axios.post("/api/v2/order/check", data).then(res => {
        let conponNum = 0
        let totalNum = 0;
        that.is_take = false;
        // 默认选中
        res.data.order.forEach(el => {
          if(el.order.isTake == 1)that.is_take = true
          el.isTake = 0
          el.invoiceData = {
            checked: false
          }
          that.subCoupon[el.mer_id] = []
          el.coupon.forEach(coupon => {
            if (coupon.checked) {
              that.subCoupon[el.mer_id].push(coupon.coupon_user_id)
            }
          })
          totalNum += el.order.total_num
        })
        that.subCoupon['0'] = []
				if(res.data.platformCoupon.length > 0){
					res.data.platformCoupon.forEach(el => {
						if (el.checked) {
							that.subCoupon[el.mer_id] = []
							that.subCoupon[el.mer_id].push(el.coupon_user_id)
						}
					})
				}
        that.$set(that, 'coupon_price', res.data.order_coupon_price);
        that.$set(that, 'total_coupon', res.data.total_coupon);
        that.$set(that, 'cartInfo', res.data.order);
        that.$set(that, 'order_type', res.data.order_type);
        that.$set(that, 'integral_count', res.data.order_total_integral);
        that.$set(that, 'integral_price', res.data.order_total_integral_price);
        that.$set(that, 'open_integral', res.data.openIntegral);
        that.$set(that, 'use_integral', res.data.useIntegral);
        that.$set(that, 'order_extend', (that.order_extend && that.order_extend.length>0) ? that.order_extend : res.data.order_extend);
        that.$set(that, 'enabledPlatformCoupon', res.data.enabledPlatformCoupon);
        that.$set(that, 'platformCoupon', res.data.platformCoupon);
        that.$set(that, 'total_platform_coupon_price', res.data.total_platform_coupon_price);
        that.order_model = res.data.order_model;
        that.allow_address = res.data.allow_address;
        that.deliveryName = res.data.order_model == 0 ?  '快递配送' : '虚拟发货' 
        that.totalPrice = res.data.order_price
        that.order_key = res.data.key
        that.orderStatus = res.data.status
        that.proPrice = res.data.total_price
        that.priceGroup = res.data
        that.coupon_price = conponNum
        that.coupon_price = conponNum
        that.order_type = res.data.order_type
        that.totalNum = totalNum
        if(res.data.mer_form_id && !that.order_form.length && res.data.mer_form_info && res.data.mer_form_info.value){
          that.getFormData(res.data.mer_form_info.value);
        }
        
      }).catch(err=>{
        Message.error(err);
        this.$router.go(-1);
      })
    },
    getFormData(formData){
      let formDatas = this.objToArr(formData);
      formDatas.forEach((item, index, arr)=>{
        item.value = '';
        if(item.name == 'texts'){
          if(item.defaultValConfig.value){
            this.$set(item, 'values' , item.defaultValConfig.value);
          }else{
            this.$set(item, 'values' , '');
          }
        }else if(item.name == 'radios'){
          this.$set(item, 'values' , item.wordsConfig.list[0]);
        }else if(item.name == 'uploadPicture' || item.name == 'checkboxs'){
          this.$set(item, 'values' , []);
        }else if(['timeranges','dateranges'].indexOf(item.name) != -1){
          if(item.valConfig.tabVal==0){
            if(item.valConfig.tabData==0){
              let current = '';
              if(item.name == 'timeranges'){
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm')
              }else{
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy/MM/dd')
              }
              this.$set(item, 'values' , [current,current]);
            }else{
              this.$set(item, 'values' , item.valConfig.specifyDate);
            }
          }else{
            this.$set(item, 'values' , ['','']);   
          }
        }else{
          if(['times','dates'].indexOf(item.name) != -1){
            if(item.valConfig.tabVal==0){
              if(item.valConfig.tabData==0){
                if(item.name == 'times'){
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm'));
                }else{
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy-MM-dd'));
                }
              }else{
                this.$set(item, 'values' , item.valConfig.specifyDate);
              }
            }else{
              this.$set(item, 'values' , '');
            }
          }else{
            this.$set(item, 'values' , '');
          }
        }
      })
      this.order_form = formDatas;
    },
    // 对象转数组
    objToArr(data) {
      let obj = Object.keys(data);
      let m = obj.map(key => data[key]);
      return m;
    },
    changeRegion(item){
      this.cityShow = item.valConfig.tabVal;
    },
    lazyLoad(node, resolve){
      let id = 0
      if(node.data){
        id = node.data.id
      }
      this.$axios.get(`api/v2/system/city/lst/${id}`).then((res) => {
        res.data.map(item=>{
          item.leaf = (this.cityShow==0 && item.level >=2) || (this.cityShow==1 && item.level >=3) || (this.cityShow==2 && item._loading == undefined);
        })
        resolve(res.data);
      }).catch(err=>{
        resolve();
      });
    },
    getAddressList() {
      let that = this;
      that.$axios.get('/api/user/address/lst', {
        params: {
          page: 1,
          limit: 50
        }
      }).then(res => {
        that.addressList = res.data.list;
        that.addressInfo = res.data.list.length > 0 ? res.data.list[0] : {};
        that.addressList.forEach((item, index) => {
          if (item.is_default) {
            that.addressId = item.address_id;
            that.current = index;
            that.post = {
              real_name: item.real_name,
              phone: item.phone
            }
          } else {
            that.addressId = that.addressList[0].address_id;
            that.current = 0;
          }
        });
        that.getConfirm();
      })
    },
    tapCurrent(index, item) {
      this.current = index;
      this.addressId = item.address_id;
      this.getConfirm();
    },
    open() {
      this.isShow = true
    },
    close() {
      this.isShow = false
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-radio__inner{
  width: 18px;
  height: 18px;
}
::v-deep .el-checkbox__inner{
  width: 16px;
  height: 16px;
}
::v-deep .el-radio__inner::after{
  width: 7px;
  height: 7px;
}
::v-deep .el-checkbox__inner::after{
  left: 5px;
  top:2px
}
.input-item {
  margin-bottom: 20px;
}
.item-require{
	color: #e93323;
	position: absolute;
  left: -8px;
}
.text-wrapper {
  position: relative;
  height: 40px;
  line-height: 40px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  box-sizing: border-box;
  border-radius: 4px;
  color: #cfcfcf;
  .select-wrapper {
    z-index: 10;
    position: absolute;
    left: 0;
    top: 45px;
    width: 100%;
    padding: 0 15px;
    background: #fff;
    border: 1px solid #E93323;
    border-radius: 4px;
    line-height: 2;
    .title-box {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #EFEFEF;
      color: #E93323;
      font-size: 14px;
      span {
        margin-right: 8px;
        color: #666666;
      }
    }
    .label-txt {
      margin: 8px 0 18px;
      color: #666666;
      font-size: 14px;
      span {
        margin-right: 10px;
        cursor: pointer;
        &.on {
          color: #E93323;
        }
      }
    }
  }
}
.order_confirm {
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .address {
    background-color: #fff;
    .title {
      height: 64px;
      font-size: 18px;
      padding: 0 28px;
      line-height: 64px;
    }
    .lines {
      width: 100%;
      height: 4px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .list {
      padding: 0 25px 26px 25px;
      height: 217px;
      overflow: hidden;
      &.on {
        height: auto;
      }
      .item {
        width: 250px;
        height: 170px;
        border: 1px solid #EAEAEA;
        padding: 22px 27px;
        overflow: hidden;
        margin: 30px 0 0 30px;
        position: relative;
        cursor: pointer;
        &.on {
          border-color: #E93323;
        }
        .icon-xuanzhong4 {
          position: absolute;
          right: -4px;
          bottom: -4px;
          font-size: 27px;
        }
        .default {
          position: absolute;
          width: 56px;
          height: 23px;
          font-size: 12px;
          color: #fff;
          text-align: center;
          line-height: 23px;
          top: 0;
          right: 0;
        }
        &.add {
          text-align: center;
          .iconfont {
            font-size: 35px;
            color: #BFBFBF;
            margin-top: 25px;
          }
          .tip {
            color: #C8C8C8;
            margin-top: 8px;
          }
        }
        .name {
          font-size: 16px;
        }
        .phone {
          margin-top: 9px;
        }
        .details {
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }
  .develivery{
    margin-left: 30px;
  }
  .develivery_take{
      margin-left: 100px;
      font-size: 14px;
      font-weight: normal;
      padding-left: 16px;
      position: relative;
      top: -10px;
      padding-bottom: 8px;
      span{
        color: #E93323;
        font-size: 14px;
        position: absolute;
        top: 3px;
        left: 0;
      }
  }
  .deliviery_item{
    margin-right: 12px;
    box-sizing: border-box;
    cursor: pointer;
    .cont{
      position: relative;
      height: 32px;
      border: 1px solid #d3d3d3;
      display: flex;
      overflow: hidden;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
      }
      .name{
        padding: 0 50px;
        font-size: 14px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -3px;
        font-size: 22px;
      }
  }
  .isShow {
    width: 100%;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #707070;
    cursor: pointer;
    .iconfont {
      margin-left: 8px;
      font-size: 12px;
    }
  }
  .wrapper {
    .wrapper_count{
      background-color: #ffffff;
      padding-bottom: 82px;
      &:last-child{
        margin-top: 14px;
      }
    }
    .checkbox-wrapper{
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-right: 10px;
      .icon {
        border-radius: 2px;
        width: 14px;
        height: 14px;
        border-color: #9B9B9B;
      }
    }
    .money_count{
      margin-right: 30px;
    }
    .money_down{
      color: #fff;
      font-size: 12px;
      line-height: 17px;
      height: 17px;
      background-color: #E93323;
      width: 34px;
      text-align: center;
      border-radius: 3px;
      margin-right: 10px;
    }
    .money_final{
      margin-top: 6px;
      color: #666666;
      font-size: 14px;
    }
    .integral_count{
      padding: 23px 24px;
      border: 1px solid #EFEFEF;
      display: flex;
      justify-content: space-between;
      .integral_title{
        font-size: 18px;
      }
      .money{
        margin-top: 15px;
        align-items: center;
      }
    }
    .title {
      height: 64px;
      line-height: 64px;
      padding: 0 28px;
      font-size: 18px;
    }
    .cartCount {
      padding: 0 32px 26px;
      margin-bottom: 20px;
      border: 1px solid #EFEFEF;
    }
    .cartInfo {
      padding-top: 29px;
      margin-bottom: 25px;
      .item{
        margin-bottom: 15px;
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
    .storeInfo {
      height: 60px;
      border-bottom: 1px solid #EFEFEF;
      position: relative;
      .qrcode {
        position: absolute;
        background: #fff;
        right: -15px;
        display: none;
        z-index: 10;
        bottom: 60px;
        border: 1px solid #ddd;
        width: 110px;
        height: 110px;
        img{
          width: 100%;
        }
      }
      .name {
        color: #666666;
      }
      .service {
        cursor: pointer;
        .iconfont {
          color: #E93323;
          font-size: 18px;
        }
        &:hover {
          + .qrcode {
            display: inline;
          }
        }
      }
    }
    .order {
      width: 1160px;
      margin: 0 auto;
      .list {
        .item {
          // margin-bottom: 26px;
          .txtPic {
            .pictrue {
              width: 62px;
              height: 62px;
              position: relative;
              span {
                display: block;
                width: 100%;
                text-align: center;
                font-size: 12px;
                line-height: 18px;
                background: rgba(0,0,0,.5);
                position: absolute;
                left: 0;
                bottom: 0;
                color: #fff;
              }
              img {
                width: 100%;
                height: 100%;
              }
            }
            .text {
              max-width: 500px;
              margin-left: 10px;
              .name {
                width: 100%;
              }
              .info {
                margin-top: 12px;
                color: #919191;
              }
              .err-txt{
                margin-top: 12px;
                color: #E93323;
                align-items: center;
                .txt{
                  display: inline-block;
                }
                .icon-tishi{
                  position: relative;
                  top: 1px;
                }
              }
            }
          }
          .ship_date{
            margin-top: 10px;
            color: #FD6523;
          }
          .font-color {
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
            text-align: right;
          }
          .money {
            font-size: 16px;
          }
          .num {
            margin-left: 6px;
            font-size: 12px;
          }
          .svip-image{
            width: 35px;
            height: 15px;
            margin-left: 5px;
            img{
              width: 35px;
              height: 15px;
            }
          }
        }
      }
      .coupon {
        border-top: 1px solid #EFEFEF;
        .icon-wenhao{
            color: #fff;
            display: inline-block;
            width: 14px;
            height: 14px;
            text-align: center;
            line-height: 14px;
            background-color: #236FE9;
            border-radius: 100%;
            font-size: 8px;
            margin-right: 5px;
          }
        .plantTitle{
          font-size: 18px;
          position: relative;
          .title{
            align-items: center;
            padding: 0;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
        }
        .couponTitle {
          font-size: 16px;
          padding: 26px 0;
          position: relative;
          .title{
            align-items: center;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
          .couponPrice {
            font-size: 16px;
            font-weight: bold;
          }
          .couponPriceNo {
            font-size: 14px;
          }
        }
        &.invoice{
          padding-bottom: 26px;
          .couponTitle{
            padding-bottom: 15px;
          }
        }
        .invoice_info{
          font-size: 14px;
          margin-left: 102px;
          color: #236FE9;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .invoice_data{
          position: absolute;
          right: 0;
          bottom: 20px;
          font-size: 16px;
          display: flex;
          .data_item{
            margin-left: 20px;
            &.modify{
              cursor: pointer;
            }
          }
        }
        .couponList {
          .item {
            height: 40px;
            width: 182px;
            border: 1px solid #E93323;
            margin: 0 15px 15px 0;
            position: relative;
            cursor: pointer;
            overflow: hidden;
            color: #E93323;
            &.disabled{
              pointer-events:none;
              opacity: .6;
            }
            &.grey {
              border-color: #B4B4B4;
            }        
            .iconfont {
              position: absolute;
              right: -2px;
              bottom: -2px;
              font-size: 20px;
            }
            .name {
              width: 70px;
              height: 100%;
              color: #fff;
              text-align: center;
              line-height: 40px;
              background-color: #E93323;
              &.grey {
                background-color: #B4B4B4;
              }
            }
            .money {
              width: 110px;
              text-align: center;
              color: #E93323;
              &.grey {
                color: #B4B4B4;
              }
            }
            &.item5 {
              border-color: #333;
              .name {
                background-color: #333;
                color: #FDD7B4;
              }
              .money {
                color: #333;
              }
              .font-color{
                color: #333!important;
              }
            }
          }
        }
        .integralCurrent {
          margin-left: 33px;
          .num {
            margin-left: 6px;
          }
        }
        .msgTitle {
          font-size: 16px;
					text-align: left;
          position: relative;
          word-wrap: break-word;
        }
      }
      .message {
        padding-top: 26px;
        margin-top: 26px;
        align-items: center;		
				.upload{
					margin-left: 26px;
					width: 800px;
				}
        .textarea {
          width: 100%;
          height: 120px;
          background-color: #F7F7F7;
          border: 0;
          outline: none;
          resize: none;
          padding: 12px 14px;
        }
      }
      .integral {
        padding: 26px 0;
      }
    }
    .totalCon {
      padding: 27px 46px;
      background: #F7F7F7;
      .total {
        & ~ .total {
          margin-top: 12px;
        }
        .money {
          width: 120px;
          text-align: right;
          font-size: 14px;
          &.total-money {
            font-size: 20px;
          }
        }
      }
    }
    .totalAmount {
      width: 1160px;
      height: 70px;
      line-height: 70px;
      background: #F7F7F7;
      text-align: right;
      padding-right: 22px;
      margin: 0 auto;
      .money {
        font-size: 20px;
        font-weight: bold;
        margin-left: 4px;
        width: 120px;
        display: inline-block;
      }
    }
    .bnt {
      margin: 27px 20px 0 0;
      cursor: pointer;
      .submit {
        width: 180px;
        height: 46px;
        border-radius: 4px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        line-height: 46px;
        outline: none;
        border: none;
        background-color: #E93323 ;
        &:disabled {
          border-color: #fab6b6;
          background-color: #fab6b6;
        }
      }
    }
  }
}
.coupon .message .number ::v-deep.el-input__inner{
  line-height: unset!important; 
}	
 .message .item-name {font-size: 16px;margin-bottom: 22px;}
.virtual_form{
  border-top: 1px solid #EFEFEF;
  .virtual-item{
    margin-top: 24px;
    display: flex;
    .item{
      align-items: center;
    }
    .virtual-title{
      width: 110px;
      font-size: 16px;
      margin-right: 20px;
      position: relative;
    }
    .discount,.el-date-editor.el-input,.el-select {
      width: 330px;
     
    }
    .el-range-editor--small.el-input__inner{
      height: 40px;
      width: 330px;
    }
  }
}
.presell_protocol{
  cursor: pointer;
}
.check_protocal .icon{
  border-radius: 0;
  width: 16px;
  height: 16px;
}
.invoice_description img{
  display: block;
  margin:  0 auto;
  width: 100%;
  max-width: 100%;
}
.invoice_data_container{
  padding-left: 26px;
  padding-right: 26px;
}
.invoice_item{
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 14px;
  &:last-child{
    margin-right: 0;
  }
  .cont{
    position: relative;
    height: 32px;
    border: 1px solid #d3d3d3;
    display: flex;
    align-items: center;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
        .iconfont{
          display: block;
        }
      }
      .name{
        padding: 0 40px;
        font-size: 14px;
        line-height: 32px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -12px;
        font-size: 22px;
        display: none;
      }
}
.invoice_type_info{
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  margin-top: 10px;
}
</style>
