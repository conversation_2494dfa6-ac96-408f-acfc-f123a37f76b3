<template>
  <div class="refund-select acea-row">
    <div class="select-item acea-row row-middle" @click="goPage(1)">
        <div class="left">
            <img src="~/assets/images/select01.png" alt=""/>
        </div>
        <div class="right-wrapper">
            <div class="title">我要退款(无需退货)</div>
            <div class="txt">未收到货，或与卖家协商同意不退货只退款</div>
        </div>
    </div>
    <div class="select-item acea-row row-middle" @click="goPage(2)">
        <div class="left">
            <img src="~/assets/images/select02.png" alt=""/>
        </div>
        <div class="right-wrapper">
            <div class="title">我要退货退款</div>
		    <div class="txt">已收到货，需要退还收到的货物</div>
	    </div>
	</div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: 'refund_select',
  auth: "guest",
  data() {
    return {
      order_id:'',
      type:0,
      ids:''
    };
  },
  async asyncData({ app, query }) {
    return {
      type: query.type,
      ids: query.ids,
      order_id: query.orderId
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "申请售后-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    let local = this.$cookies.get("auth.strategy");
    this.myHeaders = {
      Authorization: this.$cookies.get(`auth._token.${local}`)
    };
  },
  methods: {
    goPage(type){
		if(this.type == 1 && this.ids){
			this.$router.push({
        path: "/refund_confirm",
        query: {
          orderId: this.order_id,
          type: this.type,
          ids: this.ids,
          refund_type: type
        }
      });
		}else{
			this.$router.push({
        path: "/refund",
        query: {
          orderId: this.order_id,
          type: this.type,
          refund_type: type
        }
      });
		}
	  }
  }
};
</script>

<style lang="scss" scoped>
.refund-select{
	padding: 60px 0;
    .select-item{
        width: 440px;
        height: 140px;
        border: 1px solid #D5D5D5;
        border-radius: 10px;
        padding: 0 44px;
        cursor: pointer;
        &:first-child{
            margin-right: 30px;
        }
        &:hover{
            border-color: #E93323;
        }
        .left{
            width: 50px;
            height: 50px;
            margin-right: 30px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .right-wrapper{
            width: 256px;
            .title{
                font-size: 18px;
                color: #282828;
                margin-bottom: 10px;
            }
            .txt{
                color: #999999;
                font-size: 14px;
            }
        }
    }
}
</style>
