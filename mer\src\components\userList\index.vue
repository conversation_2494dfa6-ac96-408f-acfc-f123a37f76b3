<template>
  <div class="divBox" style="background: #ffffff;">
    <el-form inline size="small" @submit.native.prevent>
      <el-form-item :label="$t('关键字：')">
        <el-input
          v-model="tableFrom.keyword"
          :placeholder="$t('请输入用户昵称/用户ID')"
          class="selWidth"
          @keyup.enter.native="getList(1)"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="getList(1)"
          >{{ $t('查询') }}</el-button
        >
      </el-form-item>
      <el-form-item>
        <span style="font-size: 12px;color: #C0C4CC;"
          >{{ $t('注：将用户添加为客服时,请确保用户先关注本店铺') }}</span
        >
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData.data" size="small">
      <el-table-column label="" min-width="65">
        <template scope="scope">
          <el-radio
            v-model="templateRadio"
            :label="scope.row.uid"
            @change.native="getTemplateRow(scope.$index, scope.row)"
            >&nbsp</el-radio
          >
        </template>
      </el-table-column>
      <el-table-column prop="uid" label="ID" min-width="60" />
      <el-table-column prop="nickname" :label="$t('微信用户名称')" min-width="130" />
      <el-table-column :label="$t('客服头像')" min-width="80">
        <template slot-scope="scope">
          <div class="demo-image__preview">
            <el-image
              class="tabImage"
              :src="scope.row.avatar ? scope.row.avatar : moren"
              :preview-src-list="[scope.row.avatar || moren]"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('用户类型')" min-width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.user_type | statusFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { userLstApi } from "@/api/system";
import { getUserlistApi } from "@/api/community";
export default {
  name: "UserList",
  props: {
    api: {
      type: String,
      default: ""
    }
  },
  filters: {
    saxFilter(status) {
      const statusMap = {
        0: "未知",
        1: "男",
        2: "女"
      };
      return statusMap[status];
    },
    statusFilter(status) {
      const statusMap = {
        wechat: "微信用户",
        routine: "小程序用户",
        h5: "H5用户",
        app: "APP用户",
        pc: "PC用户"
      };
      return statusMap[status];
    }
  },
  data() {
    return {
      moren: require("@/assets/images/f.png"),
      templateRadio: 0,
      loading: false,
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20,
        keyword: ""
      }
    };
  },
  mounted() {
    this.getList(1);
  },
  methods: {
    getTemplateRow(idx, row) {
      /* eslint-disable */
      if (this.api) {
        this.$emit("submit", row);
      } else {
        form_create_helper.set(this.$route.query.field, {
          src: row.avatar || this.moren,
          id: row.uid
        });
        form_create_helper.close(this.$route.query.field);
      }
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.tableFrom.page = num ? num : this.tableFrom.page;
      let api = this.api ? getUserlistApi : userLstApi;
      api(this.tableFrom)
        .then(res => {
          this.tableData.data = res.data.list;
          this.tableData.total = res.data.count;
          this.loading = false;
        })
        .catch(res => {
          this.$message.error(res.message);
          this.loading = false;
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList("");
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList("");
    }
  }
};
</script>

<style scoped></style>
