/**
 * 留游记 LeaveuKey 术语映射批量替换工具
 * 
 * 功能：
 * 1. 交互式输入替换规则
 * 2. 只替换映射对象的 value 部分，保持 key 不变
 * 3. 自动备份、预览确认、语法验证
 * 4. 详细的替换日志和错误处理
 * 
 * 使用方式：node batch-replace-terms.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class LeaveuKeyTermsReplacer {
    constructor() {
        this.configFile = './src/config/leaveuKeyTerms.js';
        this.replacementRules = [];
        this.presetRules = {
            '1': [
                { from: '商户', to: '专家' },
                { from: '分销', to: '合作' },
                { from: '商品', to: '技能' },
                { from: '店铺', to: '专家学历' },
                { from: '品牌', to: '学历' },
                { from: '自营', to: '认证Pro' },
                { from: '店铺', to: '专家学历' },
            ],
            '2': [
                { from: '商品', to: '课程' },
                { from: '购买', to: '报名' }
            ],
            '3': [
                { from: '订单', to: '报名单' },
                { from: '发货', to: '开课' }
            ]
        };
    }

    // 启动脚本
    async run() {
        try {
            console.log('🚀 留游记术语映射批量替换工具');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('📋 专门用于批量替换 leaveuKeyTerms.js 中的映射值（value）');
            console.log('🔒 保持所有键（key）、注释和格式不变\n');
            
            // 1. 检查文件存在
            this.checkFileExists();
            
            // 2. 获取替换规则
            const hasRules = await this.getReplacementRules();
            if (!hasRules) return;
            
            // 3. 读取配置文件
            const originalContent = this.readConfigFile();
            
            // 4. 创建备份
            const backupFile = this.createBackup();
            
            // 5. 执行替换
            console.log('\n🔄 开始执行替换...');
            const { content: modifiedContent, statistics } = this.replaceValues(originalContent);
            
            // 6. 预览和确认
            const confirmed = await this.previewChanges(statistics);
            if (!confirmed) {
                console.log('❌ 用户取消操作');
                fs.unlinkSync(backupFile);
                return;
            }
            
            // 7. 验证语法
            if (!this.validateSyntax(modifiedContent)) {
                console.log('❌ 语法验证失败，操作取消');
                fs.unlinkSync(backupFile);
                return;
            }
            
            // 8. 写入文件
            this.writeConfigFile(modifiedContent);
            
            // 9. 显示完成信息
            this.showCompletionStats(statistics, backupFile);
            
            // 10. 备份管理
            await this.handleBackupCleanup(backupFile);
            
        } catch (error) {
            console.log(`❌ 操作失败: ${error.message}`);
            console.log('💡 请检查文件路径和权限');
            process.exit(1);
        }
    }

    // 检查文件是否存在
    checkFileExists() {
        if (!fs.existsSync(this.configFile)) {
            throw new Error(`配置文件不存在: ${this.configFile}\n请确保在项目根目录下运行此脚本`);
        }
        console.log(`✅ 找到配置文件: ${this.configFile}`);
    }

    // 交互式获取替换规则
    async getReplacementRules() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('\n📋 选择替换规则输入方式:');
        console.log('   1. 使用预设规则 1: 商户→专家, 分销→合作');
        console.log('   2. 使用预设规则 2: 商品→课程, 购买→报名');
        console.log('   3. 使用预设规则 3: 订单→报名单, 发货→开课');
        console.log('   4. 自定义输入替换规则');
        console.log('   5. 组合多个预设规则');
        
        const choice = await this.question(rl, '\n请选择 (1-5): ');
        
        switch (choice) {
            case '1':
            case '2':
            case '3':
                this.replacementRules = [...this.presetRules[choice]];
                console.log(`✅ 已加载预设规则 ${choice}`);
                break;
            case '4':
                await this.getCustomRules(rl);
                break;
            case '5':
                await this.getCombinedRules(rl);
                break;
            default:
                console.log('❌ 无效选择，退出');
                rl.close();
                return false;
        }
        
        rl.close();
        
        if (this.replacementRules.length === 0) {
            console.log('❌ 没有添加任何替换规则，退出');
            return false;
        }
        
        console.log(`\n📋 将使用以下 ${this.replacementRules.length} 个替换规则:`);
        this.replacementRules.forEach((rule, index) => {
            console.log(`   ${index + 1}. "${rule.from}" → "${rule.to}"`);
        });
        
        const confirm = await this.askConfirmation('继续执行替换');
        return confirm;
    }

    // 获取自定义规则
    async getCustomRules(rl) {
        console.log('\n🔧 自定义替换规则输入模式');
        console.log('💡 输入 "done" 完成规则添加\n');
        
        while (true) {
            const fromText = await this.question(rl, `请输入要替换的原文字 (输入 'done' 完成): `);
            if (fromText.toLowerCase() === 'done' || fromText === '') {
                break;
            }
            
            const toText = await this.question(rl, `请输入替换为的新文字: `);
            if (toText === '') {
                console.log('⚠️ 新文字不能为空，跳过此规则');
                continue;
            }
            
            this.replacementRules.push({ from: fromText, to: toText });
            console.log(`✅ 添加替换规则: "${fromText}" → "${toText}"`);
        }
    }

    // 获取组合规则
    async getCombinedRules(rl) {
        console.log('\n🔗 组合预设规则模式');
        console.log('💡 可以选择多个预设规则进行组合\n');
        
        const presetChoices = await this.question(rl, '请输入要组合的预设规则编号 (例如: 1,2,3): ');
        const choices = presetChoices.split(',').map(c => c.trim());
        
        choices.forEach(choice => {
            if (this.presetRules[choice]) {
                this.replacementRules.push(...this.presetRules[choice]);
                console.log(`✅ 已添加预设规则 ${choice}`);
            } else {
                console.log(`⚠️ 无效的预设规则编号: ${choice}`);
            }
        });
    }

    // 读取配置文件
    readConfigFile() {
        const content = fs.readFileSync(this.configFile, 'utf-8');
        const fileSize = (content.length / 1024).toFixed(2);
        console.log(`✅ 成功读取配置文件 (${fileSize} KB)`);
        return content;
    }

    // 执行替换操作（只替换 value 部分）
    replaceValues(content) {
        let modifiedContent = content;
        const statistics = {
            totalReplacements: 0,
            affectedKeys: 0,
            detailedLog: [],
            ruleStats: {}
        };
        
        // 初始化规则统计
        this.replacementRules.forEach(rule => {
            statistics.ruleStats[`${rule.from}→${rule.to}`] = 0;
        });
        
        // 匹配模式：'key': 'value' 或 "key": "value"
        // 使用更精确的正则表达式来匹配键值对
        const pattern = /(['"])((?:[^'"\\]|\\.)*)(['"])\s*:\s*(['"])((?:[^'"\\]|\\.)*)\4/g;
        
        modifiedContent = modifiedContent.replace(pattern, (match, keyQuote1, key, keyQuote2, valueQuote, value) => {
            let newValue = value;
            let hasChanges = false;
            const appliedRules = [];
            
            // 对每个替换规则进行处理
            this.replacementRules.forEach(rule => {
                if (newValue.includes(rule.from)) {
                    const beforeReplace = newValue;
                    newValue = newValue.replace(new RegExp(rule.from, 'g'), rule.to);
                    
                    if (beforeReplace !== newValue) {
                        hasChanges = true;
                        const ruleKey = `${rule.from}→${rule.to}`;
                        statistics.ruleStats[ruleKey]++;
                        appliedRules.push(rule);
                    }
                }
            });
            
            if (hasChanges) {
                statistics.totalReplacements++;
                statistics.affectedKeys++;
                statistics.detailedLog.push({
                    key: key,
                    before: value,
                    after: newValue,
                    appliedRules: appliedRules
                });
            }
            
            // 返回修改后的键值对
            return `${keyQuote1}${key}${keyQuote2}: ${valueQuote}${newValue}${valueQuote}`;
        });
        
        return {
            content: modifiedContent,
            statistics
        };
    }

    // 预览替换结果
    async previewChanges(statistics) {
        if (statistics.detailedLog.length === 0) {
            console.log('\n📋 没有发现需要替换的内容');
            console.log('💡 可能的原因：');
            console.log('   - 指定的文字不存在于映射值中');
            console.log('   - 文字已经被替换过了');
            return false;
        }
        
        console.log(`\n🔍 预览替换结果:`);
        console.log(`━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
        console.log(`📊 将影响 ${statistics.affectedKeys} 个映射键的值`);
        console.log(`🔄 总计 ${statistics.totalReplacements} 处替换\n`);
        
        // 显示规则统计
        console.log('📋 各规则替换统计:');
        Object.entries(statistics.ruleStats).forEach(([rule, count]) => {
            if (count > 0) {
                console.log(`   ${rule}: ${count} 次`);
            }
        });
        
        // 显示详细替换示例
        console.log('\n🔍 替换示例 (前10个):');
        statistics.detailedLog.slice(0, 10).forEach((change, index) => {
            console.log(`${index + 1}. 键: "${change.key}"`);
            console.log(`   修改前: "${change.before}"`);
            console.log(`   修改后: "${change.after}"`);
            console.log('');
        });
        
        if (statistics.detailedLog.length > 10) {
            console.log(`... 还有 ${statistics.detailedLog.length - 10} 处修改未显示`);
        }
        
        return await this.askConfirmation('确认执行这些替换');
    }

    // 创建备份文件
    createBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('.')[0];
        const backupFile = `${this.configFile}.backup.${timestamp}`;
        
        fs.copyFileSync(this.configFile, backupFile);
        console.log(`💾 已创建备份文件: ${backupFile}`);
        
        return backupFile;
    }

    // 验证文件语法
    validateSyntax(content) {
        try {
            // 检查基本的 JavaScript 语法结构
            const lines = content.split('\n');
            const hasExportDefault = lines.some(line => line.trim().startsWith('export default'));
            const hasClosingBrace = content.includes('};');
            
            if (!hasExportDefault) {
                throw new Error('缺少 export default 声明');
            }
            if (!hasClosingBrace) {
                throw new Error('缺少结束大括号');
            }
            
            // 检查括号匹配
            const openBraces = (content.match(/\{/g) || []).length;
            const closeBraces = (content.match(/\}/g) || []).length;
            if (openBraces !== closeBraces) {
                throw new Error('大括号不匹配');
            }
            
            console.log('✅ 文件语法验证通过');
            return true;
        } catch (error) {
            console.log(`❌ 文件语法验证失败: ${error.message}`);
            return false;
        }
    }

    // 写入修改后的文件
    writeConfigFile(content) {
        fs.writeFileSync(this.configFile, content, 'utf-8');
        console.log(`✅ 已保存修改到: ${this.configFile}`);
    }

    // 显示完成统计信息
    showCompletionStats(statistics, backupFile) {
        console.log('\n🎉 替换操作完成！');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`📊 操作统计:`);
        console.log(`   • 影响的键数量: ${statistics.affectedKeys}`);
        console.log(`   • 总替换次数: ${statistics.totalReplacements}`);
        console.log(`   • 使用的规则数: ${this.replacementRules.length}`);
        console.log(`💾 备份文件: ${backupFile}`);
        console.log(`📝 配置文件: ${this.configFile}`);
        
        console.log('\n📋 各规则执行结果:');
        Object.entries(statistics.ruleStats).forEach(([rule, count]) => {
            const status = count > 0 ? '✅' : '⏭️';
            console.log(`   ${status} ${rule}: ${count} 次`);
        });
    }

    // 处理备份文件清理
    async handleBackupCleanup(backupFile) {
        const deleteBackup = await this.askConfirmation('删除备份文件');
        if (deleteBackup) {
            fs.unlinkSync(backupFile);
            console.log('✅ 备份文件已删除');
        } else {
            console.log('💾 备份文件已保留');
            console.log(`💡 如需恢复：cp "${backupFile}" "${this.configFile}"`);
        }
    }

    // 辅助方法：异步问答
    question(rl, prompt) {
        return new Promise((resolve) => {
            rl.question(prompt, resolve);
        });
    }

    // 辅助方法：确认询问
    async askConfirmation(action) {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        const confirm = await this.question(rl, `✅ ${action}吗？(y/N): `);
        rl.close();
        
        return confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes';
    }
}

// 主执行入口
if (require.main === module) {
    const replacer = new LeaveuKeyTermsReplacer();
    replacer.run().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = LeaveuKeyTermsReplacer;
