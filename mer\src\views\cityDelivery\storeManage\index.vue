<template>
  <div class="divBox">
    <div class="selCard">
      <el-form :model="tableFrom" ref="searchForm" size="small" label-width="90px" inline>         
        <el-form-item :label="$t('是否开启：')" prop="status">
          <el-select
            v-model="tableFrom.status"
            :placeholder="$t('请选择')"
            class="selWidth"
            clearable
            @change="getList(1)"
          >
            <el-option v-for="item in applyStatus" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('配送方式：')" prop="type">
          <el-select
            v-model="tableFrom.type"
            :placeholder="$t('请选择')"
            class="selWidth"
            clearable
            @change="getList(1)"
          >
            <el-option v-for="item in deliveryPoint" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('发货点名称：')" prop="station_name">  
          <el-input v-model="tableFrom.station_name" :placeholder="$t('请输入发货点名称')" class="selWidth" clearable @keyup.enter.native="getList(1)" />
        </el-form-item>  
        <el-form-item :label="$t('关键字：')" prop="keyword">
          <el-input v-model="tableFrom.keyword" :placeholder="$t('请输入联系人姓名或电话')" class="selWidth" clearable @keyup.enter.native="getList(1)" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="getList(1)">{{ $t('搜索') }}</el-button>
          <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
        </el-form-item> 
      </el-form>
    </div>
    <el-card class="mt14">
      <el-button size="small" type="primary" class="mb20" @click="add">{{ $t('添加发货点') }}</el-button>
      <el-table v-loading="listLoading" :data="tableData.data" size="small">
        <el-table-column :label="$t('序号')" prop="station_id" min-width="50" />
        <el-table-column prop="station_name" :label="$t('发货点名称')" min-width="100" />
        <el-table-column prop="type" :label="$t('配送方')" min-width="100" >
        <template slot-scope="scope">
          <span >
              {{(scope.row.type==1) ? '达达' : 'UU'}}
            </span>
        </template>
        </el-table-column>
        <el-table-column prop="contact_name" :label="$t('联系人姓名')" min-width="60" />
        <el-table-column prop="phone" :label="$t('联系人电话')" min-width="100" />
        <el-table-column :label="$t('是否开启')" min-width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
              @click.native="onchangeIsShow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="create_time" :label="$t('创建时间')" min-width="100" />
        <el-table-column :label="$t('操作')" min-width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="onEdit(scope.row.station_id)">{{ $t('编辑') }}</el-button>
            <el-button type="text" size="small" @click="onRemark(scope.row.station_id)">{{ $t('备注') }}</el-button>
            <el-button type="text" size="small" @click="onDetails(scope.row.station_id)">{{ $t('详情') }}</el-button>
            <el-button type="text" size="small" @click="onDelete(scope.row.station_id,scope.$index)">{{ $t('删除') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
    <!--添加门店-->
    <add-store :mapKey="mapKey" :keyUrl="keyUrl" :deliveryType="delivery_type" :title="title" @getList="getList" ref="addStore" />
    <!--详情-->
    <el-dialog
      v-if="dialogVisible"
      :title="$t('发货点详情')"
      :visible.sync="dialogVisible"
      width="470px"
    >
      <div v-loading="loading">
        <div class="description">
          <div class="acea-row">
            <div class="description-term">商户名称：{{ storeDetail.merchant && storeDetail.merchant.mer_name }}</div>
            <div class="description-term">发货点名称：{{ storeDetail.station_name }}</div>
            <div class="description-term">配送方：{{ (storeDetail.type==1) ? '达达' : 'UU' }}</div>
            <div class="description-term">联系电话：{{ storeDetail.phone }}</div>
            <div class="description-term">配送品类：{{ storeDetail.business && storeDetail.business.label }}</div>
            <div class="description-term">发货点联系人：{{ storeDetail.contact_name }}</div>
            <div class="description-term">发货点联系电话：{{ storeDetail.phone }}</div>
            <div class="description-term">经纬度：{{ storeDetail.lat }},{{ storeDetail.lng }}</div>
            <div class="description-term">详细地址：{{ storeDetail.station_address }}</div>
            <div class="description-term">是否显示：{{ storeDetail.status == 1 ? '显示' : '不显示' }}</div>
            <div class="description-term">备注：{{ storeDetail.mark || '无' }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { deliveryStoreLst, deliveryStoreDetail, deliveryStoreRemark, deliveryStoreStatus, getConfigApi, deliveryStoreDelete } from '@/api/systemForm'
import addStore from './addStore'
export default {
  components: {
    addStore
  },
  data() {
    return {
      dialogVisible: false,
      storeDetail: {},
      tableData: {
        data: [],
        total: 0
      },
      mapKey: "",
      keyUrl: "",
      listLoading: true,
      loading: true,
      tableFrom: {
        keyword: '',
        status: '',
        station_name: '',
        page: 1,
        limit: 20
      },
      applyStatus: [
        { value: 1, label: leaveuKeyTerms['是'] },
        { value: 0, label: leaveuKeyTerms['否'] }
      ],
      deliveryPoint:[
        {value:1,label:leaveuKeyTerms['达达']},
        {value:2,label:"UU"}
      ],
      delivery_type: 1,
      title: "",
    }
  },
  mounted() {
    this.getMapKey()
    this.getList(1)
  },
  methods: { 
    /**重置 */
    searchReset(){
      this.$refs.searchForm.resetFields()
      this.getList(1)
    },
    // 添加门店
    add() {
      this.title = this.delivery_type == 1 ? "添加达达发货点" : "添加UU发货点"
      this.$refs.addStore.addStore()
    },
    // 编辑门店
    onEdit(id) {
      this.title = this.delivery_type == 1 ? "编辑达达发货点" : "编辑UU发货点"
      this.$refs.addStore.getDetails(id);
    },
    // 获取KEY值
    getMapKey() {
      getConfigApi().then(res => {
        this.mapKey = res.data.tx_map_key
        const keys = res.data.tx_map_key
        this.keyUrl = `https://apis.map.qq.com/tools/locpicker?type=1&key=${keys}&referer=myapp`
        this.delivery_type = res.data.delivery_type
      })
      .catch(res => {
        this.$message.error(res.message)
      })
    },
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableFrom.page = num || this.tableFrom.page
      deliveryStoreLst(this.tableFrom)
        .then(res => {
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          this.listLoading = false
        })
        .catch(res => {
          this.$message.error(res.message)
          this.listLoading = false
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList('')
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList('')
    },
    // 详情
    onDetails(id) {
      this.dialogVisible = true
      deliveryStoreDetail(id)
        .then(res => {
          this.storeDetail = res.data
          this.loading = false
        })
        .catch(res => {
          this.$message.error(res.message)
          this.loading = false
        })
    },
    // 备注
    onRemark(id) {
      this.$modalForm(deliveryStoreRemark(id)).then(() => this.getList(''))
    },
    // 删除
    onDelete(id,idx) {
      this.$modalSureDelete('确定删除该门店').then(
        () => {
          deliveryStoreDelete(id)
            .then(({ message }) => {
              this.$message.success(message)
              this.tableData.data.splice(idx, 1)
            })
            .catch(({ message }) => {
              this.$message.error(message)
            })
        }
      )
    },
    // 是否开通
    onchangeIsShow(row) {
      deliveryStoreStatus(row.station_id, { status: row.status })
        .then(({ message }) => {
          this.$message.success(message)
          this.getList()
        })
        .catch(({ message }) => {
          this.$message.error(message)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.description{
  &-term {
    display: table-cell;
    padding-bottom: 10px;
    line-height: 20px;
    width: 100%;
    font-size: 12px;
  }
}

</style>
