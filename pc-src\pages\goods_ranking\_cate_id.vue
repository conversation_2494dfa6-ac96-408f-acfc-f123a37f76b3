<template>
    <div>
        <div class="header">
            <div class="title">
                {{
                    categoryActive.store_category_id
                        ? categoryActive.cate_name
                        : "热销TOP总榜单"
                }}
            </div>
        </div>
        <ul class="nav">
            <template v-for="(item, index) in categoryList">
                <li
                    v-if="index < 7"
                    :key="item.store_category_id"
                    :class="{
                        active:
                            categoryActive.store_category_id ===
                            item.store_category_id,
                    }"
                    class="item"
                    @click="categoryChange(item)"
                >
                    {{ item.cate_name }}
                </li>
            </template>
            <li
                :class="{ active: categoryList.indexOf(categoryActive) >= 7 }"
                class="item"
            >
                全部<i class="iconfont icon-xiangxia"></i>
                <ul class="other">
                    <template v-for="(item, index) in categoryList">
                        <li
                            v-if="index >= 7"
                            :key="item.store_category_id"
                            class="cell"
                            @click="categoryChange(item)"
                        >
                            {{ item.cate_name }}
                        </li>
                    </template>
                </ul>
            </li>
        </ul>
        <div class="nav-content">
            <div class="list">
                <nuxt-link
                    v-for="(item, index) in productList"
                    :key="item.product_id"
                    class="item"
                    :to="`/goods_detail/${item.product_id}`"
                >
                    <div>{{ index >= 9 ? index + 1 : "0" + (index + 1) }}</div>
                    <div>
                        <img class="image" :src="item.image" />
                    </div>
                    <div>
                        <div class="title">{{ item.store_name }}</div>
                        <div class="price">
                            ￥<span>{{ item.price }}</span>
                        </div>
                    </div>
                </nuxt-link>
            </div>
            <div v-if="!productList.length">
                <div class="noGoods">
                    <div class="pictrue">
                        <img src="../../assets/images/noGoods.png" />
                    </div>
                    <div class="name">亲，该分类暂无商品哟~</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
    name: "goods_ranking",
    auth: false,
    data() {
        return {};
    },
    async asyncData({ app, params }) {
        const cate_id = params.cate_id || 0;
        const [res1, res2] = await Promise.all([
            app.$axios.get("/api/store/product/category/hotranking"),
            app.$axios.get("/api/product/spu/get_hot_ranking", {
                params: { cate_pid: cate_id },
            }),
        ]);
        res1.data.unshift({
            cate_name: "总榜单",
            store_category_id: 0,
        });
        const categoryActive = res1.data.find(
            (item) => item.store_category_id == cate_id
        );
        return {
            categoryList: res1.data,
            productList: res2.data[0].list,
            categoryActive,
        };
    },
    fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
    },
    head() {
        return {
            title: "热销排行-" + this.$store.state.titleCon,
        };
    },
    methods: {
        categoryChange(category) {
            if (
                category.store_category_id ==
                this.categoryActive.store_category_id
            ) {
                return;
            }
            this.categoryActive = category;
            this.getProduct();
        },
        getProduct() {
            this.$axios
                .get("/api/product/spu/get_hot_ranking", {
                    params: { cate_pid: this.categoryActive.store_category_id },
                })
                .then((res) => {
                    this.productList = res.data[0].list;
                })
                .catch((err) => {});
        },
    },
};
</script>

<style lang="scss" scoped>
.header {
    height: 300px;
    background: url("~assets/images/hot-list-header.png") center/cover no-repeat;
    text-align: center;

    .title {
        display: inline-block;
        height: 62px;
        padding: 0 49px;
        margin-top: 99px;
        background: url("~assets/images/hot-list-title1.png") left center/39px
                54px no-repeat,
            url("~assets/images/hot-list-title2.png") right center/39px 54px
                no-repeat;
        font-weight: bold;
        font-size: 44px;
        line-height: 62px;
        color: #ffe9be;
    }
}

.nav {
    display: flex;
    width: 1200px;
    margin: -20px auto 0;
    background-color: #ffffff;

    .item {
        position: relative;
        flex: none;
        width: 150px;
        height: 60px;
        text-align: center;
        font-size: 16px;
        line-height: 60px;
        color: #666666;
        cursor: pointer;

        + .item {
            &::before {
                content: "";
                position: absolute;
                top: 21px;
                bottom: 21px;
                left: 0;
                border-left: 1px solid #dddddd;
            }
        }

        &.active {
            background-color: #e93323;
            color: #ffffff;

            &::before {
                display: none;
            }

            + .item {
                &::before {
                    display: none;
                }
            }
        }

        .iconfont {
            margin-left: 10px;
            font-size: 12px;
        }

        &:last-child {
            &:hover {
                .other {
                    display: flex;
                }
            }
        }
    }

    .other {
        position: absolute;
        top: 100%;
        right: 0;
        z-index: 2;
        display: none;
        flex-wrap: wrap;
        width: 520px;
        padding: 0 0 28px 23px;
        border: 1px solid #eeeeee;
        background-color: #ffffff;
    }

    .cell {
        margin: 20px 45px 0 0;
        font-size: 14px;
        line-height: 19px;
        color: #666666;
        cursor: pointer;
    }
}

.nav-content {
    width: 1200px;
    margin: 20px auto 0;

    .list {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -20px -22px 0;
    }

    .item {
        position: relative;
        width: 285px;
        padding: 20px 27px 17px;
        margin: 0 20px 22px 0;
        box-sizing: border-box;
        background-color: #ffffff;

        &:first-child {
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            width: 590px;
            padding: 40px 40px 40px 30px;

            > div:last-child {
                flex: 1;
                min-width: 0;
                padding-right: 42px;
            }

            .image {
                width: 280px;
                height: 280px;
            }

            .title {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                margin-top: 0;
                white-space: normal;
            }

            .price {
                margin-top: 13px;
                text-align: left;
            }
        }

        > div:first-child {
            position: absolute;
            top: 0;
            left: 10px;
            width: 50px;
            height: 57px;
            padding-top: 3px;
            background: url("~assets/images/hot-list-tag2.png") center top/50px
                57px no-repeat;
            text-align: center;
            font-weight: bold;
            font-size: 33px;
            line-height: 40px;
            color: #986561;
        }

        &:nth-child(1) > div:first-child,
        &:nth-child(2) > div:first-child,
        &:nth-child(3) > div:first-child {
            background-image: url("~assets/images/hot-list-tag1.png");
            color: #ce8f21;
        }
    }

    .image {
        display: block;
        width: 231px;
        height: 231px;
    }

    .title {
        margin-top: 18px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 18px;
        line-height: 24px;
        color: #5a5a5a;
    }

    .price {
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
        font-size: 17px;
        color: #e93323;

        span {
            font-size: 26px;
        }
    }
}

.noGoods {
    text-align: center;
    .pictrue {
        width: 274px;
        height: 174px;
        margin: 130px auto 0 auto;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .name {
        font-size: 14px;
        color: #969696;
        margin-top: 20px;
        margin-bottom: 290px;
    }
}
</style>