// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Vue from 'vue'
import '@/assets/theme/element-variables.scss'
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Col,
  Collapse,
  CollapseItem,
  Dialog,
  Form,
  FormItem,
  Image,
  Input,
  Message,
  MessageBox,
  Option,
  Radio,
  Pagination,
  Rate,
  Row,
  Select,
  TabPane,
  Tabs,
  Tooltip,
  Upload,
  Carousel,
  CarouselItem,
  InputNumber,
  DatePicker,
  TimeSelect,
  RadioGroup,
  Cascader,
  TimePicker,
  Table,
  TableColumn,
  ButtonGroup,
  Tag

} from 'element-ui'
import locale from 'element-ui/lib/locale/lang/en'

const components = [
  Button,
  ButtonGroup,
  Form,
  FormItem,
  Input,
  Upload,
  Dialog,
  Pagination,
  Checkbox,
  MessageBox,
  Message,
  Select,
  Option,
  Radio,
  RadioGroup,
  Cascader,
  Rate,
  Image,CheckboxGroup,
  Row,
  Col,
  Collapse,
  CollapseItem,
  Tabs,
  TabPane,
  Tooltip,
  Carousel,
  CarouselItem,
  InputNumber,
  DatePicker,
  TimePicker,
  TimeSelect,
  Table,
  TableColumn,
  Tag
];

const Element = {
  install (Vue) {
    components.forEach(component => {
      Vue.component(component.name, component)
    })
  }
}
Vue.use(Element, { locale })
Vue.prototype.$message = Message
