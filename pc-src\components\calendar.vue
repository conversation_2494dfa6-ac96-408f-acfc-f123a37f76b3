<template>
  <div class="calendar-container">
    <!-- 日历头部 -->
    <div class="calendar-header">
      <h2>{{ currentYear }}年{{ currentMonth + 1 }}月</h2>
      <div class="calendar-controls">
        <div class="btn" @click="prevMonth">
          <span class="el-icon-arrow-left"></span>
        </div>
        <span class="btn-today" @click="goToday">今日</span>
        <div class="btn" @click="nextMonth">
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
    </div>
    <!-- 日历表格 -->
    <el-table 
      :data="calendarData" 
      style="width: 100%" 
      :show-header="true"
      :header-cell-style="{textAlign: 'center', padding: '11px 0'}"
      :cell-style="getCellStyle"
    >
      <el-table-column 
        v-for="(day, index) in weekDays" 
        :key="index" 
        :prop="day.prop" 
        :label="day.label"
      >
        <template slot-scope="scope">
          <div 
            class="day-number"
            :class="{
              'today': scope.row[day.prop].isToday,
              'selected': scope.row[day.prop].date && selectedDate && 
                        scope.row[day.prop].date.getTime() === selectedDate.getTime(),
              'available': availableDates.includes(scope.row[day.prop].day),
              'unavailable': !availableDates.includes(scope.row[day.prop].day)
            }"
            @click="handleDateClick(scope.row, day.prop,availableDates.includes(scope.row[day.prop].day))"
          >
         {{ scope.row[day.prop].day }}
        </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 时间段选择 -->
    <div class="time-slots-container" v-if="selectedDate">
      <div class="time-slots">
        <div
          v-for="(slot, index) in timeSlots"
          :key="index"
          :type="slot.remaining > 0 && !slot.isPast ? '' : 'info'"
          @click="selectTimeSlot(slot)"
          class="time-item"
          :class="{ 
            'selected': selectedSlot === slot.attr_reservation_id,
            'disabled': slot.stock === 0 || slot.disable
          }"
        >
          {{ slot.start_time }}-{{slot.end_time}}
          <span v-if="showStock">（{{ slot.stock > 0 ? `余${slot.stock}` : '约满' }}）</span>
          <div v-if="selectedSlot === slot.attr_reservation_id" class="iconfont icon-xuanzhong4"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: "Calendar",
  auth: "guest",
  props: {
    id: {
      type: Number | String,
      default: 0
    },
    skuId: {
      type: Number | String,
      default: 0
    },
  },
  data() {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 清除时间部分
    return {
      currentDate: new Date(),
      currentDay: new Date().getDate(),
      selectedDate: today,
      selectedSlot: null,
      weekDays: [
        { prop: 'mon', label: '一' },
        { prop: 'tue', label: '二' },
        { prop: 'wed', label: '三' },
        { prop: 'thu', label: '四' },
        { prop: 'fri', label: '五' },
        { prop: 'sat', label: '六' },
        { prop: 'sun', label: '日' }
      ],
      timeSlots: [],
      availableDates: [],
      selectedMonth: "",
      showStock: true
    }
  },
  watch: {
    currentDate: {
      immediate: true,
      handler(newVal) {
        this.fetchAvailableDatesForMonth(newVal.getFullYear(), newVal.getMonth()+1);
      }
    }
  },
  computed: {
    currentYear() {
      return this.currentDate.getFullYear()
    },
    currentMonth() {
      return this.currentDate.getMonth()
    },
    selectedDateText() {
      if (!this.selectedDate) return ''
      return `${this.selectedDate.getFullYear()}年${this.selectedDate.getMonth() + 1}月${this.selectedDate.getDate()}日`
    },
    isTodaySelected() {
      if (!this.selectedDate) return false;
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return this.selectedDate.getTime() === today.getTime();
    },
    calendarData() {
      const year = this.currentYear;
      const month = this.currentMonth;
      const today = new Date(); 
      const now = new Date(); // 当前时间
      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      // 获取当月天数
      const daysInMonth = lastDay.getDate();  
      // 获取当月第一天是星期几 (0是周日，6是周六)
      let firstDayOfWeek = firstDay.getDay();
      // 转换为周一为一周的第一天
      firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1; 
      // 创建日历数据
      const calendar = [];
      let date = 1;  
      // 创建日历行 (最多6行)
      for (let i = 0; i < 6; i++) {
        // 如果已经超过当月天数且已经显示了一部分下个月的日期，则停止
        if (date > daysInMonth) break; 
         const week = {
          mon: this.createDayData(year, month, date, today, firstDayOfWeek, 0, i),
          tue: this.createDayData(year, month, date, today, firstDayOfWeek, 1, i),
          wed: this.createDayData(year, month, date, today, firstDayOfWeek, 2, i),
          thu: this.createDayData(year, month, date, today, firstDayOfWeek, 3, i),
          fri: this.createDayData(year, month, date, today, firstDayOfWeek, 4, i),
          sat: this.createDayData(year, month, date, today, firstDayOfWeek, 5, i),
          sun: this.createDayData(year, month, date, today, firstDayOfWeek, 6, i)
        };
        // 填充每周的数据
        for (let j = 0; j < 7; j++) {
          const dayKey = this.weekDays[j].prop;
          // 第一行且当前列小于当月第一天所在的列
          if (i === 0 && j < firstDayOfWeek) {
            week[dayKey] = { day: '', date: null, isToday: false };
            continue;
          } 
          // 超出当月天数
          if (date > daysInMonth) {
            week[dayKey] = { day: '', date: null, isToday: false };
            continue;
          }
          // 正常日期
          const dateObj = new Date(year, month, date);
          const isToday = dateObj.toDateString() === today.toDateString();
          week[dayKey] = { 
            day: date < 10 ? '0'+date : String(date), // 存储日期数字
            date: dateObj,
            isToday: isToday,
            isPast: dateObj < new Date(now.getFullYear(), now.getMonth(), now.getDate())
          }; 
          date++; // 只有在处理有效日期时才递增
        }
        calendar.push(week);
      } 
      return calendar;
    }
  },
  created() {
    this.$nextTick(()=>{
      this.fetchAvailableDates()
      this.loadTimeSlots(); // 组件创建时加载今天的时间段
    })
    
  },
  methods: {
    async fetchAvailableDates() {
      this.selectedMonth = this.currentMonth+1 > 10 ? this.currentMonth+1 : '0'+(this.currentMonth+1)
      let data = {
        sku_id: this.skuId,
        date: this.currentYear+'-'+this.selectedMonth
      }
      try {
        const response = await this.$axios.get('/api/store/product/reservation/getMonth/'+this.id, {params: data});
        const formattedData = response.data.days.map(item => ({
          ...item,
          day: String(item.day).padStart(2, '0'),
        }));
        this.availableDates = formattedData.map(itm =>itm.day);
        console.log(this.availableDates);
        console.log(this.calendarData)
        return
      } catch (error) {
        console.error('获取可选日期失败:', error);
      }
    },
   createDayData(year, month, date, today, firstDayOfWeek, dayIndex, weekIndex) {
      // 第一周且当前列小于当月第一天所在的列
      if (weekIndex === 0 && dayIndex < firstDayOfWeek) {
        return { day: '', date: null, isToday: false, isAvailable: false };
      }
      // 超出当月天数
      if (date > new Date(year, month + 1, 0).getDate()) {
        return { day: '', date: null, isToday: false, isAvailable: false };
      }
      // 正常日期
      const dateObj = new Date(year, month, date);
      dateObj.setHours(0, 0, 0, 0); // 标准化日期
      const isToday = dateObj.toDateString() === today.toDateString();
      return { 
        day: date,
        date: dateObj,
        isToday: isToday,
        // isAvailable: isAvailable
      };
    },
    prevMonth() {
      this.currentDate = new Date(this.currentYear, this.currentMonth - 1, 1)
      // this.fetchAvailableDatesForMonth(this.currentYear, this.currentMonth);
      if(this.currentMonth == new Date().getMonth()){
        this.loadTimeSlots()
      }else{
        this.clearSelection();
      }
    },
    nextMonth() {
      this.currentDate = new Date(this.currentYear, this.currentMonth + 1, 1)
      // this.fetchAvailableDatesForMonth(this.currentYear, this.currentMonth);
      if(this.currentMonth == new Date().getMonth()){
        this.loadTimeSlots()
      }else{
        this.clearSelection();
      }
    },
    goToday() {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 清除时间部分
      // 更新当前视图月份
      this.currentDate = new Date();
      // 选中今天
      this.selectedDate = today;
      this.selectedSlot = null;
      // 加载今天的时间段
      this.loadTimeSlots();
      // 如果今天不在当前月份视图，需要刷新日历
      if (today.getMonth() !== this.currentMonth || today.getFullYear() !== this.currentYear) {
        this.currentDate = new Date(); // 确保切换到当前月份
      }
    },
    handleDateClick(row, dayKey, selectable) {
      const cellData = row[dayKey];
      // 如果不是可选日期则不处理
      if (!selectable || (row[dayKey].date.getTime() === this.selectedDate.getTime())) return;
      // 如果点击的是已选中的日期，则取消选中
      const newSelectedDate = cellData.date && 
      (!this.selectedDate || 
      cellData.date.getTime() !== this.selectedDate.getTime()) 
      ? cellData.date 
      : null;
      this.currentDay = cellData.day;
      this.selectedDate = newSelectedDate;
      this.selectedSlot = null;
      this.loadTimeSlots();
    },
    async fetchAvailableDatesForMonth(year, month) {
      this.selectedMonth = month+1 > 10 ? month+1 : '0'+(month)
      let data = {
        sku_id: this.skuId,
        date: year+'-'+this.selectedMonth
      }
      try {
        const response = await this.$axios.get('/api/store/product/reservation/getMonth/'+this.id, {params: data});
        const formattedData = response.data.days.map(item => ({
          ...item,
          day: String(item.day).padStart(2, '0'),
        }));
        this.availableDates = formattedData.map(itm =>itm.day);
        return; 
      } catch (error) {
        console.error('获取可选日期失败:', error);
      }
    },
    loadTimeSlots(id) {
      if(id)this.skuId = id
      if (!this.selectedDate) {
        this.timeSlots = [];
        return;
      }
      let month = this.currentMonth+1 > 10 ? this.currentMonth+1 : '0'+(this.currentMonth+1)
      let day = Number(this.currentDay) >= 10 ? Number(this.currentDay) : '0'+Number(this.currentDay)
     let data = {
        sku_id: this.skuId,
        date: this.currentYear+'-'+month,
        day: day
      }
      this.$axios.get('/api/store/product/reservation/getDay/'+this.id, {params: data}).then(res => {
        this.timeSlots = res.data.list
        this.showStock = res.data.show
      })
      .catch(err => {
        this.$message.error(err);
      });
    },
    selectTimeSlot(slot) {
      if (slot.stock > 0 && !slot.disable) {
        this.selectedSlot = slot.attr_reservation_id
        this.$emit('time-selected', {
          timeSlots: this.timeSlots,
          date: this.currentYear+'-'+this.selectedMonth,
          time: slot.attr_reservation_id,
          day: this.currentDay,
          stock: slot.stock,
        })
      }
    },
    clearSelection() {
      // this.selectedDate = null
      this.selectedSlot = null
      this.timeSlots = []
    },
    getCellStyle({ row, column }) {
      const dayKey = column.property;
      const cellData = row[dayKey];
      return {
        textAlign: 'center',
        height: '50px',
        padding: '5px',
        cursor: cellData.date && !cellData.isPast ? 'pointer' : 'default'
      };
    }
  }
}
</script>

<style scoped lang="scss">
.calendar-container {
  width: 818px;
  margin: 0 auto;
}
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  position: relative;
  top: -5px;
}
.calendar-header h2 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}
/deep/ .el-table::before {
  display: none;
}
/deep/ .el-table thead {
  color: #282828;
}
/deep/ .el-table th {
  background: #F9F9F9;
}
/deep/ .el-table td {
  border: none;
}
/deep/ .el-table th.is-leaf {
  border: none;
}
.calendar-controls {
  display: flex;
  align-items: center;
  .btn {
    width: 34px;
    height: 34px;
    cursor: pointer;
    border: 1px solid #D3D3D3;
    text-align: center;
    line-height: 34px;
  }
}
.btn-today {
  font-size: 16px;
  margin: 0 20px;
  cursor: pointer;
}
.time-slots {
  display: flex;
  flex-wrap: wrap;
}
.time-item {
  width: 189px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EAEAEA;
  margin: 20px 20px 0 0;
  cursor: pointer;
  position: relative;
  font-size: 14px;
  overflow: hidden; /* 确保不裁剪子元素 */
  box-sizing: border-box; /* 确保宽度包含 padding 和 border */
  &:nth-child(4n) {
    margin-right: 0;
  }
  &.disabled {
    background: #F9F9F9;
    color: #ccc;
    border-color: #F9F9F9;
    cursor: not-allowed;
  }
  &.selected {
    border-color: #E93323;
  }
  .iconfont {
    position: absolute;
    right: -3px;
    bottom: -2.3px;
    font-size: 22px;
    color: #E93323;
    line-height: 1; /* 避免行高影响 */
    vertical-align: top; /* 对齐方式 */
    display: inline-block; /* 强制块级渲染 */
    transform: translateZ(0); /* 触发 GPU 加速，避免渲染问题 */
  }
}
.day-number.unavailable {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.6;
}

.day-number.today.unavailable {
  color: #c0c4cc;
}
.el-tag {
  cursor: pointer;
  user-select: none;
}
.day-number {
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 50%;
  text-align: center;
  margin: 0 auto;
  transition: all 0.2s;
}

// .day-number.today {
//   color: #409EFF;
// }

.day-number.selected {
  background-color: rgba(233,51,35,0.05);
  color: #E93323;
}
.day-number.past {
  color: #ccc;
  cursor: not-allowed;
}

.today-marker {
  color: #409EFF;
  font-size: 12px;
  margin-left: 2px;
}
.el-table /deep/ .cell {
  padding: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
