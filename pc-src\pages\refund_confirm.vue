<template>
  <div>
    <div class="header wrapper_1200">
      <span class="home"><nuxt-link class="home" to="/">首页  </nuxt-link> > 个人中心 > 我的订单 ></span>申请退款
    </div>
    <div class="refund wrapper_1200">
      <table class="table">
        <thead>
          <tr>
            <td>商品信息</td>
            <td>退货件数</td>
            <td>退款金额</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in orderData.product" :key="index">
            <td class="td">
              <table border="0">
                <tr>
                  <td>
                    <img :src="item.cart_info.productAttr.image || item.cart_info.product.image" />
                    <div>
                      <div class="name">
                        {{ item.cart_info.product.store_name }}<template v-if="item.cart_info.productAttr">
                          {{ item.cart_info.productAttr.sku }}</template
                        >
                      </div>
                      <div v-if="item.cart_info.productAttr">
                        ￥{{ item.cart_info.productAttr.price }}<span>x{{ item.refund_num }}</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
            <td class="td">{{ item.refund_num }}</td>
            <td v-if="item.cart_info.productAttr" class="td">¥{{ item.cart_info.productAttr.price * item.refund_num }}</td>
          </tr>
        </tbody>
      </table>
      <client-only>
        <el-form ref="form" :model="form" label-width="110px">
          <div class="cont">
            <el-form-item label="退款件数" v-if="type == 1">
              <el-select v-model="form.num" placeholder="请选择退款件数" @change="changeNum">
                <el-option
                  :label="item"
                  :value="item"
                  v-for="(item, index) in numArray"
                  :key="index"
                ></el-option>
              </el-select>
              <span class="item-desc">您最多可提交数量为{{ numArray.length }}件</span>
            </el-form-item>
            <el-form-item :label="status == 0 ? '退款金(含运费)' : '退款金(不含运费)'">
                <el-input-number
                  v-model="rerundPrice"
                  :precision="2"
                  :min="0.00"
                  :max="cash"
                  controls-position="right"
                  style="width: 228px;"
                ></el-input-number>
              <span class="item-desc">您最多可退款 ¥{{parseFloat(orderData.total_refund_price) + parseFloat(orderData.postage_price)}}</span>
            </el-form-item>
            <el-form-item label="退款原因" class="item-border">
              <el-select v-model="form.region" placeholder="请选择退款原因">
                <el-option
                  :label="item"
                  :value="item"
                  v-for="(item, index) in refundArray"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注说明">
              <el-input
                type="textarea"
                placeholder="填写备注信息，100字以内"
                :autosize="{ minRows: 4 }"
                maxlength="100"
                v-model="form.desc"
              ></el-input>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">申请退款</el-button>
          </el-form-item>
        </el-form>
      </client-only>
    </div>
    <!-- 弹窗 -->
    <client-only>
      <el-dialog
        :modal="true"
        :visible.sync="isDialog"
        width="370px"
        :show-close="false"
      >
        <div class="refund-box">
          <img src="~assets/images/refund.png" alt="" />
          <p class="title">退款申请提交成功</p>
          <span>我们会继续为您提供更优质的商品及服务</span>
          <el-button type="primary" @click="subBtn">确认</el-button>
        </div>
      </el-dialog>
    </client-only>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from "element-ui";
export default {
  auth: "guest",
  data() {
    return {
      form: {
        region: "",
        desc: "",
        num: 1
      },
      upLoadUrl: process.env.BASE_URL + "/api/upload/image/file",
      pics: [],
      refundArray: [
          "收货地址填错了",
          "与描述不符",
          "信息填错了，重新拍",
          "收到商品损坏了",
          "未按预定时间发货",
          "其它原因"
      ],
      orderData: {},
      myHeaders: {},
      isDialog: false,
      id: '',
      ids: '',
      type: 1,
      numArray: [],
      rerundPrice: '',
      refund_price:'',
	    postage_price: '',
      maxRefundPrice: '',
      status: '',
      order_status: false,
      refund_type: '',
      unitPostage: 0,
    };
  },
  computed: {
    cash() {
      return parseFloat(this.orderData.total_refund_price) + parseFloat(this.orderData.postage_price);
    }
  },
  async asyncData({ app, query }) {
    let data = [];
    try{
      data = await app.$axios.get('/api/common/refund_message');
    }catch (e){
    }
    console.log(data)
    return {
      id: query.orderId,
      ids: query.ids,
      type: query.type,
      refund_type: query.refund_type,
      refundArray: data.data,
      unitPrice: ''
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "申请退款-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    let local = this.$cookies.get("auth.strategy");
    this.myHeaders = {
      Authorization: this.$cookies.get(`auth._token.${local}`)
    };
    this.getOrderInfo();
  },
  methods: {
    getOrderInfo(){
        let that = this;
        that.$axios.get("/api/refund/product/"+that.id, {
           params: { ids: that.ids, type: that.type }
        }).then(res => {
          that.orderData = res.data;
          that.refund_price = res.data.total_refund_price;
          that.postage_price = res.data.postage_price;
          that.maxRefundPrice = res.data.postage_price + Number(res.data.total_refund_price)
          that.rerundPrice = that.maxRefundPrice.toFixed(2);
          that.status = res.data.status;
		      that.order_status = res.data.activity_type;
          that.unitPostage = that.postage_price > 0 ? that.comsys.Div(that.postage_price, res.data.product[0].refund_num).toFixed(2) : 0;
          if(that.type == 1){
            that.unitPrice = that.comsys.Div(res.data.total_refund_price,res.data.product[0].refund_num)
            for (let i=1;i<=res.data.product[0].refund_num;i++){
                that.numArray.unshift(i)
            }
            that.form.num = that.numArray.length
            that.refund_price = this.comsys.Mul(that.unitPrice, that.numArray[0])
            }
          }).catch(err => {
              return Message.error(err);
            });
    },
    changeNum(num){
      this.refund_price = this.comsys.Mul(this.unitPrice, num)
      this.maxRefundPrice = this.refund_price + this.postage_price;
      this.maxRefundPrice = this.refund_price + (this.postage_price > 0 ? (num === this.orderData.product[0].refund_num 
				? this.postage_price 
				: this.comsys.Mul(num, this.unitPostage)):0);
      this.rerundPrice = this.maxRefundPrice.toFixed(2);
    },
    subBtn() {
      this.isDialog = false;
      return this.$router.replace({
        path: "/user/refund_list",
        query: { type: 2 }
      });
    },
    onSubmit() {
      let that = this;
      if (that.form.region === "") {
        return Message.error("请选择退款原因");
      }
      that.$axios
        .post("/api/refund/apply/"+that.id, {
          type: that.type,
          refund_type: that.refund_type,
          num: that.form.num,
          ids: that.ids,
          refund_message: that.form.region || "",
          mark: that.form.desc,
          // pics: that.pics.join(","),
          refund_price: that.rerundPrice
        })
        .then(res => {
          this.isDialog = true;
        })
        .catch(err => {
          return Message.error(err);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  height: 60px;
  line-height: 60px;
  color: #999999;
  .home {
    color: #282828;
  }
}
.refund {
  padding: 40px 20px 46px;
  background: #ffffff;
  .el-icon-plus {
    margin-top: 20px;
  }
  .table {
    width: 100%;
    border: 1px solid #efefef;
    border-collapse: collapse;
    thead {
      background: #efefef;
      td {
        height: 40px;
        font-size: 14px;
        text-align: center;
        color: #282828;
      }
    }
    tbody {
      .td {
        width: 219px;
        border: 1px solid #efefef;
        font-size: 14px;
        text-align: center;
        color: #282828;
        &:first-child {
          width: auto;
          padding: 20px 50px;
          text-align: left;
          span {
            margin-left: 10px;
            font-size: 12px;
            color: #b1b1b1;
          }
        }
      }
    }
    img {
      float: left;
      width: 70px;
      height: 70px;
      + div {
        margin-left: 90px;
      }
    }
    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      margin-bottom: 13px;
      overflow: hidden;
    }
  }
  .el-form {
    margin-top: 20px;
    .cont {
      padding-right: 25px;
      padding-left: 25px;
      border: 1px solid #efefef;
      padding: 25px 20px;
    }
    .item-desc{
      color: #B5B5B5;
    }
    .item-border{
      padding-bottom: 25px;
      border-bottom: 1px dashed #EFEFEF;
    }
    > .el-form-item {
      margin-top: 25px;
      margin-bottom: 0;
      text-align: right;
    }
    .el-textarea {
      width: 820px;
    }
  }
}
.el-form .item-price ::v-deep input{
  padding-right: 0;
}
.el-form ::v-deep .el-form-item__label{
  color: #282828;
}
.el-form ::v-deep .el-textarea__inner {
  border: none;
  background: #f7f7f7;
}
.el-form ::v-deep .el-upload--picture-card {
  width: 70px;
  height: 70px;
  border-style: solid;
  line-height: 68px;
}
.el-form ::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 70px;
  height: 70px;
}
.refund-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    color: #e93323;
    font-size: 20px;
    margin-top: 20px;
  }
  span {
    margin: 10px 0 40px;
    color: #939393;
    font-size: 14px;
  }
  button {
    width: 150px;
  }
}
.img-box-wrapper {
  display: flex;
  flex-wrap: wrap;
  .img-item {
    position: relative;
    width: 70px;
    height: 70px;
    margin-right: 20px;
    margin-bottom: 12px;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    img {
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }
    .icon {
      position: absolute;
      right: -10px;
      top: -8px;
      font-size: 20px;
      cursor: pointer;
      color: #e93323;
    }
  }
}
</style>
