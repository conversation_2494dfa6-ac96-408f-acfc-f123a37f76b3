<template>
  <div class="order_confirm wrapper_1200" >
    <!-- <div class="header">
      <span class="home">首页 > <span v-if="!news">购物车 > </span> </span>提交订单
    </div> -->
    <div class="address">
      <div class="title">{{service_type == 1 ? '到店服务' : '上门服务'}}</div>
      <div class="lines">
        <img src="../assets/images/line.png">
      </div>
    </div>
    <div class="wrapper wrapper_1200">
      <div class="wrapper_count">
        <div class="title">预约信息</div>
        <div class="order">
          <div class="list">
            <div class="cartCount" v-for="(item, index) in cartInfo" :key="index">
              <div class="storeInfo acea-row row-between-wrapper">
                <div class="name">{{ item.mer_name }}</div>
                <div class="service" @click="chatShow(item.mer_id)">联系客服 <span class="iconfont icon-lianxikefu"></span></div>
              </div>
              <div class="cartInfo">
                <div class="item" v-for="(itemn, indexn) in item.list" :key="indexn">
                  <div class="acea-row row-between-wrapper">
                    <div class="txtPic acea-row row-middle">
                      <div class="pictrue">
                        <img :src='itemn.productAttr.image' v-if="itemn.productAttr.image">
                        <img :src='itemn.product.image' v-else>
                      </div>
                      <div class="text">
                        <div class="name line2">{{ itemn.product.store_name }}</div>
                        <div class="info" v-if="itemn.productAttr">{{ itemn.productAttr.sku }}</div>
                        <div class="info" v-else>默认</div>
                        <div class="err-txt acea-row" v-if="itemn.undelivered && addressInfo.real_name">
                          <span class="iconfont icon-tishi"></span>
                          <div class="txt">此商品不支持该区域配送</div>
                        </div>
                      </div>
                    </div>
                    <div class="acea-row row-between-wrapper">
                      <div class="money acea-row row-middle">
                        <span v-if="itemn.productAttr.show_svip_price && itemn.productAttr">¥{{ itemn.productAttr.svip_price }}</span>
                        <span v-if="itemn.productAttr.show_svip_price" class="svip-image"><img src="@/assets/images/svip.png" alt=""></span>
                        <span class="num">x{{ itemn.cart_num }}</span>
                      </div>
                      <span class="font-color">¥{{ itemn.productAttr.product.price }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <!--关联系统表单-->
              <div v-for="(list,idx) in formValue" :key="idx" class="wrapper virtual_form">
                <div class="item-title">{{formList.name}}</div>
                <el-form report-submit='true'>
                  <div class="acea-row row-between">
                    <div
                      class="virtual-item"
                      v-for="(item, index) in list"
                      :key="index"
                    >  
                      <div class='item acea-row' :class="{'item-img' : item.name == 'uploadPicture'}">
                        <div class="virtual-title"> <span v-if="item.titleShow.val" class="item-require">*</span>{{item.titleConfig.value}} </div>
                        <!-- checkboxs -->
                      <div v-if="item.name == 'checkboxs'" class="discount">
                        <el-checkbox-group v-model="item.values" size="medium">
                          <el-checkbox :label="j" v-for="(j,jindex) in item.wordsConfig.list" :key="jindex">{{j.val}}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <!-- radios -->
                      <div v-if="item.name == 'radios'" class="discount">
                        <el-radio-group v-model="item.values" size="medium" >
                          <el-radio
                            :label="j.val" v-for="(j,jindex) in item.wordsConfig.list" :key="jindex"
                          >{{j.val}}</el-radio>
                        </el-radio-group>
                      </div>
                      <!-- text -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 0" class='discount'>
                        <el-input v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- number -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 4" class="discount">
                        <el-input type="number" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- email -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 3" class="discount">
                        <el-input v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- selects -->
                      <div v-if="item.name == 'selects'" class="discount">
                        <el-select v-model="item.values" placeholder="请选择">
                          <el-option
                            v-for="(j,jindex) in item.wordsConfig.list"
                            :key="jindex"
                            :label="j.val"
                            :value="jindex">
                          </el-option>
                        </el-select>
                      </div>
                      <!-- data -->
                      <div v-if="item.name == 'dates'" class="discount">
                        <el-date-picker
                          class="confirm"
                          v-model="item.values"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :placeholder="item.tipConfig.value"
                        >
                        </el-date-picker>
                      </div>
                      <!-- dateranges -->
                      <div v-if="item.name == 'dateranges'" class="discount">
                        <el-date-picker
                          class="confirm"
                          v-model="item.values"
                          type="daterange"
                          format="yyyy/MM/dd"
                          value-format="yyyy/MM/dd"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                        >
                        </el-date-picker>
                      </div>
                      <!-- time -->
                      <div v-if="item.name == 'times'" class="discount">
                        <el-time-picker
                          format="HH:mm"
                          value-format="HH:mm"
                          class="confirm"
                          v-model="item.values"
                          :placeholder="item.tipConfig.value"
                        >
                        </el-time-picker>
                      </div>
                      <!-- timeranges -->
                      <div v-if="item.name == 'timeranges'" class="discount">
                        <el-time-picker
                          format="HH:mm"
                          value-format="HH:mm"
                          class="confirm"
                          is-range
                          v-model="item.values"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          placeholder="选择时间范围"
                        >
                        </el-time-picker>
                      </div>
                      <!-- id -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 2" class="discount">
                        <el-input type="idcard" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- phone -->
                      <div v-if="item.name == 'texts' && item.valConfig.tabVal == 1" class="discount">
                        <el-input maxlength="11" v-model="item.values" :placeholder="item.tipConfig.value"></el-input>
                      </div>
                      <!-- city -->
                      <div v-if="item.name == 'citys'" @click.stop="changeRegion(item)" class="discount">
                        <el-cascader ref="cascader" :placeholder="item.tipConfig.values" :props="cascaderProps" v-model="item.value" class="confirm"></el-cascader>
                      </div>
                      <!-- img -->
                      <div v-if="item.name == 'uploadPicture'">
                        <el-upload
                          class="upload"
                          list-type="picture-card"
                          :action="upLoadUrl"
                          :limit="8"
                          :on-remove="handleRemove"
                          :on-success="(res, file) => handleSuccess(res, file, idx, index)"
                          :on-exceed="handleExceed"
                          :before-upload="beforeUpload"
                        >
                          <i class="el-icon-plus"></i>
                        </el-upload>
                      </div>
                      </div>
                    </div>
                  </div>
                </el-form>
              </div> 
            </div> 
            <div class="bnt acea-row row-right">
              <button class="submit" @click="SubOrder">下一步</button>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="wrapper_count">
        <div class="totalCon">
          <div class="total acea-row row-middle row-right">
            <div><span class=font-color>{{ totalNum }} </span>件商品，商品总金额：</div>
            <div class="money">¥{{ proPrice || 0 }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.total_platform_coupon_price>0">
            <div>平台优惠金额：</div>
            <div class="money">-¥{{ priceGroup.total_platform_coupon_price }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_coupon_price>0">
            <div>店铺优惠金额：</div>
            <div class="money">-¥{{ priceGroup.order_coupon_price }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_svip_discount>0">
            <div>SVIP优惠金额：</div>
            <div class="money">-¥{{ priceGroup.order_svip_discount }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="total_coupon > 0">
            <div>共优惠：</div>
            <div class="money">-¥{{ total_coupon }}</div>
          </div>
          <div class="total acea-row row-middle row-right" v-if="priceGroup.order_total_postage > 0">
            <div>运费：</div>
            <div class="money">+¥{{ priceGroup.order_total_postage || 0 }}</div>
          </div> 
        </div>
        <div class="totalAmount">应付总额：<span class="money font-color">¥{{ totalPrice }}</span></div>
        <div class="bnt acea-row row-right">
          <button class="submit" @click="SubOrder">下一步</button>
        </div>
      </div> -->
    </div>
    
    <chat-room
      v-if="chatPopShow"
      :chatId="mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
import ChatRoom from "@/components/ChatRoom";
export default {
  name: "reservation_confirm",
  auth: "guest",
  components: { ChatRoom },
  data() {
    return {
      cascaderProps:{
        value:'id',
        label:'name',
        lazy: true,
        lazyLoad:this.lazyLoad
      },
      upLoadUrl: process.env.BASE_URL + "/api/upload/image/file",
      msgObj: {},
      chatPopShow: false,
      isShowSelect: false,
      virtualIndex: 0,
      protocal: "",
      order_model: 2,
      allow_address: true,
      deliveryName: '上门服务',
      order_extend: [],
      extend: {},
      timeVal: '',
      dateVal: '',
      imgUrl: [],
      uploadLimit: 10,
      currentLimit: 10,
      pics: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      post: {
        real_name: '',
        phone: ''
      }, //服务人员信息
      formData: {
        name: '',
        phone: '',
        con: '',
        checked: false
      },
      cityData: {
        pid: 0,
        step: 1,
        list: [],
        con: '',
        province: {},
        city: {},
        county: {},
        district: {}
      },
      cityDataOrg:[],
      addressList: [],
      current: 0,
      news: 1,
      cartInfo: [],
      addressId: 0,
      couponId: 0,
      subCoupon: {},
      order_type: '',
      computeData: {},
      userInfo: {},
      mark: {},//备注信息
      totalNum: 0,
      priceGroup: {},
      totalPrice: 0,//最终商品金额；
      seckillId: 0,
      isShow:false,
      freight: 0,
      orderStatus: '',
      proPrice: '',
      coupon_price: '',
      addressInfo: {},
      open_integral: 0,
      coupon_number: 0,
      coupon_amount: '',
      selectedIndex: -1,
      selectedArr: [],
      stepStop: false,
      activeIndex: "",
      take: [],
      order_key: '',
      mer_id: 0,
      is_take: false,
      cityShow:1,
      order_form: [],
      attrSelected: "",
      count: 1,
      formCount: 2,
      formList: {},
      formValue: [],
      svipData: {},
      cartId: "",
      service_type: 1,  //1到店2上门
      form_type: 2,
      reservation_type: 0,
      productValue: {},
      productInfo: {},
      attrSelected: {},
      productAttr: [],
    }
  },
  async asyncData({query}) {
    try{
      return {
      id: query.id,
      mer_id: query.mer_id,
      sku_id: query.sku_id,
      address_id: query.address_id,
      service_type: query.service_type,
      cartId: query.cart_id,
      form_type: query.form_type
    }
    }catch (e){}
    
  },
  fetch({store}) {
    store.commit('isHeader', true);
    store.commit('isFooter', true);
  },
  head() {
    return {
      title: "确认订单-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getConfirm(this.addressId);
  },
  watch: {
    
  },
  methods: {
    // 获取表单数据
    getFormList(form, count) {
      let arr = []
      let data = this.getFormData(form)
      for(var i=0; i<count; i++) {
        arr.push(JSON.parse(JSON.stringify(data)))
      }
      this.formValue = arr
      console.log(this.formValue)
    },
    // 具体日期
    onchangeDate(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    onchangeTime(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    getTime(index){ 
			this.virtualIndex = index;
		},
    chatShow(mer_id) {
      if(this.$auth.loggedIn){
        this.mer_id = mer_id;
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    // 表单重置
    formReset() {
      this.formData.name = ''
      this.formData.phone = ''
      this.formData.con = ''
      this.formData.checked = false
      this.cityData.province = {}
      this.cityData.city = {}
      this.cityData.district = {}
      this.cityData.step = 1
      this.cityData.pid = 0
      this.selectedArr = []
    },
    bindAdd(isReset) {
      if(isReset){this.cityData.step = 1;this.stepStop = false}
      this.isShowSelect = !this.isShowSelect
      if(this.cityData.step == 4 || this.stepStop){
        return
      } else {
        this.cityData.city = {}
        this.cityData.district ={}
        this.cityData.province ={}
        this.cityData.county ={}
        this.getCityList(0,null)
      }
    },
    beforeUpload(file) {
		  const isImage = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;
		  if (!isImage) {
		    this.$message.error("上传图片只能是 JPG、PNG 格式!");
		  }
		  if (!isLt2M){
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
		  return isImage && isLt2M;
		},
    /** 删除图片*/
		DelPic: function(index) {
			let that = this,
			pic = this.pics[index];
			that.pics.splice(index, 1);
			that.$set(that, 'pics', that.pics);
		},
    //上传图片前的图片验证回调
    beforeAvatarUpload(file) {
      //图片格式
      const isJPG = file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/jpeg';
      //图片大小
      const isLt2M = file.size / 1024 / 1024 <= 2;
      if (!isJPG) {
        this.$message.error('上传图片只能为jpg/jpeg或png格式');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB');
      }
      const _this = this;
      return isJPG && isLt2M;
    },
    handleExceed() {
      this.$message.warning("最多上传10张图片");
    },
    handleRemove(file, fileList) {
      this.pics = [];
      fileList.forEach(item => {
        this.pics.push(item.response.data.url);
      });
      this.currentLimit = this.uploadLimit - this.pics.length - this.imgUrl.length || -1;
    },
    handleSuccess(response, file, index, idx) { 
      if (response.status === 200) {
        console.log(`第 ${index + 1} 个上传组件上传成功`);
        this.formValue[index][idx]['values'].push(response.data.path);
      } else if (response.status === 400) {
        this.$message.error(response.message);
      }
    },
    getCityList(pid,fun) {
      // this.cityData.list = []
      // console.log(res.data);
      pid = pid || 0
      this.$axios.get('/api/v2/system/city/lst/'+pid).then(res => {
        this.cityDataOrg = res.data
        this.cityData.list = res.data
        fun && fun()
      })
    },
    // 选择城市
    bindCity(item) {
      let that = this;
      if (that.cityData.step == 4) {
        that.cityData.district = item;
        that.selectedArr.push(item);
        that.isShowSelect = false
      } else {
        if (that.cityData.step == 1) {
          that.cityData.province = item;
          that.getCityList(item.id,null);
          that.selectedArr = [item];
          that.cityData.step++;
          return
        }
        if (that.cityData.step == 2) {
          that.cityData.city = item
          that.getCityList(item.id,null)
          that.cityData.step++
          that.selectedArr.push(item);
          return
        }
        if(that.cityData.step == 3){
          that.cityData.county = item
          that.selectedArr.push(item);
          that.cityData.step++
          that.getCityList(item.id,function(){
            if(that.cityData.list && that.cityData.list.length){
              that.stepStop = false
              return
            }else{
              that.stepStop = true
              that.isShowSelect = false
              return
            }
          })
        }
      }
    },
   
    SubOrder() {
      let that = this;	
			if(that.formList && that.formList.name)that.order_form = that.formValue;
			let extendArr = [];
      for(var i=0; i<that.order_form.length; i++) {
				let extendData = {}
				for (var j = 0; j < that.order_form[i].length; j++) {
				  let curdata = that.order_form[i][j]
          // if (curdata.name === 'uploadPicture') {
          //   curdata.values = that.pics;
          //   curdata.value = that.pics;
          // }
          if (curdata.name === 'radios') {
            curdata.value = curdata.values.val
          }
          if (['radios'].indexOf(curdata.name) == -1 && (curdata.titleShow.val || (['uploadPicture','citys','timeranges','checkboxs','uploadPicture','dateranges'].indexOf(curdata.name) == -1 && curdata.values && curdata.values.trim())) || (['citys','timeranges','checkboxs','dateranges'].indexOf(curdata.name) != -1 && curdata.values[0])) {
            if ((curdata.name === 'texts' && curdata.valConfig.tabVal == 0) || ['dates','times'].indexOf(curdata.name) != -1) {
              if (!curdata.values || (curdata.values && !curdata.values.trim())){
                return Message.error(`请填写第${i+1}个${curdata.titleConfig.value}`)
              }else{
                curdata.value = curdata.values
              }
            }
            if(curdata.name === 'timeranges'){
              if(!curdata.values[0]){
                return Message.error(`请选择第${i+1}个${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.values.join('-');
              }
            }
            if(curdata.name === 'dateranges'){
              if(!curdata.values[0]){
                return Message.error(`请选择第${i+1}个${curdata.titleConfig.value}`);
              }else{
								curdata.value = curdata.values[0]+'/'+curdata.values[1]
							}
            }
            if (curdata.name === 'checkboxs') {
              if (!curdata.values.length) {
                return Message.error(`请选择第${i+1}个${curdata.titleConfig.value}`);
              }else{
                let obj = '';
                curdata.values.forEach(j=>{
                  obj = obj + (obj?',':'') + j.val
                })
                curdata.value = obj;
              }
            }
            if (curdata.name === 'citys') {
              if (!curdata.value || (curdata.value && !curdata.value.length)) {
                return Message.error(`请选择第${i+1}个${curdata.titleConfig.value}`);
              }else{
                curdata.value = Array.isArray(curdata.value) ? curdata.value.join(',') : curdata.value;
              }
            }
            if (curdata.name === 'selects') {
              if (typeof curdata.values == 'string' && curdata.values =='') {
                return Message.error(`请选择第${i+1}个${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.wordsConfig.list[curdata.values].val
              }
            }
            if (curdata.name === 'texts' && curdata.valConfig.tabVal == 4) {
              if (!curdata.values || (curdata.values && !curdata.values.trim())){
                return Message.error(`请填写第${i+1}个${curdata.titleConfig.value}`)
              }else if (curdata.values <= 0) {
                return Message.error(`第${i+1}个请填写大于0的${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.values;
              }
            }
            if (curdata.name === 'texts' && curdata.valConfig.tabVal == 3) {
              if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(curdata.values)) {
                return Message.error(`第${i+1}个请填写正确的${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.values;
              }
            }
            if (curdata.name === 'texts' && curdata.valConfig.tabVal == 1) {
              if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(curdata.values)) {
                return Message.error(`第${i+1}个请填写正确的${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.values;
              }
            }
            if (curdata.name === 'texts' && curdata.valConfig.tabVal == 2) {
              if (!/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/i.test(curdata.values)) {
                return Message.error(`第${i+1}个请填写正确的${curdata.titleConfig.value}`);
              }else{
                curdata.value = curdata.values;
              }
            }
            if (curdata.name === 'uploadPicture') {
              if (!curdata.values.length) {
                return Message.error(`请上传第${i+1}个${curdata.titleConfig.value}`);
              }
            }
          }
					extendData[curdata.key] = curdata.value
				}	
				extendArr.push(extendData)
			}
			let formId = extendArr.length > 0 ? 1 : 0
      localStorage.setItem('extendInfo',  JSON.stringify(extendArr))

      this.$router.push({
        path: `/order_confirm?cartId=${this.cartId}&serviceType=${this.service_type}&addressId=${this.address_id}&formId=${formId}&merId=${that.mer_id}`
      });
    },
    
    getConfirm() {
      let that = this;
      let data = {
				cart_id: that.cartId.split(",")
			}
			if(that.service_type == 1){
				data.takes = [that.mer_id]
			}else {
				data.address_id = that.address_id
			}
      that.$axios.post("/api/v2/order/check", data).then(res => {
        that.$set(that, 'cartInfo', res.data.order);		
				let count = that.form_type == 1 ? res.data.order[0]['list'][0]['cart_num'] :1;
				that.$set(that, 'totalPrice', res.data.order_price);
				that.$set(that, 'formList', res.data.mer_form_info);
        if(res.data.mer_form_info&&res.data.mer_form_info.name){
         this.formList = res.data.mer_form_info
         that.getFormList(res.data.mer_form_info,count);
        }
        
      }).catch(err=>{
        Message.error(err);
        this.$router.go(-1);
      })
    },
    getFormData(form){
      let formDatas = this.objToArr(form.value)
      formDatas.forEach((item, index, arr)=>{
        item.values = '';
        if(item.name == 'texts'){
          if(item.defaultValConfig.value){
            this.$set(item, 'values' , item.defaultValConfig.value);
          }else{
            this.$set(item, 'values' , '');
          }
        }else if(item.name == 'radios'){
          this.$set(item, 'values' , item.wordsConfig.list[0]['val']);
        }else if(item.name == 'uploadPicture' || item.name == 'checkboxs'){
          this.$set(item, 'values' , []);
        }else if(['timeranges','dateranges'].indexOf(item.name) != -1){
          if(item.valConfig.tabVal==0){
            if(item.valConfig.tabData==0){
              let current = '';
              if(item.name == 'timeranges'){
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm')
              }else{
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy/MM/dd')
              }
              this.$set(item, 'values' , [current,current]);
            }else{
              this.$set(item, 'values' , item.valConfig.specifyDate);
            }
          }else{
            this.$set(item, 'values' , ['','']);   
          }
        }else{
          if(['times','dates'].indexOf(item.name) != -1){
            if(item.valConfig.tabVal==0){
              if(item.valConfig.tabData==0){
                if(item.name == 'times'){
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm'));
                }else{
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy-MM-dd'));
                }
              }else{
                this.$set(item, 'values' , item.valConfig.specifyDate);
              }
            }else{
              this.$set(item, 'values' , '');
            }
          }else{
            this.$set(item, 'values' , '');
          }
        }
      })
      return formDatas;
    },
    // 对象转数组
    objToArr(data) {
      let obj = Object.keys(data);
      let m = obj.map(key => data[key]);
      return m;
    },
    changeRegion(item){
      this.cityShow = item.valConfig.tabVal;
    },
    lazyLoad(node, resolve){
      let id = 0
      if(node.data){
        id = node.data.id
      }
      this.$axios.get(`api/v2/system/city/lst/${id}`).then((res) => {
        res.data.map(item=>{
          item.leaf = (this.cityShow==0 && item.level >=2) || (this.cityShow==1 && item.level >=3) || (this.cityShow==2 && item.level>=4 && item._loading == undefined);
        })
        resolve(res.data);
      }).catch(err=>{
        resolve();
      });
    },
    

   
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-radio__inner{
  width: 18px;
  height: 18px;
}
::v-deep .el-checkbox__inner{
  width: 16px;
  height: 16px;
}
::v-deep .el-radio__inner::after{
  width: 7px;
  height: 7px;
}
::v-deep .el-checkbox__inner::after{
  left: 5px;
  top:2px
}
.service_form {
  background: #fff;
  padding: 26px;
  margin-bottom: 14px;
  .el-form-item {
    &:last-child {
      margin-bottom: 0;
    }
  }
  .discount {
    width: 334px;
  }
}
.input-item {
  margin-bottom: 20px;
}
.item-require{
	color: #e93323;
	position: absolute;
  left: -8px;
}
.text-wrapper {
  position: relative;
  height: 40px;
  line-height: 40px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  box-sizing: border-box;
  border-radius: 4px;
  color: #cfcfcf;
  .select-wrapper {
    z-index: 10;
    position: absolute;
    left: 0;
    top: 45px;
    width: 100%;
    padding: 0 15px;
    background: #fff;
    border: 1px solid #E93323;
    border-radius: 4px;
    line-height: 2;
    .title-box {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #EFEFEF;
      color: #E93323;
      font-size: 14px;
      span {
        margin-right: 8px;
        color: #666666;
      }
    }
    .label-txt {
      margin: 8px 0 18px;
      color: #666666;
      font-size: 14px;
      span {
        margin-right: 10px;
        cursor: pointer;
        &.on {
          color: #E93323;
        }
      }
    }
  }
}
.order_confirm {
  margin-top: 20px;
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .address {
    background-color: #fff;
    .title {
      height: 64px;
      font-size: 18px;
      padding: 0 28px;
      line-height: 64px;
    }
    .lines {
      width: 100%;
      height: 4px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .list {
      padding: 0 25px 26px 25px;
      height: 217px;
      overflow: hidden;
      &.on {
        height: auto;
      }
      .item {
        width: 250px;
        height: 170px;
        border: 1px solid #EAEAEA;
        padding: 22px 27px;
        overflow: hidden;
        margin: 30px 0 0 30px;
        position: relative;
        cursor: pointer;
        &.on {
          border-color: #E93323;
        }
        .icon-xuanzhong4 {
          position: absolute;
          right: -4px;
          bottom: -4px;
          font-size: 27px;
        }
        .default {
          position: absolute;
          width: 56px;
          height: 23px;
          font-size: 12px;
          color: #fff;
          text-align: center;
          line-height: 23px;
          top: 0;
          right: 0;
        }
        &.add {
          text-align: center;
          .iconfont {
            font-size: 35px;
            color: #BFBFBF;
            margin-top: 25px;
          }
          .tip {
            color: #C8C8C8;
            margin-top: 8px;
          }
        }
        .name {
          font-size: 16px;
        }
        .phone {
          margin-top: 9px;
        }
        .details {
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }
  .develivery{
    margin-left: 30px;
  }
  .develivery_take{
    margin-left: 100px;
    font-size: 14px;
    font-weight: normal;
    padding-left: 16px;
    position: relative;
    top: -10px;
    padding-bottom: 8px;
    span{
      color: #E93323;
      font-size: 14px;
      position: absolute;
      top: 3px;
      left: 0;
    }
  }
  .deliviery_item{
    margin-right: 12px;
    box-sizing: border-box;
    cursor: pointer;
    .cont{
      position: relative;
      height: 38px;
      border: 1px solid #d3d3d3;
      display: flex;
      overflow: hidden;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
      }
      .name{
        padding: 0 50px;
        font-size: 16px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -3px;
        font-size: 22px;
      }
  }
  .isShow {
    width: 100%;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #707070;
    cursor: pointer;
    .iconfont {
      margin-left: 8px;
      font-size: 12px;
    }
  }
  .wrapper {
    .wrapper_count{
      background-color: #ffffff;
      padding-bottom: 56px;
      &:last-child{
        margin-top: 14px;
      }
    }
    .checkbox-wrapper{
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
    .money_count{
      margin-right: 30px;
    }
    .money_down{
      color: #fff;
      font-size: 12px;
      line-height: 17px;
      height: 17px;
      background-color: #E93323;
      width: 34px;
      text-align: center;
      border-radius: 3px;
      margin-right: 10px;
    }
    .money_final{
      margin-top: 6px;
      color: #666666;
      font-size: 14px;
    }
    .integral_count{
      padding: 32px 26px;
      border: 1px solid #EFEFEF;
      display: flex;
      justify-content: space-between;
      .integral_title{
        font-size: 18px;
      }
      .money{
        margin-top: 15px;
        align-items: center;
      }
    }
    .title {
      height: 64px;
      line-height: 64px;
      padding: 0 28px;
      font-size: 18px;
    }
    .cartCount {
      padding: 0 32px 26px;
      margin-bottom: 20px;
      border: 1px solid #EFEFEF;
    }
    .cartInfo {
      padding-top: 30px;
      margin-bottom: 20px;
      .item{
        margin-bottom: 15px;
        &:last-child{
          margin-bottom: 0;
        }
      }
      .money {
        margin-right: 104px;
      }
    }
    .storeInfo {
      height: 60px;
      border-bottom: 1px solid #EFEFEF;
      position: relative;
      .qrcode {
        position: absolute;
        background: #fff;
        right: -15px;
        display: none;
        z-index: 10;
        bottom: 60px;
        border: 1px solid #ddd;
        width: 110px;
        height: 110px;
        img{
          width: 100%;
        }
      }
      .name {
        color: #666666;
      }
      .service {
        cursor: pointer;
        color: #999999;
        .iconfont {
          color: #E93323;
          font-size: 18px;
        }
        &:hover {
          + .qrcode {
            display: inline;
          }
        }
      }
    }
    .order {
      width: 1160px;
      margin: 0 auto;
      .list {
        .item {
          // margin-bottom: 26px;
          .txtPic {
            .pictrue {
              width: 62px;
              height: 62px;
              position: relative;
              span {
                display: block;
                width: 100%;
                text-align: center;
                font-size: 12px;
                line-height: 18px;
                background: rgba(0,0,0,.5);
                position: absolute;
                left: 0;
                bottom: 0;
                color: #fff;
              }
              img {
                width: 100%;
                height: 100%;
              }
            }
            .text {
              max-width: 500px;
              margin-left: 10px;
              .name {
                width: 100%;
              }
              .info {
                margin-top: 12px;
                color: #919191;
              }
              .err-txt{
                margin-top: 12px;
                color: #E93323;
                align-items: center;
                .txt{
                  display: inline-block;
                }
                .icon-tishi{
                  position: relative;
                  top: 1px;
                }
              }
            }
          }
          .ship_date{
            margin-top: 10px;
            color: #FD6523;
          }
          .font-color {
            font-size: 16px;
            font-weight: 500;
            display: inline-block;
            text-align: right;
          }
          .num {
            margin-left: 6px;
            font-size: 12px;
          }
          .svip-image{
            width: 35px;
            height: 15px;
            margin-left: 5px;
            img{
              width: 35px;
              height: 15px;
            }
          }
        }
      }
      .coupon {
        border-top: 1px solid #EFEFEF;
        .icon-wenhao{
          color: #fff;
          display: inline-block;
          width: 14px;
          height: 14px;
          text-align: center;
          line-height: 14px;
          background-color: #236FE9;
          border-radius: 100%;
          font-size: 8px;
          margin-right: 5px;
        }
        .plantTitle{
          font-size: 18px;
          position: relative;
          .title{
            align-items: center;
            padding: 0;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
        }
        .couponTitle {
          font-size: 16px;
          padding: 26px 0;
          position: relative;
          .item-name {
            font-size: 18px;
            &.item-75 {
              width: 75px;
            }
          }
          .title{
            align-items: center;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
          .couponPrice {
            font-size: 16px;
            font-weight: bold;
          }
          .couponPriceNo {
            font-size: 14px;
          }
        }
        &.invoice{
          padding-bottom: 26px;
          .couponTitle{
            padding-bottom: 15px;
          }
        }
        .invoice_info{
          font-size: 14px;
          margin-left: 102px;
          color: #236FE9;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .invoice_data{
          position: absolute;
          right: 0;
          bottom: 20px;
          font-size: 16px;
          display: flex;
          .data_item{
            margin-left: 20px;
            &.modify{
              cursor: pointer;
            }
          }
        }
        .couponList {
          .item {
            height: 40px;
            width: 182px;
            border: 1px solid #E93323;
            margin: 0 15px 15px 0;
            position: relative;
            cursor: pointer;
            overflow: hidden;
            &.disabled{
              pointer-events:none;
              opacity: .6;
            }
            &.grey {
              border-color: #B4B4B4;
            }        
            .iconfont {
              position: absolute;
              right: -2px;
              bottom: -4px;
              font-size: 20px;
            }
            .name {
              width: 70px;
              height: 100%;
              color: #fff;
              text-align: center;
              line-height: 40px;
              background-color: #E93323;
              &.grey {
                background-color: #B4B4B4;
              }
            }
            .money {
              width: 110px;
              text-align: center;
              color: #E93323;
              &.grey {
                color: #B4B4B4;
              }
            }
            &.item5 {
              border-color: #333;
              .name {
                background-color: #333;
                color: #FDD7B4;
              }
              .money {
                color: #333;
              }
              .font-color{
                color: #333!important;
              }
            }
          }
        }
        .counter-wrap {
          flex: 1;
          min-width: 0;
          span {
            vertical-align: bottom;
            font-size: 14px;
            color: #5a5a5a;
          }
        }
        .rules {
          color: #999999;
          margin-left: 26px;
          .rule-item {
            margin-bottom: 12px;
          }
        }
      .counter {
        display: inline-block;
        border: 1px solid #d3d3d3;
        font-size: 0;
        button {
          width: 44px;
          height: 36px;
          border: none;
          background: none;
          outline: none;
          font-weight: inherit;
          font-size: 12px;
          font-family: inherit;
          color: #707070;
          vertical-align: middle;
          &:disabled {
            color: #d0d0d0;
            cursor: not-allowed;
          }
        }
        input {
          width: 64px;
          height: 36px;
          border: none;
          border-right: 1px solid #d3d3d3;
          border-left: 1px solid #d3d3d3;
          outline: none;
          font-weight: inherit;
          font-size: 18px;
          font-family: inherit;
          text-align: center;
          color: #5a5a5a;
          vertical-align: middle;
        }
      }
        .integralCurrent {
          margin-left: 33px;
          .num {
            margin-left: 6px;
          }
        }
        .msgTitle {
          font-size: 18px;
					text-align: left;
          position: relative;
          word-wrap: break-word;
        }
      }
      .message {
        padding-top: 26px;
        align-items: center;		
				.upload{
					margin-left: 26px;
					width: 800px;
				}
        .textarea {
          width: 820px;
          height: 120px;
          background-color: #F7F7F7;
          border: 0;
          outline: none;
          resize: none;
          padding: 12px 14px;
          margin-left: 26px;
        }
      }
      .integral {
        padding: 26px 0;
      }
    }
    .totalCon {
      padding: 27px 46px;
      .total {
        & ~ .total {
          margin-top: 12px;
        }
        .money {
          width: 120px;
          text-align: right;
        }
      }
    }
    .totalAmount {
      width: 1160px;
      height: 70px;
      line-height: 70px;
      background: #F7F7F7;
      text-align: right;
      padding-right: 22px;
      margin: 0 auto;
      .money {
        font-size: 20px;
        font-weight: bold;
        margin-left: 4px;
        width: 120px;
        display: inline-block;
      }
    }
    .bnt {
      margin: 38px 20px 0 0;
      cursor: pointer;
      .submit {
        width: 164px;
        height: 46px;
        border-radius: 4px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        line-height: 46px;
        outline: none;
        border: none;
        background-color: #E93323 ;
        &:disabled {
          border-color: #fab6b6;
          background-color: #fab6b6;
        }
      }
    }
  }
}
.coupon .message .number ::v-deep.el-input__inner{
  line-height: unset!important; 
}	
.virtual_form{
  margin-top: 30px;
  border-top: 1px solid #EFEFEF;
  .item-title {
    margin-top: 25px;
    font-size: 16px;
  }
  .virtual-item{
    margin-top: 24px;
    display: flex;
    flex: 1 1 50%;
    .item{
      align-items: center;
      &.item-img {
        align-items: flex-start;
        .virtual-title {
          margin-top: 2px;
        }
      }
    }
    .virtual-title{
      width: 110px;
      font-size: 16px;
      margin-right: 20px;
      position: relative;
    }
    .discount,.el-date-editor.el-input,.el-select {
      width: 344px;
    }
    .el-range-editor--small.el-input__inner{
      height: 40px;
      width: 344px;
    }
  }
  /deep/ .el-date-editor--daterange, /deep/ .el-cascader {
    width: 344px;
  }
  /deep/ .el-upload--picture-card {
    width: 70px;
    height: 70px;
    line-height: 75px;
    border: 1px solid #D0D0D0;
  }
  /deep/ .upload{
    width: 344px;
  }
}
/deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 70px;
  height: 70px;
}
.presell_protocol{
  cursor: pointer;
}
.check_protocal .icon{
  border-radius: 0;
  width: 16px;
  height: 16px;
}
.invoice_description img{
  display: block;
  margin:  0 auto;
  width: 100%;
  max-width: 100%;
}
.invoice_data_container{
  padding-left: 26px;
  padding-right: 26px;
}
.invoice_item{
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 14px;
  &:last-child{
    margin-right: 0;
  }
  .cont{
    position: relative;
    height: 32px;
    border: 1px solid #d3d3d3;
    display: flex;
    align-items: center;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
        .iconfont{
          display: block;
        }
      }
      .name{
        padding: 0 40px;
        font-size: 14px;
        line-height: 32px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -12px;
        font-size: 22px;
        display: none;
      }
}
.invoice_type_info{
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  margin-top: 10px;
}
/* 隐藏上传成功状态的打勾图标 */
.el-upload-list__item .el-icon-upload-success {
  display: none;
}
/* 或者更具体的选择器 */
::v-deep .el-upload-list__item-status-label .el-icon-upload-success,
::v-deep .el-upload-list__item.is-success .el-upload-list__item-status-label{
  display: none;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  margin: 0 15px 15px 0;
  border-radius: 4px;
}
</style>
