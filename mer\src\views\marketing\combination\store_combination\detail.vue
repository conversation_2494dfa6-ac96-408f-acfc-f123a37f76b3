<template>
  <el-dialog :title="$t('查看详情')" :visible.sync="dialogVisible" width="700px" v-if="dialogVisible">
    <el-table v-loading="listLoading" :data="tableData.data" size="small">
      <el-table-column prop="uid" label="ID" min-width="80" />
      <el-table-column prop="nickname" :label="$t('用户名称')" min-width="120">
          <template slot-scope="scope">
            <!-- 判断是否为团长，若是则显示“团长”标签 -->
            <span v-if="scope.row.is_initiator == 1" class="initiator">{{ $t('团长') }}</span><span>{{ scope.row.nickname }}</span>
          </template>
        </el-table-column>
      <el-table-column :label="$t('用户头像')" min-width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.avatar" class="demo-image__preview">
            <el-image
              style="width: 36px; height: 36px"
              :src="scope.row.avatar"
              :preview-src-list="[scope.row.avatar]"
            />
          </div>
          <!-- 若用户头像为空，显示默认头像 -->
          <img v-else src="../../../../assets/images/f.png" alt="" style="width: 36px; height: 36px; vertical-align: top;">
        </template>
      </el-table-column>
      <el-table-column :label="$t('订单编号')" min-width="100">
          <template slot-scope="scope">
            <span> {{ scope.row.orderInfo && scope.row.orderInfo.order_sn }}</span>
          </template>
      </el-table-column>
      <el-table-column :label="$t('金额')" min-width="90">
         <template slot-scope="scope">
            <span> {{ scope.row.orderInfo && scope.row.orderInfo.pay_price }}</span>
          </template>
      </el-table-column> 
      <el-table-column :label="$t('订单状态')" min-width="90">
          <template slot-scope="scope">
            <span v-if="scope.row.orderInfo && scope.row.orderInfo.status == -1" class="refuned">{{ $t('已退款') }}</span>
            <span v-else class="unrefuned">{{ $t('未退款') }}</span>
          </template>
      </el-table-column>  
      <!-- <el-table-column label="团长佣金" min-width="90">
        <template slot-scope="scope">
          <span> {{ scope.row.orderInfo && scope.row.orderInfo.pay_price }}</span>
        </template>
      </el-table-column>    -->
    </el-table>
    <div class="block mb20">
      <el-pagination
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>

  </el-dialog>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { combinationDetailApi } from '@/api/marketing'
export default {
  name: 'Info',
  props: {
    isShow: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      id: '',
      listLoading: false,
      dialogVisible: false,
      tableData: {
        data: [],
        total: 0,
      },
      tableFrom: {
        page: 1,
        limit: 20,
      },
    }
  },
  computed: {

  },
  methods: {
    // 获取列表数据
    getList(id) {
      this.id = id;
      this.listLoading = true;
      combinationDetailApi(this.id,this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list;
          this.tableData.total = res.data.count;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    // 处理分页变化
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList(this.id);
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList(this.id);
    },
  }
}
</script>

<style scoped lang="scss">
.box-container {
  overflow: hidden;
}
.box-container .list {
  float: left;
  line-height: 40px;
}
.box-container .sp {
  width: 50%;
}
.box-container .sp3 {
  width: 33.3333%;
}
.box-container .sp100 {
  width: 100%;
}
.box-container .list .name {
  display: inline-block;
  color: #606266;
}
.box-container .list .blue {
  color: var(--prev-color-primary);
}
.box-container .list.image {
  margin: 20px 0;
  position: relative;
}
.box-container .list.image img {
  position: absolute;
  top: -20px;
}
.labeltop{
  height: 280px;
  overflow-y: auto;
}
.title{
  margin-bottom: 16px;
  color: #17233d;
  font-size: 14px;
  font-weight: bold;
}
.initiator{
    color: red;
    border: 1px solid red;
    text-align: center;
    display: inline-block;
    margin-right: 4px;
    padding: 0 2px;
    height: 16px;
    line-height: 16px;
    font-size: 10px;
}
.refuned{
    color: #f56c6c;
}
.unrefuned{
    color:var(--prev-color-primary);
}
</style>
