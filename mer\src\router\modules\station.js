// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const stationRouter =
  {
    path: `${roterPre}/station`,
    name: 'station',
    meta: {
      icon: '',
      title: leaveuKeyTerms['站内消息']
    },
    alwaysShow: true,
    component: Layout,
    children: [
      {
        path: 'notice/:id?',
        name: 'stationNotice',
        meta: {
          title: leaveuKeyTerms['站内消息']
        },
        component: () => import('@/views/station/notice/index')
      }
    ]
  }

export default stationRouter
