<template>
  <div class="chat-room" @click="press=false">
    <div class="room" @click="roomClick">
      <div v-drag class="head">
        <div class="image">
          <img :src="merchantInfo && merchantInfo.avatar"/>
        </div>
        <div class="name">{{ merchantInfo && merchantInfo.name }}</div>
        <div
          :class="[
            'iconfont',
            muted ? 'icon-shengyinjingyinxianxing' : 'icon-shengyinyinliang',
          ]"
          @click="muted = !muted"
        ></div>
        <div class="iconfont icon-guanbi5" @click="close(false)"></div>
      </div>
      <div class="main">
        <div class="chat">
          <div ref="record" class="record" @scroll="onScroll">
            <ul>
              <div v-for="(items, index) in recordList" :key="index">
               <div class="time">{{ items.time }}</div>
               <li
                 v-for="item in items.children"
                 :key="item.service_log_id"
                 :class="{ right: item.send_type == 0 }"
                 @contextmenu.prevent="showReverst(item)"
               >
                    <div v-if="item.show" class="time-tag">
                    {{ item.create_time }}
                  </div>
                  <div v-if="item.msn_type != 100" class="avatar">
                    <img v-if="item.send_type == 0" :src="item.user.avatar" @error="defaultAvatar(item.user)"/>
                    <img v-else :src="item.service.avatar" @error="defaultAvatar(item.service)"/>
                  </div>
                  <div class="content">
                    <div
                      v-if="item.msn_type === 1"
                      class="text"
                      v-html="item.msn"
                    ></div>
                    <div v-if="item.msn_type === 2" class="image">
                      <div class="text">
                        <i :class="`em ${item.msn}`"></i>
                      </div>
                    </div>
                    <div v-if="item.msn_type === 3" class="image">
                      <img :src="item.msn"/>
                    </div>
                    <div v-if="item.msn_type === 4" class="goods">
                      <div class="thumb">
                        <img :src="item.product.image"/>
                      </div>
                      <div class="intro">
                        <div class="name">
                          {{ item.product.store_name }}
                        </div>
                        <div class="attr">
                          <span>库存：{{ item.product.stock }}</span>
                          <span>销量：{{ item.product.sales }}</span>
                        </div>
                        <div class="group">
                          <div class="money">
                            ￥{{ item.product.price }}
                          </div>
                          <nuxt-link
                            target="_blank"
                            :to="{
                              path: `/goods_detail/${item.product.product_id}`,
                            }"
                          >查看商品 >
                          </nuxt-link
                          >
                        </div>
                      </div>
                    </div>
                    <template v-if="item.msn_type === 5 &&  item.orderInfo && item.orderInfo.order_id">
                      <div
                        v-for="(val, inx) in item.orderInfo.orderProduct" :key="val.id"
                        class="order"
                      >
                        <div class="thumb" v-if="inx == 0">
                          <img :src="val.cart_info.product.image"/>
                        </div>
                        <div class="intro" v-if="inx == 0">
                          <div class="name">
                            订单ID：{{ item.orderInfo.order_id }}
                          </div>
                          <div class="attr">商品数量：{{ item.orderInfo.total_num }}</div>
                          <div class="group">
                            <div class="money">
                              ￥{{ item.orderInfo.pay_price }}
                            </div>
                            <nuxt-link
                              target="_blank"
                              :to="{
                                path: '/order_detail',
                                query: { orderId: item.orderInfo.order_id },
                              }"
                            >查看订单 >
                            </nuxt-link
                            >
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-if="item.msn_type === 6 && item.refundOrder && item.refundOrder.refund_order_id">
                      <div
                        v-for="(val, inx) in item.refundOrder.refundProduct" :key="val.id"
                        class="order"
                      >
                        <div class="thumb" v-if="inx == 0">
                          <img :src="val.product.cart_info.product.image"/>
                        </div>
                        <div class="intro" v-if="inx == 0">
                          <div class="name">
                            退款单号：{{ item.refundOrder.refund_order_sn }}
                          </div>
                          <div class="attr">商品数量：{{ item.refundOrder.refund_num }}</div>
                          <div class="group">
                            <div class="money">
                              ￥{{ item.refundOrder.refund_price }}
                            </div>
                            <nuxt-link
                              target="_blank"
                              :to="{
                                path: '/order_detail',
                                query: { orderId: item.refundOrder.order_id },
                              }"
                            >查看退款单 >
                            </nuxt-link
                            >
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  <template v-if="item.msn_type == 100">
                    <div class="recall-msg">{{item.send_type == 0 ? '你撤回了一条消息' : '对方撤回了一条消息'}}</div>
                  </template>
                  <div class="recall" v-if="item.longpress && press && (new Date().getTime()/1000 - item.send_time) <= 120">
										<div v-if="(new Date().getTime()/1000 - item.send_time) <= 120" class="recall-item" @click.stop="reverstMsg(item)">撤回</div>
									</div>
                </li>
              </div>
            </ul>
          </div>
          <div class="editor">
            <div class="editor-hd">
              <div>
                <button
                  class="emoji-btn"
                  title="表情"
                  @click.stop="emojiSwitch"
                >
                  <i class="iconfont icon-biaoqing1"></i>
                </button>
                <button title="图片">
                  <el-upload
                    :action="uploadAction"
                    :headers="headers"
                    :show-file-list="false"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :before-upload="beforeUpload"
                  >
                    <i class="iconfont icon-tupian1"></i>
                  </el-upload>
                </button>
              </div>
              <div v-show="emojiShow" class="emoji-panel">
                <i
                  v-for="(emoji, index) in emojiList"
                  :key="index"
                  :class="`em ${emoji}`"
                  @click="selectEmoji(emoji)"
                ></i>
              </div>
            </div>
            <div class="editor-bd">
              <textarea
                v-model="chatCont"
                placeholder="请输入文字内容"
                @keydown.enter="ctrlEnter"
              ></textarea>
            </div>
            <div class="editor-ft">
              <button :disabled="!chatCont" @click="sendMessage">发送</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <audio ref="audio" src="~/assets/notice.wav"></audio>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import emojiList from "@/utils/emoji";
import Socket from "@/libs/socket";
import {MessageBox} from "element-ui";

export default {
  name: "ChatRoom",
  auth: false,
  props: {
    chatId: {
      type: Number
    },
  },
  directives: {
    drag: {
      inserted: function (el) {
        let x = 0;
        let y = 0;
        let l = 0;
        let t = 0;
        let isDown = false;
        el.onmousedown = function (e) {
          x = e.clientX;
          y = e.clientY;
          l = el.parentNode.offsetLeft;
          t = el.parentNode.offsetTop;
          isDown = true;
          el.style.cursor = "move";
          window.onmousemove = function (e) {
            if (isDown == false) {
              return;
            }
            let nx = e.clientX;
            let ny = e.clientY;
            let nl = nx - (x - l);
            let nt = ny - (y - t);
            el.parentNode.style.left = nl + "px";
            el.parentNode.style.top = nt + "px";
          };
          window.onmouseup = function () {
            isDown = false;
            el.style.cursor = "default";
            window.onmousemove = null;
            window.onmouseup = null;
          };
          return false;
        };
      },
    },
  },
  data() {
    return {
      emojiList: emojiList,
      emojiShow: false,
      recordList: [],
      page: 1,
      limit: 20,
      loading: false,
      finished: false,
      chatCont: "",
      uploadAction: process.env.BASE_URL + "/api/upload/image/file",
      headers: null,
      audio: null,
      muted: false,
      chatShow: false,
      token: '',
      merchantInfo: {},
      closed: false,
      press: false,
    };
  },
  watch: {
    chatId: {
      async handler(id, old) {
        if (old !== undefined) {
          this.socket && this.socket.send({
            data: {
              mer_id: id
            },
            type: "chat_start"
          });
        }
        this.page = 1;
        this.finished = false;
        this.loading = false;
        this.getRecordList();
        try {
          this.merchantInfo = await this.$axios.$get('/api/service/info/' + id);
        } catch (e) {
          this.$message.error('系统错误');
        }
      },
      immediate: true
    }
  },
  mounted() {
    let that = this;
    this.$nextTick(() => {
      let local = this.$cookies.get('auth.strategy');
      let token = (this.$cookies.get(`auth._token.${local}`) || '').split(' ')[1] || '';
      this.audio = this.$refs.audio;
      let ws = process.env.WS_URL || process.env.BASE_URL || location.origin;
      let url = `${ws}/?type=user&token=${token}`
      const createSocket = () => {
        this.socket = new Socket(url);
        this.socket.vm(this);
      };
      createSocket();
      this.$on('socket_open', () => {
        that.socket.send({
          data: {
            mer_id: this.chatId
          },
          type: "chat_start"
        });
      })
      this.$on(["reply", "chat", "send_chat"], (data) => {
        data.longpress = false;
        if (data.is_get && data.is_get == 1 && !this.muted) {
          this.audio.play();
        }
        if(this.recordList.length>0 && this.recordList[this.recordList.length-1]['children'].length > 0){
          let index = this.recordList[this.recordList.length-1]['children'].length-1
					let item = this.recordList[this.recordList.length-1]['children'][index]
					if(data.send_time - item.send_time > 300){
						this.recordList.push({time: data.create_time,children:[data]})
					}else{
						this.recordList[this.recordList.length-1]['children'].push(data);
					}
				}else{
					this.recordList.push({time: data.create_time,children:[data]})
				}
        this.deleteMsg(data);
        this.$nextTick(() => {
          this.$refs.record.scrollTop =
            this.$refs.record.scrollHeight - this.$refs.record.clientHeight;
        });
      });
      this.$on(["socket_error", "socket_close"], () => {
        if (!this.closed) {
          MessageBox.confirm("连接失败,是否重新连接？", "提示").then(_ => {
            createSocket();
          })
        }
      });
      this.$on("err_tip", (data) => {
        this.$message.error(data);
      });
    });
  },
  beforeDestroy() {
    this.close(true);
  },
  methods: {
    defaultAvatar(user) {
      user.avatar = '/static/f.png';
    },
    roomClick(event) {
      if (
        !event.target.classList.contains("emoji-panel") &&
        !event.target.classList.contains("emoji-btn") &&
        !event.target.classList.contains("icon-biaoqing") &&
        this.emojiShow
      ) {
        this.emojiShow = false;
      }
    },
    // 右键撤回
    showReverst(item){
       this.recordList.forEach((item, index) => {
        item.children.forEach((itm, idx) => {
          itm.longpress = false
        });
      });
      item.longpress = true;
      this.press = true;
    },
    // 撤回消息
    reverstMsg(item) {   
      this.socket.send({
        data: {
          msn: item.service_log_id,
          msn_type: 100,
          mer_id: this.chatId,
        },
        type: "send_chat",
      });
      setTimeout(function(){
        item.longpress = false;
      },300)
    },
    // 删除数组中已撤回的消息
    deleteMsg(data){
      let that = this;
      that.recordList.forEach(function(item, index) {
        item.children.forEach(function(itx, i) {
          if(data.msn_type == 100 && data.msn == itx.service_log_id){
            item.children.splice(i,1)
          }
        });	
      });
    },
    // enter 发送
    ctrlEnter(e) {
      if (e.keyCode == 13) {
        e.preventDefault();
      }
      if (this.chatCont.trim()) {
        this.sendMessage();
      }
    },
    // 关闭聊天窗口
    close(flag) {
      this.socket.send({
        data: {
          mer_id: this.chatId
        },
        type: "chat_end"
      });
      this.socket.close();
      if (!flag) {
        this.$emit('close');
        this.closed = true;
      }
    },
    // 选择表情
    selectEmoji(emoji) {
      this.emojiShow = false;
      this.socket.send({
        data: {
          msn: emoji,
          msn_type: 2,
          mer_id: this.chatId,
        },
        type: "send_chat",
      });
    },
    onScroll(event) {
      if (event.target.scrollTop <= 30) {
        this.getRecordList();
      }
    },
    // 聊天记录
    getRecordList() {
      if (this.loading) {
        return;
      }
      if (this.finished) {
        return;
      }
      this.loading = true;
       this.$axios
        .get(`/api/has_service/${this.chatId}`)
        .then((res) => {
          this.$axios
          .get(`/api/service/history/${this.chatId}`, {
            params: {
              page: this.page,
              limit: this.limit
            },
          })
          .then((res) => {
            // this.recordList = res.data.list.concat(this.page < 2 ? [] : this.recordList);
            let arr = this.getChatTime(res.data.list)
              let newArr = []
              for(var j in arr){
                let key = j
                let item  = {}
                item.time = key;
                arr[j].forEach(function(itm, i) {
                  itm.longpress = false
                });
                item.children = arr[j]
                newArr.push(item)
              }
            this.recordList = newArr.concat(this.page < 2 ? [] : this.recordList);
            this.finished = res.data.list.length < this.limit;
            this.page++;
            if (this.page === 2) {
              this.$nextTick(() => {
                this.$refs.record.scrollTop =
                  this.$refs.record.scrollHeight - this.$refs.record.clientHeight;
              });
            }
            this.$nextTick(() => {
              setTimeout(() => {
                this.loading = false;
              }, 500);
            });
          })
          .catch(() => {
            this.$message.error('加载失败');
          });
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    // 处理聊天时间
		getChatTime(list){
			let arr = {}
			let currentTime = ''
			for(var i=0; i<list.length;i++){
				if(i == 0){
					let time = list[i]['create_time']
					arr[time] = [list[i]]
					currentTime = time
				}else{
					if((list[i]['send_time'] - list[i-1]['send_time']) <= 300 ){
						arr[currentTime].push(list[i]) 
					}else{
						let time = list[i]['create_time']
						arr[time] = [list[i]]
						currentTime = time
					}
				}
			}
			return arr;
		},	
    // 表情包显示隐藏
    emojiSwitch() {
      this.emojiShow = !this.emojiShow;
    },
    // 发送消息
    sendMessage() {
      this.socket.send({
        data: {
          msn: this.chatCont,
          msn_type: 1,
          mer_id: this.chatId,
        },
        type: "send_chat",
      });
      this.chatCont = "";
    },
    beforeUpload(file) {
      const isImage = file.type === "image/jpeg" || file.type === "image/png";
      if (!isImage) {
        this.$message.error("上传图片只能是 JPG、PNG 格式!");
      }
      return isImage;
    },
    uploadSuccess(response) {
      if (response.status === 200) {
        this.socket.send({
          data: {
            msn: response.data.path,
            msn_type: 3,
            mer_id: this.chatId,
          },
          type: "send_chat",
        });
      } else if (response.status === 400) {
        this.$message.error(response.msg);
      }
    },
    uploadError(error) {
      this.$message.error(error);
    },
  },
};
</script>

<style lang="scss" scoped>
.chat-room {
  .room {
    border-radius: 10px;
    position: fixed;
    top: calc(50% - 327px);
    left: calc(50% - 365px);
    z-index: 999;
    display: flex;
    flex-direction: column;
    width: 730px;
    height: 654px;
    background-color: #ffffff;
    overflow: hidden;
    box-shadow: 1px 1px 15px 0 rgba(0, 0, 0, 0.1);
    &.win {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .head {
      display: flex;
      align-items: center;
      height: 50px;
      padding-right: 15px;
      padding-left: 20px;
      background: linear-gradient(270deg, #1890ff 0%, #3875ea 100%);
      .image {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .name {
        flex: 1;
        min-width: 0;
        margin-left: 15px;
        font-size: 16px;
        color: #ffffff;
      }
      .iconfont {
        width: 25px;
        height: 25px;
        font-size: 16px;
        line-height: 25px;
        text-align: center;
        color: #ffffff;
        cursor: pointer;
      }
    }
    .main {
      flex: 1;
      display: flex;
      min-height: 0;
      .chat {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
      }
      .record {
        flex: 1;
        min-height: 0;
        overflow-x: hidden;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }
        .time{
          margin: 15px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999999;
          font-size: 12px;
        }
        ul {
          padding: 20px;
        }
        li {
          ~ li {
            margin-top: 20px;
          }
          &::after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
          }
          &.right {
            position: relative;
            .recall{
              width: 70px;
              height: 30px;
              line-height: 30px;
              background: #fff;
              border-radius: 2px;
              text-align: center;
              font-size: 12px;
              box-shadow: 0px 3px 6px 1px rgba(51,51,51,0.1);
              position:absolute;
              bottom: -20px;
              right: 50px;
              z-index: 10;
             .recall-item{
              cursor: pointer;
             }
            }          
            .avatar {
              float: right;
            }
            .content {
              text-align: right;
              > div {
                text-align: left;
              }
            }
          }
        }
        .recall-msg {
          display: flex;
          align-items: center;
          justify-content: center;;
          width: 100%;	
          color: #999;
          font-size: 12px;
          margin: 15px 0;
        }
        .time-tag {
          padding-top: 10px;
          padding-bottom: 30px;
          text-align: center;
          color: #999999;
        }
        .avatar {
          float: left;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          &.right {
            float: right;
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .content {
          margin-right: 56px;
          margin-left: 56px;
        }
        .text {
          display: inline-block;
          min-height: 41px;
          padding: 10px 12px;
          border-radius: 10px;
          background-color: #f5f5f5;
          font-size: 15px;
          line-height: 21px;
          color: #000000;
          word-break: break-all;
        }
        .image {
          display: inline-block;
          max-width: 50%;
          border-radius: 10px;
          overflow: hidden;
          img {
            display: block;
            max-width: 100%;
          }
        }
        .goods,
        .order {
          display: inline-flex;
          align-items: center;
          width: 320px;
          padding: 10px 13px;
          border-radius: 10px;
          background-color: #f5f5f5;
        }
        .thumb {
          width: 60px;
          height: 60px;
          border-radius: 5px;
          overflow: hidden;
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
        .intro {
          flex: 1;
          min-width: 0;
          margin-left: 10px;
          .name {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 15px;
            color: #000000;
          }
          .attr {
            margin-top: 5px;
            font-size: 12px;
            color: #999999;
            span {
              vertical-align: middle;
              ~ span {
                margin-left: 10px;
              }
            }
          }
          .group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 5px;
            .money {
              font-size: 14px;
              color: #ff0000;
            }
            a {
              font-size: 12px;
              color: #1890ff;
            }
          }
        }
      }
      .editor {
        display: flex;
        flex-direction: column;
        height: 162px;
        border-top: 1px solid #ececec;
        > div {
          &:first-child {
            font-size: 0;
          }
        }
        button {
          border: none;
          background: none;
          outline: none;
          ~ button {
            margin-left: 20px;
          }
          &.end {
            font-size: 15px;
          }
          &:hover {
            color: #1890ff;
            .iconfont {
              color: #1890ff;
            }
          }
        }
        .editor-hd {
          position: relative;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 50px;
          padding-right: 20px;
          padding-left: 20px;
          .iconfont {
            line-height: 1;
            color: #333333;
          }
          .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 5px;
            width: 390px;
            padding-bottom: 10px;
            border: 1px solid #ececec;
            margin-bottom: 5px;
            background-color: #ffffff;
            box-shadow: 1px 0 16px 0 rgba(0, 0, 0, 0.05);
            .em {
              width: 28px;
              height: 28px;
              padding: 4px;
              margin-top: 10px;
              margin-left: 10px;
              box-sizing: border-box;
              &:hover {
                background-color: #ececec;
              }
            }
          }
        }
        .icon-biaoqing1,
        .icon-tupian1 {
          font-size: 22px;
        }
        .icon-guanji {
          margin-right: 5px;
          font-size: 15px;
        }
        .editor-bd {
          flex: 1;
          min-height: 0;
          textarea {
            display: block;
            width: 100%;
            height: 100%;
            padding-right: 20px;
            padding-left: 20px;
            border: none;
            outline: none;
            resize: none;
            white-space: pre-wrap;
            overflow-wrap: break-word;
            &::-webkit-scrollbar {
              display: none;
            }
          }
        }
        .editor-ft {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding-right: 20px;
          padding-bottom: 20px;
          button {
            width: 68px;
            height: 26px;
            border: none;
            border-radius: 3px;
            background-color: #3875ea;
            outline: none;
            font-size: 13px;
            color: #ffffff;
            &:disabled {
              background-color: #cccccc;
            }
          }
        }
      }
      .notice {
        display: flex;
        flex-direction: column;
        width: 260px;
        border-left: 1px solid #ececec;
        .rich {
          flex: 1;
          min-height: 0;
          padding: 18px 18px 0;
          overflow-x: hidden;
          overflow-y: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          
        }
        .copy {
          padding-top: 15px;
          padding-bottom: 15px;
          font-size: 12px;
          text-align: center;
          a {
            color: #cccccc !important;
            text-decoration: none;
          }
        }
      }
    }
  }
}
.main .notice .rich ::v-deep video,
.main .notice .rich ::v-deep img {
  width: 100%;
}
.em {
  display: inline-block;
  height: 1em;
  width: 1em;
  overflow: hidden;
  line-height: 18px;
  font-size: 22px;
  vertical-align: middle;
  margin-top: -4px;
  color: transparent !important;
  background-size: 4100%;
  background-image: url("~assets/images/look.png") !important;
 }
.em.em-tlj-1 {
  background-position: 0% 0%;
}
.em.em-tlj-2 {
  background-position: 0% 2.5%;
}
.em.em-tlj-3 {
  background-position: 0% 5%;
}
.em.em-tlj-4 {
  background-position: 0% 7.5%;
}
.em.em-tlj-5 {
  background-position: 0% 10%;
}
.em.em-tlj-6 {
  background-position: 0% 12.5%;
}
.em.em-tlj-7 {
  background-position: 0% 15%;
}
.em.em-tlj-8 {
  background-position: 0% 17.5%;
}
.em.em-tlj-9 {
  background-position: 0% 20%;
}
.em.em-tlj-10 {
  background-position: 0% 22.5%;
}
.em.em-tlj-11 {
  background-position: 0% 25%;
}
.em.em-tlj-12 {
  background-position: 0% 27.5%;
}
.em.em-tlj-13 {
  background-position: 0% 30%;
}
.em.em-tlj-14 {
  background-position: 0% 32.5%;
}
.em.em-tlj-15 {
  background-position: 0% 35%;
}
.em.em-tlj-16 {
  background-position: 0% 37.5%;
}
.em.em-tlj-17 {
  background-position: 0% 40%;
}
.em.em-tlj-18 {
  background-position: 0% 42.5%;
}
.em.em-tlj-19 {
  background-position: 0% 45%;
}
.em.em-tlj-20 {
  background-position: 0% 47.5%;
}
.em.em-tlj-21 {
  background-position: 0% 50%;
}
.em.em-tlj-22 {
  background-position: 0% 52.5%;
}
.em.em-tlj-23 {
  background-position: 0% 55%;
}
.em.em-tlj-24 {
  background-position: 0% 57.5%;
}
.em.em-tlj-25 {
  background-position: 0% 60%;
}
.em.em-tlj-26 {
  background-position: 0% 62.5%;
}
.em.em-tlj-27 {
  background-position: 0% 65%;
}
.em.em-tlj-28 {
  background-position: 0% 67.5%;
}
.em.em-tlj-29 {
  background-position: 0% 70%;
}
.em.em-tlj-30 {
  background-position: 0% 72.5%;
}
.em.em-tlj-31 {
  background-position: 0% 75%;
}
.em.em-tlj-32 {
  background-position: 0% 77.5%;
}
.em.em-tlj-33 {
  background-position: 0% 80%;
}
.em.em-tlj-34 {
  background-position: 0% 82.5%;
}
.em.em-tlj-35 {
  background-position: 0% 85%;
}
.em.em-tlj-36 {
  background-position: 0% 87.5%;
}
.em.em-tlj-37 {
  background-position: 0% 90%;
}
.em.em-tlj-38 {
  background-position: 0% 92.5%;
}
.em.em-tlj-39 {
  background-position: 0% 95%;
}
.em.em-tlj-40 {
  background-position: 0% 97.5%;
}
.em.em-tlj-41 {
  background-position: 0% 100%;
}
.em.em-tlj-42 {
  background-position: 2.5% 0%;
}
.em.em-tlj-43 {
  background-position: 2.5% 2.5%;
}
.em.em-tlj-44 {
  background-position: 2.5% 5%;
}
.em.em-tlj-45 {
  background-position: 2.5% 7.5%;
}
.em.em-tlj-46 {
  background-position: 2.5% 10%;
}
.em.em-tlj-47 {
  background-position: 2.5% 12.5%;
}
.em.em-tlj-48 {
  background-position: 2.5% 15%;
}
.em.em-tlj-49 {
  background-position: 2.5% 17.5%;
}
.em.em-tlj-50 {
  background-position: 2.5% 20%;
}
.em.em-tlj-51 {
  background-position: 2.5% 22.5%;
}
.em.em-tlj-52 {
  background-position: 2.5% 25%;
}
.em.em-tlj-53 {
  background-position: 2.5% 27.5%;
}
.em.em-tlj-54 {
  background-position: 2.5% 30%;
}
.em.em-tlj-55 {
  background-position: 2.5% 32.5%;
}
.em.em-tlj-56 {
  background-position: 2.5% 35%;
}
.em.em-tlj-57 {
  background-position: 2.5% 37.5%;
}
.em.em-tlj-58 {
  background-position: 2.5% 40%;
}
.em.em-tlj-59 {
  background-position: 2.5% 42.5%;
}
.em.em-tlj-60 {
  background-position: 2.5% 45%;
}
.em.em-tlj-61 {
  background-position: 2.5% 47.5%;
}
.em.em-tlj-62 {
  background-position: 2.5% 50%;
}
.em.em-tlj-63 {
  background-position: 2.5% 52.5%;
}
.em.em-tlj-64 {
  background-position: 2.5% 55%;
}
.em.em-tlj-65 {
  background-position: 2.5% 57.5%;
}
.em.em-tlj-66 {
  background-position: 2.5% 60%;
}
.em.em-tlj-67 {
  background-position: 2.5% 62.5%;
}
.em.em-tlj-68 {
  background-position: 2.5% 65%;
}
.em.em-tlj-69 {
  background-position: 2.5% 67.5%;
}
.em.em-tlj-70 {
  background-position: 2.5% 70%;
}
.em.em-tlj-71 {
  background-position: 2.5% 72.5%;
}
.em.em-tlj-72 {
  background-position: 2.5% 75%;
}
.em.em-tlj-73 {
  background-position: 2.5% 77.5%;
}
.em.em-tlj-74 {
  background-position: 2.5% 80%;
}
.em.em-tlj-75 {
  background-position: 2.5% 82.5%;
}
.em.em-tlj-76 {
  background-position: 2.5% 85%;
}
.em.em-tlj-77 {
  background-position: 2.5% 87.5%;
}
.em.em-tlj-78 {
  background-position: 2.5% 90%;
}
.em.em-tlj-79 {
  background-position: 2.5% 92.5%;
}
.em.em-tlj-80 {
  background-position: 2.5% 95%;
}
.em.em-tlj-81 {
  background-position: 2.5% 97.5%;
}
.em.em-tlj-82 {
  background-position: 2.5% 100%;
}
.em.em-tlj-83 {
  background-position: 5% 0%
 }
 .em.em-tlj-84 {
  background-position: 5% 2.5%
 }
 .em.em-tlj-85 {
  background-position: 5% 5%
 }
 .em.em-tlj-86 {
  background-position: 5% 7.5%
 }
 .em.em-tlj-87 {
  background-position: 5% 10%
 }
 .em.em-tlj-88 {
  background-position: 5% 12.5%
 }
 .em.em-tlj-89 {
  background-position: 5% 15%
 }
 .em.em-tlj-90 {
  background-position: 5% 17.5%
 }
 .em.em-tlj-91 {
  background-position: 5% 20%
 }
 .em.em-tlj-92 {
  background-position: 5% 22.5%
 }
 .em.em-tlj-93 {
  background-position: 5% 25%
 }
 .em.em-tlj-94 {
  background-position: 5% 27.5%
 }
 .em.em-tlj-95 {
  background-position: 5% 30%
 }
 .em.em-tlj-96 {
  background-position: 5% 32.5%
 }


</style>
