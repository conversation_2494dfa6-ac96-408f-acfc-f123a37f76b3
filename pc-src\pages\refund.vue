<template>
  <div>
    <div class="header wrapper_1200">
      <span class="home">首页 > 订单详情 > </span>申请退款
    </div>
    <div class="refund wrapper_1200">
      <table class="table">
        <thead>
          <tr>
            <td>商品信息</td>
            <td>退货件数</td>
            <td>退款金额</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in orderData" :key="index">
            <td class="td">
              <table border="0">
                <tr>
                  <td>
                    <div class="check-box" @click="bindCheck(item)">
                        <span v-if="item.check" class="iconfont icon-xuanzhong1" style="color: #E93323; font-size: 20px;"></span>
				        <span v-else class="iconfont icon-weixuanzhong" style="font-size: 20px;"></span>
                    </div>
                    <img :src="item.cart_info.product.image" />
                    <div>
                      <div class="name">
                        {{ item.cart_info.product.store_name }}<template v-if="item.cart_info.productAttr">
                          {{ item.cart_info.productAttr.sku }}</template
                        >
                      </div>
                      <div v-if="item.cart_info.productAttr">
                        ￥{{ item.cart_info.productAttr.price }}<span>x{{ item.refund_num }}</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
            <td class="td">{{ item.refund_num }}</td>
            <td v-if="item.cart_info.productAttr" class="td">¥{{ item.cart_info.productAttr.price * item.refund_num }}</td>
          </tr>
        </tbody>
      </table>
      <div class="refundOrder">
          <el-button type="primary" @click="onSubmit">申请退款</el-button>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from "element-ui";
export default {
  auth: "guest",
  data() {
    return {
      orderData: {},
      myHeaders: {},
      isDialog: false,
      id: '',
      type: 1,
      numArray: [],
      rerundPrice: '',
      refund_price:'',
	    postage_price: '',
      maxRefundPrice: '',
      status: '',
      order_status: false,
      refund_type: '',
      activeId:[]
    };
  },
  async asyncData({ app, query }) {
    return {
      id: query.orderId,
      type: query.type,
      refund_type: query.refund_type
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "批量退款-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    let local = this.$cookies.get("auth.strategy");
    this.myHeaders = {
      Authorization: this.$cookies.get(`auth._token.${local}`)
    };
    this.getOrderInfo();
  },
  methods: {
    getOrderInfo(){
        let that = this;
        that.$axios.get("/api/refund/batch_product/"+that.id, {
           params: { ids: that.ids }
        }).then(res => {
          res.data.forEach(el=>{
			    el.check = false
		    })
          that.orderData = res.data;
        }).catch(err => {
          return Message.error(err);
        });
    },
    // 是否选中
    bindCheck(item){
      item.check = !item.check
      this.arrFilter()
      },
      // 筛选
    arrFilter(){
      let tempArr = this.orderData.filter(el=>{
        return el.check == true
      })
      this.activeId = []
      tempArr.map(item =>{
          this.activeId.push(item.order_product_id)
      })
    },
    onSubmit() {
      let that = this;
      if(that.activeId.length == 0){
		    return Message.error("请选择商品");
      }else{
      that.$router.push({
        path: "/refund_confirm",
        query: {
          orderId: that.id,
          type: that.type,
          ids: that.activeId.join(','),
          refund_type: that.refund_type
        }
      });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  height: 60px;
  line-height: 60px;
  color: #999999;
  .home {
    color: #282828;
  }
}
.refund {
  padding: 40px 20px 46px;
  background: #ffffff;
  .el-icon-plus {
    margin-top: 20px;
  }
  .table {
    width: 100%;
    border: 1px solid #efefef;
    border-collapse: collapse;
    thead {
      background: #efefef;
      td {
        height: 40px;
        font-size: 14px;
        text-align: center;
        color: #282828;
      }
    }

    tbody {
      .td {
        width: 219px;
        border: 1px solid #efefef;
        font-size: 14px;
        text-align: center;
        color: #282828;
        &:first-child {
          width: auto;
          padding: 20px 10px;
          text-align: left;
          span {
            margin-left: 10px;
            font-size: 12px;
            color: #b1b1b1;
          }
        }
      }
    }
    img {
      float: left;
      width: 70px;
      height: 70px;
      + div {
        float: left;
        max-width: 360px;
        margin-left: 20px;
      }
    }
    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      margin-bottom: 13px;
      overflow: hidden;
    }
  }
  .check-box{
      float: left;
      margin-right: 30px;
      .iconfont{
        font-size: 20px;
        position: relative;
        top: 20px;
       }
    }
}
.refundOrder{
    margin-top: 20px;
    text-align: right;
}
</style>
