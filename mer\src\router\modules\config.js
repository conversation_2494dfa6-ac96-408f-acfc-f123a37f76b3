// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const configRouter =
    {
      path: `${roterPre}/config`,
      name: 'config',
      meta: {
        icon: 'dashboard',
        title: leaveuKeyTerms['系统配置']
      },
      alwaysShow: true, // 一直显示根路由
      component: Layout,
      children: [
        {
          path: 'picture',
          name: 'system_config_picture',
          meta: {
            title: leaveuKeyTerms['素材管理']
          },
          component: () => import('@/views/system/config/picture')
        },
        {
          path: 'service',
          name: 'Service',
          meta: {
            title: leaveuKeyTerms['客服管理']
          },
          component: () => import('@/views/system/service/index')
        },
        {
          path: 'service_staff',
          name: 'ServiceStaff',
          meta: {
            title: leaveuKeyTerms['服务人员']
          },
          component: () => import('@/views/system/service_staff/index')
        },
        {
          path: 'guarantee',
          name: 'Guarantee',
          meta: {
            title: leaveuKeyTerms['保障服务'],
            noCache: true
          },
          component: () => import('@/views/system/guarantee/index')
        },

        {
          path: 'freight',
          name: 'Freight',
          meta: {
            title: leaveuKeyTerms['物流设置']
          },
          component: () => import('@/views/system/freight/index'),
          children: [
            {
              path: 'shippingTemplates',
              name: 'ShippingTemplates',
              meta: {
                title: leaveuKeyTerms['运费模板'],
                noCache: true
              },
              component: () => import('@/views/system/freight/shippingTemplates')
            },
            {
              path: 'express',
              name: 'Express',
              meta: {
                title: leaveuKeyTerms['物流公司'],
                noCache: true
              },
              component: () => import('@/views/system/freight/express/index')
            }
          ]
        }

      ]
    }
export default configRouter
