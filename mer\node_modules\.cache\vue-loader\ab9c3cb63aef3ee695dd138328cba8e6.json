{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue", "mtime": 1750492732712}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\r\n\r\n// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\nimport WangEditor from \"@/components/wangEditor/index.vue\";\r\nimport vuedraggable from \"vuedraggable\";\r\nimport Sortable from \"sortablejs\";\r\nimport productInfo from \"./components/productInfo.vue\";\r\nimport productSpecs from \"./components/productSpecs.vue\";\r\nimport productDetail from \"./components/productDetail.vue\";\r\nimport productOther from \"./components/productOther.vue\";\r\nimport productMarket from \"./components/productMarket.vue\";\r\nimport reservationSetting from \"./components/reservationSetting.vue\";\r\nimport reservationSpecs from \"./components/reservationSpecs.vue\";\r\nimport reservationOther from \"./components/reservationOther.vue\";\r\nimport templatesFrom from \"@/components/templatesFrom\";\r\nimport { mateName, arraysEqual } from \"@/utils\";\r\nimport {\r\n  GoodsTableHead,\r\n  VirtualTableHead,\r\n  VirtualTableHead2,\r\n  reservationTableHeard\r\n} from \"./TableHeadList.js\";\r\nimport {\r\n  shippingListApi,\r\n  templateLsitApi,\r\n  productCreateApi,\r\n  productLstDetail,\r\n  productUpdateApi,\r\n  productConfigApi,\r\n  productGetTempKeysApi,\r\n  guaranteeListApi,\r\n  productPreviewApi,\r\n  specsSelectedApi,\r\n  productSpecsDetailApi,\r\n  associatedFormList,\r\n  productUnrelatedListApi,\r\n  unitCreatApi,\r\n  productReservationCreateApi,\r\n  productReservationEditApi,\r\n  productReservationInfoApi,\r\n  attrCreatApi\r\n} from \"@/api/product\";\r\nimport { roterPre } from \"@/settings\";\r\nimport guaranteeService from \"@/components/serviceGuarantee/index\";\r\nimport previewBox from \"@/components/previewBox/index\";\r\nimport productParam from \"./components/productParam.vue\";\r\nimport attrList from \"@/components/attrList\";\r\nimport goodsList from \"@/components/goodsList\";\r\nimport SettingMer from \"@/libs/settingMer\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport taoBao from \"./taoBao\";\r\nimport copyRecord from \"./copyRecord\";\r\nimport addCarMy from \"./addCarMy\";\r\nimport cdkeyLibrary from \"./cdkeyLibrary\";\r\nconst defaultObj = {\r\n  image: \"\",\r\n  slider_image: [],\r\n  customize_time_period: [\"\"],\r\n  store_name: \"\",\r\n  store_info: \"\",\r\n  keyword: \"\",\r\n  brand_id: \"\", // 品牌id\r\n  cate_id: \"\", // 平台分类id\r\n  mer_cate_id: [], // 商户分类id\r\n  param_temp_id: \"\",\r\n  unit_name: \"\",\r\n  sort: 0,\r\n  once_max_count: 0,\r\n  is_good: 0,\r\n  is_show: 1,\r\n  auto_on_time: \"\",\r\n  auto_off_time: \"\",\r\n  temp_id: \"\",\r\n  video_link: \"\",\r\n  guarantee_template_id: \"\",\r\n  delivery_way: [],\r\n  mer_labels: [],\r\n  delivery_free: 0,\r\n  pay_limit: 0,\r\n  once_min_count: 0,\r\n  svip_price_type: 0,\r\n  refund_switch: 1,\r\n  params: [],\r\n  custom_temp_id: [],\r\n  header: [],\r\n  attrValue: [\r\n    {\r\n      image: \"\",\r\n      price: null,\r\n      cost: null,\r\n      ot_price: null,\r\n      svip_price: null,\r\n      select: false,\r\n      stock: null,\r\n      cdkey: {},\r\n      library_name: \"\",\r\n      library_id: \"\",\r\n      bar_code: \"\",\r\n      bar_code_number: \"\",\r\n      weight: null,\r\n      volume: null,\r\n      reservation: []\r\n    }\r\n  ],\r\n  specValue: [\r\n    {\r\n      price: null,\r\n      ot_price: null\r\n    }\r\n  ],\r\n  attr: [],\r\n  extension_type: 0,\r\n  integral_rate: -1,\r\n  content: \"\",\r\n  spec_type: 0,\r\n  give_coupon_ids: [],\r\n  is_gift_bag: 0,\r\n  couponData: [],\r\n  extend: [], // 自定义留言\r\n  type: 0,\r\n  product_type: 0,\r\n  is_show: 1,\r\n\r\n  // 预约设置数据\r\n  time_period: [],\r\n  reservation_time_interval: 60,\r\n  reservation_times: [],\r\n  reservation_time_type: 1,\r\n  reservation_type: 3,\r\n  reservation_start_time: \"\",\r\n  reservation_end_time: \"\",\r\n  show_num_type: 1,\r\n  sale_time_type: 1,\r\n  sale_time_start_day: \"\",\r\n  sale_time_end_day: \"\",\r\n  sale_time_week: [1, 2, 3, 4, 5, 6, 7],\r\n  show_reservation_days: 10,\r\n  is_advance: 0,\r\n  advance_time: 0,\r\n  is_cancel_reservation: 0,\r\n  cancel_reservation_time: 0,\r\n  reservation_form_type: 1,\r\n  mer_form_id: \"\"\r\n};\r\nconst objTitle = {\r\n  price: {\r\n    title: leaveuKeyTerms['售价']\r\n  },\r\n  cost: {\r\n    title: leaveuKeyTerms['成本价']\r\n  },\r\n  ot_price: {\r\n    title: leaveuKeyTerms['划线价']\r\n  },\r\n  svip_price: {\r\n    title: leaveuKeyTerms['付费会员价']\r\n  },\r\n  stock: {\r\n    title: leaveuKeyTerms['库存']\r\n  },\r\n  bar_code: {\r\n    title: leaveuKeyTerms['规格编码']\r\n  },\r\n  bar_code_number: {\r\n    title: leaveuKeyTerms['条形码']\r\n  },\r\n  weight: {\r\n    title: leaveuKeyTerms['重量（KG）']\r\n  },\r\n  volume: {\r\n    title: \"体积(m³)\"\r\n  }\r\n};\r\n// 定义验证规则数组，每个规则包含字段名、提示信息和验证函数\r\nconst validationRules = [\r\n  {\r\n    field: \"store_name\",\r\n    message: leaveuKeyTerms['基本信息-商品名称不能为空'],\r\n    validator: value => !value.trim()\r\n  },\r\n  // {\r\n  //   field: 'unit_name',\r\n  //   message: '基本信息-单位不能为空',\r\n  //   validator: value => !value\r\n  // },\r\n  {\r\n    field: \"cate_id\",\r\n    message: leaveuKeyTerms['基本信息-平台商品分类不能为空'],\r\n    validator: value => !value\r\n  },\r\n  {\r\n    field: \"image\",\r\n    message: leaveuKeyTerms['基本信息-商品封面图不能为空'],\r\n    validator: value => !value\r\n  },\r\n  {\r\n    field: \"slider_image\",\r\n    message: leaveuKeyTerms['基本信息-商品轮播图不能为空'],\r\n    validator: value => value.length === 0\r\n  }\r\n];\r\nexport default {\r\n  name: \"ProductProductAdd\",\r\n  components: {\r\n    WangEditor,\r\n    guaranteeService,\r\n    previewBox,\r\n    attrList,\r\n    goodsList,\r\n    taoBao,\r\n    copyRecord,\r\n    addCarMy,\r\n    cdkeyLibrary,\r\n    draggable: vuedraggable,\r\n    templatesFrom,\r\n    productInfo,\r\n    productSpecs,\r\n    productDetail,\r\n    productMarket,\r\n    productParam,\r\n    productOther,\r\n    reservationSetting,\r\n    reservationOther,\r\n    reservationSpecs\r\n  },\r\n  data() {\r\n    const url =\r\n      SettingMer.https + \"/upload/image/0/file?ueditor=1&token=\" + getToken();\r\n    return {\r\n      roterPre: roterPre,\r\n      baseURL: SettingMer.httpUrl || \"http://localhost:8080\",\r\n      formUrl: \"\",\r\n      tabs: [],\r\n      fullscreenLoading: false,\r\n      props: { emitPath: false },\r\n      active: 0,\r\n      deduction_set: -1,\r\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])], // 单规格\r\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])], // 多规格\r\n      ruleList: [],\r\n      createBnt: true,\r\n      showIput: false,\r\n      merCateList: [], // 商户分类筛选\r\n      categoryList: [], // 平台分类筛选\r\n      shippingList: [], // 运费模板\r\n      guaranteeList: [], // 服务保障模板\r\n      BrandList: [], // 品牌\r\n      deliveryList: [],\r\n      labelList: [], // 商品标签\r\n      formData: [], //表单数据\r\n      formThead: Object.assign({}, objTitle),\r\n      formValidate: Object.assign({}, defaultObj),\r\n      picValidate: true,\r\n      formDynamics: {\r\n        template_name: \"\",\r\n        template_value: []\r\n      },\r\n      manyTabTit: {},\r\n      manyTabDate: {},\r\n\r\n      // 规格数据\r\n      formDynamic: {\r\n        attrsName: \"\",\r\n        attrsVal: \"\"\r\n      },\r\n      isBtn: false,\r\n      images: [],\r\n      currentTab: \"1\",\r\n      isChoice: \"\",\r\n      upload: {\r\n        videoIng: false // 是否显示进度条；\r\n      },\r\n      progress: 10, // 进度条默认0\r\n      videoLink: \"\",\r\n\r\n      loading: false,\r\n      ruleValidate: {\r\n        give_coupon_ids: [\r\n          {\r\n            required: true,\r\n            message: \"请选择优惠券\",\r\n            trigger: \"change\",\r\n            type: \"array\"\r\n          }\r\n        ],\r\n        store_name: [\r\n          { required: true, message: \"请输入商品名称\", trigger: \"blur\" }\r\n        ],\r\n        cate_id: [\r\n          { required: true, message: \"请选择平台分类\", trigger: \"change\" }\r\n        ],\r\n        keyword: [\r\n          { required: true, message: \"请输入商品关键字\", trigger: \"blur\" }\r\n        ],\r\n        unit_name: [{ required: true, message: \"请输入单位\", trigger: \"blur\" }],\r\n        store_info: [\r\n          { required: true, message: \"请输入商品简介\", trigger: \"blur\" }\r\n        ],\r\n        temp_id: [\r\n          { required: true, message: \"请选择运费模板\", trigger: \"change\" }\r\n        ],\r\n        once_max_count: [\r\n          { required: true, message: \"请输入限购数量\", trigger: \"change\" }\r\n        ],\r\n        image: [{ required: true, message: \"请上传商品图\", trigger: \"change\" }],\r\n        slider_image: [\r\n          {\r\n            required: true,\r\n            message: \"请上传商品轮播图\",\r\n            type: \"array\",\r\n            trigger: \"change\"\r\n          }\r\n        ],\r\n        spec_type: [\r\n          {\r\n            required: true,\r\n            message: \"请选择商品规格\",\r\n            trigger: \"change\",\r\n            validator: (rule, value, callback) => {\r\n              if (value === undefined || value === null || value === '') {\r\n                callback(new Error('请选择商品规格'));\r\n              } else if (![0, 1, 2].includes(value)) {\r\n                callback(new Error('规格类型必须在 0,1,2 范围内'));\r\n              } else {\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        delivery_way: [\r\n          { required: true, message: \"请选择送货方式\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      attrInfo: {},\r\n      keyNum: 0,\r\n      extensionStatus: 0,\r\n      deductionStatus: 0,\r\n      previewVisible: false,\r\n      previewKey: \"\",\r\n      deliveryType: [],\r\n\r\n      customBtn: 0, // 自定义留言开关\r\n      // 自定义留言下拉选择\r\n      CustomList: [\r\n        {\r\n          value: \"text\",\r\n          label: \"文本框\"\r\n        },\r\n        {\r\n          value: \"number\",\r\n          label: \"数字\"\r\n        },\r\n        {\r\n          value: \"email\",\r\n          label: \"邮件\"\r\n        },\r\n        {\r\n          value: \"date\",\r\n          label: \"日期\"\r\n        },\r\n        {\r\n          value: \"time\",\r\n          label: \"时间\"\r\n        },\r\n        {\r\n          value: \"idCard\",\r\n          label: \"身份证\"\r\n        },\r\n        {\r\n          value: \"mobile\",\r\n          label: \"手机号\"\r\n        },\r\n        {\r\n          value: \"image\",\r\n          label: \"图片\"\r\n        }\r\n      ],\r\n      customess: {\r\n        content: []\r\n      }, // 自定义留言内容\r\n\r\n      headTab: [],\r\n      type: 0,\r\n      modals: false,\r\n      attrVal: {\r\n        price: null,\r\n        cost: null,\r\n        ot_price: null,\r\n        stock: null,\r\n        bar_code: null,\r\n        bar_code_number: null,\r\n        weight: null,\r\n        volume: null\r\n      },\r\n      specVal: {\r\n        price: null,\r\n        ot_price: null\r\n      },\r\n      open_svip: false,\r\n      svip_rate: 0,\r\n      extension_one_rate: \"\",\r\n      extension_two_rate: \"\",\r\n      deduction_ratio_rate: \"\",\r\n      customSpecs: [],\r\n      merSpecsSelect: [],\r\n      sysSpecsSelect: [],\r\n      attrs: [],\r\n      attrsList: [],\r\n      activeAtter: [],\r\n      attrShow: false,\r\n      createProduct: false,\r\n      generateArr: [],\r\n      createCount: this.$route.params.id ? 0 : -10,\r\n      virtualList: [],\r\n      formList: [],\r\n      carMyShow: false, //是否开启卡密弹窗\r\n      tabIndex: 0,\r\n      tabName: \"\",\r\n      oneFormBatch: [\r\n        {\r\n          image: \"\",\r\n          price: \"\",\r\n          cost: \"\",\r\n          ot_price: \"\",\r\n          svip_price: \"\",\r\n          stock: \"\",\r\n          cdkey: {},\r\n          code: \"\",\r\n          weight: \"\",\r\n          volume: \"\"\r\n        }\r\n      ],\r\n      headerCarMy: {\r\n        title: \"卡密设置\",\r\n        slot: \"fictitious\",\r\n        align: \"center\",\r\n        width: 95\r\n      },\r\n      product_id: \"\",\r\n      goodList: [],\r\n      unitList: [],\r\n      recommendVisible: false,\r\n      timeVal: \"\",\r\n      timeVal2: \"\",\r\n      is_timed: 0,\r\n      cdkeyId: null, //卡密库id\r\n      cdkeyLibraryInfo: null, //卡密库对象\r\n      selectedLibrary: [], //已选择的卡密库\r\n      cdkeyLibraryList: [], //可选的卡密库\r\n      columnsInstalM: [],\r\n      canSel: true, // 规格图片添加判断\r\n      changeAttrValue: \"\", //修改的规格值\r\n      tableKey: 0,\r\n      rakeBack: [\r\n        {\r\n          title: \"一级返佣(元)\",\r\n          slot: \"extension_one\",\r\n          align: \"center\",\r\n          width: 95\r\n        },\r\n        {\r\n          title: \"二级返佣(元)\",\r\n          slot: \"extension_two\",\r\n          align: \"center\",\r\n          width: 95\r\n        }\r\n      ],\r\n      manyVipPrice: \"\",\r\n      manyBrokerage: \"\",\r\n      manyBrokerageTwo: \"\"\r\n    };\r\n  },\r\n  computed: {\r\n    attrValue() {\r\n      const obj = Object.assign({}, this.attrVal);\r\n      return obj;\r\n    },\r\n    specValue() {\r\n      const obj = Object.assign({}, this.specVal);\r\n      return obj;\r\n    }\r\n  },\r\n  watch: {\r\n    \"formValidate.attr\": {\r\n      handler: function(val) {\r\n        if (this.formValidate.spec_type === 1) this.watCh(val);\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    \"$route.query.id\": {\r\n      handler: function(nVal, oVal) {\r\n        if (nVal !== oVal && nVal) {\r\n          this.initData();\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    \"$route.query.productType\": {\r\n      handler: function(nVal, oVal) {\r\n        if (nVal !== oVal && nVal) {\r\n          this.getHeaderTab();\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.tempRoute = Object.assign({}, this.$route);\r\n    if (this.$route.query.id && this.formValidate.spec_type === 1) {\r\n      this.$watch(\"formValidate.attr\", this.watCh);\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.$route.query.productType) {\r\n      this.formValidate.type = Number(this.$route.query.productType);\r\n      // 若选中的商品类型为卡密商品（ID 为 3），调用获取卡密库列表的方法\r\n      if (this.formValidate.type === 3) {\r\n        this.getCdkeyLibraryList();\r\n      }\r\n\r\n      // 获取商品配置信息\r\n      this.productCon();\r\n\r\n      // 根据商品类型显示规格信息\r\n      this.showSpecsByType();\r\n\r\n      // 修正拼写错误，将 arrs 改为 attrs\r\n      this.generateHeader(this.attrs);\r\n\r\n      // 触发 generateHeader 事件并传递规格数据\r\n      this.$emit(\"generateHeader\", this.attrs);\r\n    }\r\n\r\n    this.initData();\r\n    this.getHeaderTab();\r\n  },\r\n\r\n  destroyed() {\r\n    window.removeEventListener(\"popstate\", this.goBack, false);\r\n  },\r\n  methods: {\r\n    /**\r\n     * @description: 商品信息tab=1的相关方法\r\n     */\r\n\r\n    // 获取商品配置信息\r\n    productCon() {\r\n      productConfigApi()\r\n        .then(res => {\r\n          this.extensionStatus = Number(res.data.extension_status);\r\n          this.deductionStatus = res.data.integral_status;\r\n          this.deliveryType = res.data.delivery_way.map(String);\r\n          this.open_svip =\r\n            res.data.mer_svip_status == 1 && res.data.svip_switch_status == 1;\r\n          this.svip_rate = Number(res.data.svip_store_rate);\r\n          this.extension_one_rate = res.data.extension_one_rate + \"\";\r\n          this.extension_two_rate = res.data.extension_two_rate + \"\";\r\n          this.deduction_ratio_rate = res.data.integral_rate;\r\n          const name =\r\n            this.formValidate.type == 0\r\n              ? \"快递配送\"\r\n              : this.formValidate.type == 1\r\n              ? \"虚拟发货\"\r\n              : \"卡密发货\";\r\n          if (!this.$route.params.id) {\r\n            this.formValidate.delivery_way = this.deliveryType;\r\n          }\r\n          if (this.deliveryType.length == 2) {\r\n            if (this.formValidate.type == 2 || this.formValidate.type == 3) {\r\n              this.deliveryList = [{ value: \"2\", name: name }];\r\n            } else {\r\n              this.deliveryList = [\r\n                { value: \"1\", name: leaveuKeyTerms['到店自提'] },\r\n                { value: \"2\", name: name }\r\n              ];\r\n            }\r\n          } else {\r\n            if (\r\n              this.deliveryType.length == 1 &&\r\n              this.deliveryType[0] == \"1\" &&\r\n              this.formValidate.type != 2\r\n            ) {\r\n              this.deliveryList = [{ value: \"1\", name: leaveuKeyTerms['到店自提'] }];\r\n            } else {\r\n              this.deliveryList = [{ value: \"2\", name: name }];\r\n              this.formValidate.delivery_way = [\"2\"];\r\n            }\r\n          }\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 动态tab头部数据\r\n    getHeaderTab() {\r\n      this.headTab = [\r\n        { title: leaveuKeyTerms['商品信息'], name: \"1\" },\r\n        { title: leaveuKeyTerms['规格设置'], name: \"2\" },\r\n        { title: leaveuKeyTerms['商品详情'], name: \"3\" },\r\n        ...(this.formValidate.type === 4\r\n          ? [{ title: leaveuKeyTerms['预约设置'], name: \"7\" }]\r\n          : []),\r\n        { title: leaveuKeyTerms['营销设置'], name: \"4\" },\r\n        { title: leaveuKeyTerms['商品参数'], name: \"5\" },\r\n        { title: leaveuKeyTerms['其他设置'], name: \"6\" }\r\n      ];\r\n    },\r\n\r\n    // 根据商品类型判断是否显示重量体积\r\n    showSpecsByType() {\r\n      if (this.formValidate.type == 2 || this.formValidate.type == 3) {\r\n        delete this.attrValue.weight;\r\n        delete this.attrValue.volume;\r\n      } else {\r\n        this.attrValue.weight = \"\";\r\n        this.attrValue.volume = \"\";\r\n      }\r\n    },\r\n\r\n    // 根据商品平台分类获取参数模板\r\n    getSpecsLst(info, isData) {\r\n      let cate_id = info ? info.cate_id : this.formValidate.cate_id;\r\n      specsSelectedApi({ cate_id: cate_id })\r\n        .then(res => {\r\n          this.merSpecsSelect = res.data.mer || [];\r\n          this.sysSpecsSelect = res.data.sys || [];\r\n          if (this.$route.query.type == 1 && isData) {\r\n            this.infoData(info, \"taobao\");\r\n          }\r\n          if (this.$route.query.type != 1 && isData) {\r\n            this.infoData(info);\r\n          }\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 获取参数模板详情数据\r\n    getSpecsList() {\r\n      let merParams = [...this.formValidate.custom_temp_id],\r\n        sysParams = [...[this.formValidate.param_temp_id]];\r\n      let params = [...merParams, ...sysParams];\r\n      if (params.length <= 0) {\r\n        this.formValidate.merParams = [];\r\n        this.formValidate.sysParams = [];\r\n      } else {\r\n        productSpecsDetailApi({\r\n          template_ids: params.toString()\r\n        })\r\n          .then(res => {\r\n            let arr = [];\r\n            this.formValidate.params.forEach((item, i) => {\r\n              if (!item.parameter_id) arr.push(item);\r\n            });\r\n            this.formValidate.params = [...arr, ...res.data];\r\n          })\r\n          .catch(res => {\r\n            this.$message.error(res.message);\r\n          });\r\n      }\r\n    },\r\n\r\n    // 根据不同商品类型动态生成商品规格表头\r\n    generateHeader(data) {\r\n      let array = [];\r\n      data.forEach(item => {\r\n        if (item.detail.length === 0) {\r\n          return this.$message.error(`请添加${item.value}的规格值`);\r\n        } else {\r\n          array.push({\r\n            title: item.value,\r\n            key: item.value,\r\n            minWidth: 140,\r\n            fixed: \"left\"\r\n          });\r\n        }\r\n      });\r\n      let specificationsColumns = array;\r\n      let arr;\r\n      if (this.formValidate.type == 2) {\r\n        arr = [...specificationsColumns, ...VirtualTableHead];\r\n        // 找到slot 等于 fictitious 将title改为规格名称\r\n        this.formValidate.header.map(item => {\r\n          if (item.slot === \"fictitious\") {\r\n            item.title = leaveuKeyTerms['云盘设置'];\r\n          }\r\n        });\r\n      } else if (this.formValidate.type == 3) {\r\n        //卡密商品\r\n        arr = [...specificationsColumns, ...VirtualTableHead2];\r\n      } else if (this.formValidate.type == 4) {\r\n        arr = [...specificationsColumns, ...reservationTableHeard];\r\n      } else {\r\n        arr = [...specificationsColumns, ...GoodsTableHead];\r\n      }\r\n      this.$set(this.formValidate, \"header\", arr);\r\n      this.tableKey += 1;\r\n      this.columnsInstalM = arr;\r\n    },\r\n\r\n    /**\r\n     * @description: 商品规格tab==2的相关方法\r\n     */\r\n\r\n    //  添加新规格\r\n    handleAddRole() {\r\n      let data = {\r\n        value: this.formDynamic.attrsName,\r\n        add_pic: 0,\r\n        detail: []\r\n      };\r\n      this.attrs.push(data);\r\n    },\r\n\r\n    // 子传父修改数据\r\n    setAttrs(val, packageConfig = null, extraServicesConfig = null) {\r\n      this.attrs = val;\r\n      // 设置规格属性\r\n      this.formValidate.attr = val;\r\n\r\n      // 检查是否是服务包数据（包含Packages规格）\r\n      const isServicePackage = val.some(attr => attr.value === 'Packages');\r\n\r\n      if (isServicePackage) {\r\n        // 如果是服务包，确保切换到多规格模式\r\n        this.formValidate.spec_type = 1;\r\n      }\r\n\r\n      // 强制重新生成表头和数据\r\n      this.generateHeader(this.attrs);\r\n      this.generateAttr(this.attrs);\r\n\r\n      // 如果是服务包，需要设置价格（等待generateAttr完成后）\r\n      if (isServicePackage && packageConfig) {\r\n        this.$nextTick(() => {\r\n          // 再等一个tick确保ManyAttrValue已经更新\r\n          this.$nextTick(() => {\r\n            this.setServicePackagePrices(packageConfig, extraServicesConfig);\r\n            this.$forceUpdate();\r\n          });\r\n        });\r\n      } else {\r\n        // 确保界面更新\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n\r\n    // 设置服务包价格\r\n    setServicePackagePrices(packageConfig, extraServicesConfig = null) {\r\n      console.log('设置服务包价格，配置数据：', packageConfig);\r\n      console.log('Basic配置：', packageConfig.basic);\r\n      console.log('Standard配置：', packageConfig.standard);\r\n      console.log('Premium配置：', packageConfig.premium);\r\n      console.log('额外服务配置：', extraServicesConfig);\r\n      console.log('当前ManyAttrValue：', this.ManyAttrValue);\r\n\r\n      // 为每个生成的规格行设置对应的价格\r\n      this.ManyAttrValue.forEach((item, index) => {\r\n        console.log(`处理第${index}行：`, item);\r\n        if (index === 0) return; // 跳过第一行（批量设置行）\r\n\r\n        // 处理主服务包价格（现在只处理主服务包，额外服务在前端处理）\r\n        if (item.detail && item.detail['Packages']) {\r\n          const packageName = item.detail['Packages'];\r\n          console.log('处理套餐：', packageName);\r\n          console.log('套餐详情：', item.detail);\r\n\r\n          let packagePrice = 0;\r\n\r\n          // 根据套餐名称设置价格\r\n          if (packageConfig.basic.enabled && packageName === packageConfig.basic.name) {\r\n            packagePrice = parseFloat(packageConfig.basic.price);\r\n            console.log('设置Basic价格：', packageConfig.basic.price, '转换后：', packagePrice);\r\n          } else if (packageConfig.standard.enabled && packageName === packageConfig.standard.name) {\r\n            packagePrice = parseFloat(packageConfig.standard.price);\r\n            console.log('设置Standard价格：', packageConfig.standard.price, '转换后：', packagePrice);\r\n          } else if (packageConfig.premium.enabled && packageName === packageConfig.premium.name) {\r\n            packagePrice = parseFloat(packageConfig.premium.price);\r\n            console.log('设置Premium价格：', packageConfig.premium.price, '转换后：', packagePrice);\r\n          }\r\n\r\n          // 设置服务包价格\r\n          if (packagePrice > 0) {\r\n            item.price = packagePrice;\r\n            item.ot_price = packagePrice;\r\n            item.cost = Math.round(packagePrice * 0.7); // 成本价设为售价的70%\r\n            item.stock = 999; // 服务包默认库存\r\n            console.log('最终设置的价格：', packagePrice);\r\n            console.log('完整的item：', item);\r\n          }\r\n        } else {\r\n          console.log('没有找到Packages规格或detail为空');\r\n        }\r\n      });\r\n\r\n      // 强制更新视图\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    // 规格名称改变\r\n    attrChangeValue(i, val) {\r\n      this.generateHeader(this.attrs);\r\n      this.generateAttr(this.attrs);\r\n    },\r\n\r\n    // 删除规格\r\n    handleRemoveRole(index) {\r\n      this.attrs.splice(index, 1);\r\n      if (!this.attrs.length) {\r\n        this.formValidate.header = [];\r\n        this.ManyAttrValue = [];\r\n      } else {\r\n        this.generateAttr(this.attrs);\r\n      }\r\n    },\r\n\r\n    // 删除表格中 对应属性\r\n    delAttrTable(val) {\r\n      for (let i = 0; i < this.ManyAttrValue.length; i++) {\r\n        let item = this.ManyAttrValue[i];\r\n        if (item.attr_arr && item.attr_arr.includes(val)) {\r\n          this.ManyAttrValue.splice(i, 1);\r\n          i--;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 删除规格图片\r\n    delManyImg(val, index, indexn) {\r\n      const newAttrs = [...this.attrs];\r\n      newAttrs[index].detail = [...newAttrs[index].detail];\r\n      newAttrs[index].detail[indexn] = {\r\n        ...newAttrs[index].detail[indexn],\r\n        pic: \"\"\r\n      };\r\n\r\n      this.attrs = newAttrs;\r\n      this.ManyAttrValue.forEach(item => {\r\n        if (item.attr_arr && item.attr_arr.includes(val.value)) {\r\n          item.image = \"\";\r\n        }\r\n      });\r\n    },\r\n\r\n    //添加云盘链接\r\n    addVirtual(type, index, name) {\r\n      this.tabIndex = index;\r\n      this.tabName = name;\r\n      if (type == 0) {\r\n        this.$refs.addCarMy.carMyShow = true;\r\n        this.virtualListClear();\r\n        this.$refs.addCarMy.fixedCar = {\r\n          is_type: 0,\r\n          key: \"\",\r\n          stock: 0\r\n        };\r\n      } else {\r\n        this.getSelectedLiarbry();\r\n        this.cdkeyLibraryInfo = {};\r\n        this.$refs.cdkeyLibrary.cdkeyShow = true;\r\n      }\r\n    },\r\n\r\n    // 查看云盘链接\r\n    seeVirtual(type, data, name, index) {\r\n      this.tabName = name;\r\n      this.tabIndex = index;\r\n      if (type == 0) {\r\n        this.virtualListClear();\r\n        this.$refs.addCarMy.fixedCar = {\r\n          is_type: 0,\r\n          key: \"\",\r\n          stock: 0\r\n        };\r\n        if (\r\n          data.cdkey &&\r\n          data.cdkey.list &&\r\n          data.cdkey.list.length &&\r\n          data.cdkey.is_type == 1\r\n        ) {\r\n          this.$refs.addCarMy.fixedCar.is_type = 1;\r\n          this.virtualList = data.cdkey.list;\r\n        } else if (data.cdkey && data.cdkey.key) {\r\n          this.$refs.addCarMy.fixedCar.is_type = 0;\r\n          this.$refs.addCarMy.fixedCar.key = data.cdkey.key;\r\n          this.$refs.addCarMy.fixedCar.stock = data.stock;\r\n        }\r\n        this.$refs.addCarMy.carMyShow = true;\r\n      } else {\r\n        this.cdkeyLibraryInfo = {\r\n          id: data.library_id,\r\n          name: data.library_name\r\n        };\r\n\r\n        this.getSelectedLiarbry(data);\r\n        this.$refs.cdkeyLibrary.cdkeyShow = true;\r\n      }\r\n    },\r\n\r\n    //提交云盘链接\r\n    fixdBtn(e) {\r\n      if (e.is_type == 0) {\r\n        this.$set(this[this.tabName][this.tabIndex][\"cdkey\"], \"key\", e.key);\r\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", Number(e.stock));\r\n        this[this.tabName][this.tabIndex][\"cdkey\"].list = [];\r\n      } else {\r\n        this.$set(this[this.tabName][this.tabIndex][\"cdkey\"], \"list\", e.list);\r\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", e.list.length);\r\n        this[this.tabName][this.tabIndex][\"cdkey\"].key = \"\";\r\n      }\r\n      this.$set(\r\n        this[this.tabName][this.tabIndex][\"cdkey\"],\r\n        \"is_type\",\r\n        e.is_type\r\n      );\r\n      this.$refs.addCarMy.carMyShow = false;\r\n    },\r\n\r\n    // 关闭云盘弹窗\r\n    closeCarMy() {\r\n      this.$refs.addCarMy.carMyShow = false;\r\n    },\r\n\r\n    // 清除属性\r\n    batchDel() {\r\n      this.oneFormBatch = [\r\n        {\r\n          image: \"\",\r\n          price: \"\",\r\n          cost: \"\",\r\n          ot_price: \"\",\r\n          stock: \"\",\r\n          bar_code: \"\",\r\n          weight: \"\",\r\n          volume: \"\",\r\n          virtual_list: []\r\n        }\r\n      ];\r\n    },\r\n\r\n    //卡密列表\r\n    getCdkeyLibraryList() {\r\n      productUnrelatedListApi().then(res => {\r\n        this.cdkeyLibraryList = res.data.data;\r\n      });\r\n    },\r\n\r\n    //添加倒入卡密的值\r\n    changeVirtual(e) {\r\n      this.virtualList = this.virtualList.concat(e);\r\n    },\r\n\r\n    // 取出来已选择的卡密库\r\n    getSelectedLiarbry(data, array) {\r\n      this.selectedLibrary = [];\r\n      array.forEach((item, index) => {\r\n        if (item.library_id) this.selectedLibrary.push(item.library_id);\r\n      });\r\n    },\r\n\r\n    //选择卡密库回调\r\n    handlerChangeCdkeyIdSubSuccess(row) {\r\n      if (!row) {\r\n        this.$set(this[this.tabName][this.tabIndex], \"cdkeyLibrary\", {});\r\n        this.$set(this[this.tabName][this.tabIndex], \"library_name\", \"\");\r\n        this.$set(this[this.tabName][this.tabIndex], \"library_id\", 0);\r\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", 0);\r\n      } else {\r\n        this.$set(this[this.tabName][this.tabIndex], \"library_id\", row.id);\r\n        this.$set(\r\n          this[this.tabName][this.tabIndex][\"cdkeyLibrary\"],\r\n          \"name\",\r\n          row.name\r\n        );\r\n        this.$set(\r\n          this[this.tabName][this.tabIndex],\r\n          \"stock\",\r\n          row.total_num - row.used_num\r\n        );\r\n      }\r\n    },\r\n\r\n    //提交属性值；\r\n    subAttrs(e) {\r\n      let selectData = [];\r\n      this.attrsList.forEach((el, index) => {\r\n        let obj = [];\r\n        el.details.forEach(label => {\r\n          if (label.select) {\r\n            obj.push(label.name);\r\n          }\r\n        });\r\n        if (obj.length) {\r\n          selectData.push(obj);\r\n        }\r\n      });\r\n      let newData = [];\r\n      if (selectData.length) {\r\n        newData = this.doCombination(selectData);\r\n      }\r\n      this.attrShow = false;\r\n      this.activeAtter = selectData;\r\n      this.oneFormBatch[0].attr = newData.length ? newData.join(\";\") : \"全部\";\r\n      let manyAttr = this.ManyAttrValue;\r\n      manyAttr.forEach(j => {\r\n        this.$set(j, \"select\", false);\r\n        if (newData.length) {\r\n          newData.forEach(item => {\r\n            if (j.sku && j.sku.split(\"\").length == item.split(\"\").length) {\r\n              if (j.sku == item) {\r\n                this.$set(j, \"select\", true);\r\n              }\r\n            } else {\r\n              if (j.sku && j.sku == item) {\r\n                this.$set(j, \"select\", true);\r\n              }\r\n            }\r\n          });\r\n        } else {\r\n          this.$set(j, \"select\", true);\r\n        }\r\n      });\r\n      this.$nextTick(function() {\r\n        this.$set(this, \"ManyAttrValue\", manyAttr);\r\n      });\r\n    },\r\n\r\n    watCh(val) {\r\n      const tmp = {};\r\n      const tmpTab = {};\r\n      this.formValidate.attr.forEach((o, i) => {\r\n        tmp[\"value\" + i] = { title: o.value };\r\n        tmpTab[\"value\" + i] = o.detail;\r\n      });\r\n      // this.ManyAttrValue = this.attrFormat(val)\r\n      this.manyTabTit = tmp;\r\n      this.manyTabDate = tmpTab;\r\n      this.formThead = Object.assign({}, this.formThead, tmp);\r\n    },\r\n\r\n    //清空卡密\r\n    virtualListClear() {\r\n      this.virtualList = [\r\n        {\r\n          is_type: 0,\r\n          key: \"\",\r\n          stock: \"\"\r\n        }\r\n      ];\r\n    },\r\n\r\n    /**\r\n     * @description: 商品详情tab==3的相关方法\r\n     */\r\n\r\n    // 商品详情\r\n    getEditorContent(data) {\r\n      this.formValidate.content = data;\r\n    },\r\n\r\n    /**\r\n     * @description: 营销设置tab==4的相关方法\r\n     */\r\n\r\n    // 选择店铺推荐商品\r\n    openRecommend() {\r\n      this.recommendVisible = true;\r\n    },\r\n\r\n    /**\r\n     * @description: 其他设置tab==6的相关方法\r\n     */\r\n\r\n    // 运费模板\r\n    addTem() {\r\n      this.$refs.templateForm.dialogVisible = true;\r\n      this.$refs.templateForm.resetData();\r\n    },\r\n\r\n    // 系统表单下拉数据\r\n    getFormList() {\r\n      associatedFormList()\r\n        .then(res => {\r\n          this.formList = res.data;\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 关联的表单信息\r\n    getFormInfo() {\r\n      if (!this.formValidate.mer_form_id) {\r\n        return;\r\n      } else {\r\n        let time = new Date().getTime() * 1000;\r\n        let formUrl = `${\r\n          this.baseURL\r\n        }/pages/admin/system_form/index?inner_frame=1&time=${time}&form_id=${\r\n          this.formValidate.mer_form_id\r\n        }`;\r\n        this.formUrl = formUrl;\r\n      }\r\n    },\r\n\r\n    // 添加服务保障模板\r\n    addServiceTem() {\r\n      this.$refs.serviceGuarantee.add();\r\n    },\r\n\r\n    /**\r\n     * 页面点击操作的相关方法\r\n     */\r\n\r\n    // 表单验证\r\n    validate(prop, status, error) {\r\n      if (status === false) {\r\n        this.$message.warning(error);\r\n      }\r\n    },\r\n\r\n    // 返回上一页\r\n    goBack() {\r\n      sessionStorage.clear();\r\n      window.history.back();\r\n    },\r\n\r\n    // 点击上一步\r\n    handleSubmitUp() {\r\n      if (this.formValidate.type === 4) {\r\n        if (this.currentTab === \"7\") {\r\n          this.currentTab = \"3\";\r\n        } else if (this.currentTab === \"4\") {\r\n          this.currentTab = \"7\";\r\n        } else {\r\n          this.currentTab = (Number(this.currentTab) - 1).toString();\r\n        }\r\n      } else {\r\n        this.currentTab = (Number(this.currentTab) - 1).toString();\r\n      }\r\n    },\r\n\r\n    // 点击下一步\r\n    handleSubmitNest(name) {\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          if (this.formValidate.type == 4) {\r\n            if (this.currentTab == 7) {\r\n              this.currentTab = \"4\";\r\n            } else if (this.currentTab == 3) {\r\n              this.currentTab = \"7\";\r\n            } else {\r\n              this.currentTab = (Number(this.currentTab) + 1).toString();\r\n            }\r\n          } else {\r\n            this.currentTab = (Number(this.currentTab) + 1).toString();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    switchTimed(val) {\r\n      this.is_timed = val;\r\n    },\r\n    // 点击提交按钮\r\n    handleSubmit(name) {\r\n      // 检查 reservationSetting 引用是否存在，避免出现未定义错误\r\n      this.$store.dispatch(\"settings/setEdit\", false);\r\n      let ids = [];\r\n      this.goodList.forEach((item, index) => {\r\n        ids.push(item.product_id);\r\n      });\r\n\r\n      this.formValidate.good_ids = ids;\r\n      this.formValidate.auto_off_time = this.is_timed\r\n        ? this.formValidate.auto_off_time\r\n        : \"\";\r\n      // 遍历验证规则数组进行验证\r\n      for (const rule of validationRules) {\r\n        const { field, message, validator } = rule;\r\n        if (validator(this.formValidate[field])) {\r\n          return this.$message.warning(message);\r\n        }\r\n      }\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          if (this.formValidate.spec_type == 1) {\r\n            // 多规格模式（包括服务包转换后的多规格）\r\n            if (this.ManyAttrValue.length < 2)\r\n              return this.$message.warning(\"商品规格-规格数量最少1个\");\r\n            // 删除第一项\r\n            let newData = JSON.parse(JSON.stringify(this.ManyAttrValue));\r\n            newData.shift();\r\n            this.formValidate.attrValue = newData;\r\n          } else {\r\n            this.formValidate.attrValue = this.OneattrValue;\r\n            this.formValidate.attr = [];\r\n          }\r\n\r\n          // 预约商品的数据和验证逻辑\r\n          if (this.formValidate.type == 4) {\r\n            if (\r\n              this.formValidate.reservation_time_type == 1 &&\r\n              !this.formValidate.reservation_start_time\r\n            ) {\r\n              return this.$message.warning(\"请选择预约时间段\");\r\n            }\r\n            if (\r\n              !this.formValidate.time_period &&\r\n              this.formValidate.time_period.length\r\n            ) {\r\n              return this.$message.warning(\"请选择预约时间段\");\r\n            }\r\n            // 移除time_period中的stock字段\r\n            this.formValidate.time_period = this.formValidate.time_period.map(\r\n              ({ stock, ...rest }) => rest\r\n            );\r\n            // 检查每个属性的预约设置\r\n            const hasEmptyReservation = this.formValidate.attrValue.some(\r\n              attr => {\r\n                if (\r\n                  !attr &&\r\n                  !attr.reservation &&\r\n                  attr.reservation.length == 0\r\n                ) {\r\n                  this.$message.warning(\"请设置预约数量\");\r\n                  return true;\r\n                }\r\n\r\n                // 移除reservation中的is_show字段\r\n                attr.reservation = attr.reservation.map(\r\n                  ({\r\n                    is_show,\r\n                    start,\r\n                    end,\r\n                    stock,\r\n                    end_time,\r\n                    start_time,\r\n                    ...rest\r\n                  }) => ({\r\n                    start_time: start || start_time,\r\n                    end_time: end || end_time,\r\n                    stock: stock\r\n                  })\r\n                );\r\n                return false;\r\n              }\r\n            );\r\n            if (hasEmptyReservation) return;\r\n          }\r\n\r\n          this.fullscreenLoading = true;\r\n          this.loading = true;\r\n          let disCreate = this.$route.query.id && !this.$route.query.type;\r\n          disCreate ? this.productUpdate() : this.productCreate();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 点击预览按钮\r\n    handlePreview(name) {\r\n      if (this.formValidate.spec_type === 1) {\r\n        let newData = JSON.parse(JSON.stringify(this.ManyAttrValue));\r\n        newData.shift();\r\n        this.formValidate.attrValue = newData;\r\n      } else {\r\n        this.formValidate.attrValue = this.OneattrValue;\r\n        this.formValidate.attr = [];\r\n      }\r\n      productPreviewApi(this.formValidate)\r\n        .then(async res => {\r\n          this.previewVisible = true;\r\n          this.previewKey = res.data.preview_key;\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 添加商品\r\n    productCreate() {\r\n      const api =\r\n        this.formValidate.type === 4\r\n          ? productReservationCreateApi\r\n          : productCreateApi;\r\n      api(this.formValidate)\r\n        .then(async res => {\r\n          this.fullscreenLoading = false;\r\n          this.$message.success(res.message);\r\n          this.$router.push({ path: this.roterPre + \"/product/list\" });\r\n          this.loading = false;\r\n        })\r\n        .catch(res => {\r\n          this.fullscreenLoading = false;\r\n          this.loading = false;\r\n          this.ManyAttrValue = [\r\n            ...this.oneFormBatch,\r\n            ...this.formValidate.attrValue\r\n          ];\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 编辑商品\r\n    productUpdate() {\r\n      const api =\r\n        this.formValidate.type === 4\r\n          ? productReservationEditApi\r\n          : productUpdateApi;\r\n      api(this.$route.query.id, this.formValidate)\r\n        .then(async res => {\r\n          this.fullscreenLoading = false;\r\n          this.$message.success(res.message);\r\n          this.$router.push({ path: this.roterPre + \"/product/list\" });\r\n          this.formValidate.slider_image = [];\r\n          this.loading = false;\r\n        })\r\n        .catch(res => {\r\n          this.fullscreenLoading = false;\r\n          this.loading = false;\r\n          this.ManyAttrValue = [\r\n            ...this.oneFormBatch,\r\n            ...this.formValidate.attrValue\r\n          ];\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 商品编辑-获取商品的信息\r\n\r\n    initData() {\r\n      this.getShippingList();\r\n      this.getGuaranteeList();\r\n      this.productGetRule();\r\n      this.getFormList();\r\n      this.$store.dispatch(\"settings/setEdit\", true);\r\n      this.formValidate.slider_image = [];\r\n      if (this.$route.query.id || this.$route.query.type == \"copy\") {\r\n        this.product_id = this.$route.query.id;\r\n        this.setTagsViewTitle();\r\n        this.getInfo();\r\n      } else {\r\n        this.productCon();\r\n        if (this.deduction_set == -1) this.formValidate.integral_rate = -1;\r\n      }\r\n      if (this.$route.query.type == 1) {\r\n        this.type = this.$route.query.type;\r\n        this.$refs.taoBao.modals = true;\r\n      } else {\r\n        this.type = 0;\r\n      }\r\n    },\r\n\r\n    setTagsViewTitle() {\r\n      const title = leaveuKeyTerms['编辑商品'];\r\n      const route = Object.assign({}, this.tempRoute, {\r\n        title: `${title}-${this.$route.query.id}`\r\n      });\r\n      this.$store.dispatch(\"tagsView/updateVisitedView\", route);\r\n    },\r\n\r\n    // 获取服务保障模板\r\n    getGuaranteeList() {\r\n      guaranteeListApi().then(res => {\r\n        this.guaranteeList = res.data;\r\n      });\r\n    },\r\n\r\n    // 获取商品属性模板；\r\n    productGetRule() {\r\n      templateLsitApi().then(res => {\r\n        this.ruleList = res.data;\r\n      });\r\n    },\r\n    // 运费模板；\r\n    getShippingList() {\r\n      shippingListApi().then(res => {\r\n        this.shippingList = res.data;\r\n      });\r\n    },\r\n\r\n    doCombination(arr) {\r\n      var count = arr.length - 1; //数组长度(从0开始)\r\n      var tmp = [];\r\n      var totalArr = []; // 总数组\r\n      return doCombinationCallback(arr, 0); //从第一个开始\r\n      //js 没有静态数据，为了避免和外部数据混淆，需要使用闭包的形式\r\n      function doCombinationCallback(arr, curr_index) {\r\n        for (let val of arr[curr_index]) {\r\n          tmp[curr_index] = val; //以curr_index为索引，加入数组\r\n          //当前循环下标小于数组总长度，则需要继续调用方法\r\n          if (curr_index < count) {\r\n            doCombinationCallback(arr, curr_index + 1); //继续调用\r\n          } else {\r\n            totalArr.push(tmp.join(\",\")); //(直接给push进去，push进去的不是值，而是值的地址)\r\n          }\r\n          //js  对象都是 地址引用(引用关系)，每次都需要重新初始化，否则 totalArr的数据都会是最后一次的 tmp 数据；\r\n          let oldTmp = tmp;\r\n          tmp = [];\r\n          for (let index of oldTmp) {\r\n            tmp.push(index);\r\n          }\r\n        }\r\n        return totalArr;\r\n      }\r\n    },\r\n\r\n    getRecommend(selected) {\r\n      this.goodList =\r\n        selected && selected.length <= 18 ? selected : selected.slice(0, 18);\r\n      this.recommendVisible = false;\r\n    },\r\n    closeRecommend() {\r\n      this.recommendVisible = false;\r\n    },\r\n    // 删除店铺推荐商品\r\n    deleteRecommend(index) {\r\n      this.goodList.splice(index, 1);\r\n    },\r\n\r\n    delSpecs(index) {\r\n      this.formValidate.params.splice(index, 1);\r\n    },\r\n\r\n    attrFormat(arr) {\r\n      let data = [],\r\n        that = this;\r\n      let res = [];\r\n      return format(arr);\r\n      function format(arr) {\r\n        if (arr.length > 1) {\r\n          arr.forEach((v, i) => {\r\n            if (i === 0) data = arr[i][\"detail\"];\r\n            const tmp = [];\r\n            data.forEach(function(vv) {\r\n              arr[i + 1] &&\r\n                arr[i + 1][\"detail\"] &&\r\n                arr[i + 1][\"detail\"].forEach(g => {\r\n                  const rep2 =\r\n                    (i !== 0 ? \"\" : arr[i][\"value\"] + \"_$_\") +\r\n                    vv +\r\n                    \"-$-\" +\r\n                    arr[i + 1][\"value\"] +\r\n                    \"_$_\" +\r\n                    g;\r\n                  tmp.push(rep2);\r\n                  if (i === arr.length - 2) {\r\n                    const rep4 = {\r\n                      image: \"\",\r\n                      price: 0,\r\n                      cost: 0,\r\n                      ot_price: 0,\r\n                      select: true,\r\n                      sku: \"\",\r\n                      stock: 0,\r\n                      cdkey: {},\r\n                      cdkeyLibrary: {},\r\n                      library_name: \"\",\r\n                      library_id: \"\",\r\n                      bar_code: \"\",\r\n                      weight: 0,\r\n                      volume: 0,\r\n                      extension_one: 0,\r\n                      extension_two: 0\r\n                    };\r\n                    rep2.split(\"-$-\").forEach((h, k) => {\r\n                      const rep3 = h.split(\"_$_\");\r\n                      if (!rep4[\"detail\"]) rep4[\"detail\"] = {};\r\n                      rep4[\"detail\"][rep3[0]] = rep3.length > 1 ? rep3[1] : \"\";\r\n                    });\r\n                    // if(rep4.detail !== 'undefined' && rep4.detail !== null){\r\n                    Object.values(rep4.detail).forEach((v, i) => {\r\n                      rep4[\"value\" + i] = v;\r\n                    });\r\n                    // }\r\n                    res.push(rep4);\r\n                  }\r\n                });\r\n            });\r\n            data = tmp.length ? tmp : [];\r\n          });\r\n        } else {\r\n          const dataArr = [];\r\n          arr.forEach((v, k) => {\r\n            v[\"detail\"].forEach((vv, kk) => {\r\n              dataArr[kk] = v[\"value\"] + \"_\" + vv;\r\n              res[kk] = {\r\n                image: \"\",\r\n                price: 0,\r\n                cost: 0,\r\n                ot_price: 0,\r\n                select: true,\r\n                sku: \"\",\r\n                stock: 0,\r\n                cdkey: {},\r\n                cdkeyLibrary: {},\r\n                library_name: \"\",\r\n                library_id: \"\",\r\n                bar_code: \"\",\r\n                weight: 0,\r\n                volume: 0,\r\n                extension_one: 0,\r\n                extension_two: 0,\r\n                detail: { [v[\"value\"]]: vv }\r\n              };\r\n              Object.values(res[kk].detail).forEach((v, i) => {\r\n                res[kk][\"value\" + i] = v;\r\n              });\r\n            });\r\n          });\r\n          data.push(dataArr.join(\"__SCRIPT_BLOCK_0__\"));\r\n        }\r\n        if (that.generateArr.length > 0) {\r\n          that.generateArr.forEach((v, i) => {\r\n            res[i][\"image\"] = v.image || v.pic;\r\n            res[i][\"price\"] = v.price;\r\n            res[i][\"cost\"] = v.cost;\r\n            res[i][\"ot_price\"] = v.ot_price;\r\n            res[i][\"sku\"] = v.sku;\r\n            res[i][\"stock\"] = v.stock;\r\n            res[i][\"unique\"] = v.unique;\r\n            res[i][\"bar_code\"] = v.bar_code;\r\n            res[i][\"volume\"] = v.volume;\r\n            res[i][\"weight\"] = v.weight;\r\n            res[i][\"extension_one\"] = v.extension_one;\r\n            res[i][\"extension_two\"] = v.extension_two;\r\n            res[i][\"cdkey\"] = (v.cdkey && v.cdkey.length && v.cdkey[0]) || null;\r\n            res[i][\"cdkeyLibrary\"] = v.cdkeyLibrary || {};\r\n            res[i][\"library_name\"] = v.cdkeyLibrary && v.cdkeyLibrary.name;\r\n            res[i][\"library_id\"] = v.library_id || \"\";\r\n            res[i][\"svip_price\"] = v.svip_price || \"\";\r\n          });\r\n        }\r\n        return res;\r\n      }\r\n    },\r\n\r\n    handleFocus(val) {\r\n      this.changeAttrValue = val;\r\n    },\r\n    handleBlur() {\r\n      this.changeAttrValue = \"\";\r\n    },\r\n\r\n    //选中属性\r\n    activeAttr(e) {\r\n      this.attrsList = e;\r\n    },\r\n    //关闭属性弹窗\r\n    labelAttr() {\r\n      this.attrShow = false;\r\n    },\r\n\r\n    // 立即生成\r\n    generateAttr(data, val) {\r\n      // 判断该段Js执行时间\r\n      this.generateHeader(data);\r\n      const combinations = this.generateCombinations(data);\r\n\r\n      let rows = combinations.map(combination => {\r\n        const row = {\r\n          attr_arr: combination,\r\n          detail: {},\r\n          cdkey: {},\r\n          title: \"\",\r\n          key: \"\",\r\n          price: 0,\r\n          image: \"\",\r\n          ot_price: 0,\r\n          cost: 0,\r\n          stock: 0,\r\n          is_show: 1,\r\n          is_default_select: 0,\r\n          unique: \"\",\r\n          weight: \"\",\r\n          extension_one: 0,\r\n          extension_two: 0,\r\n          svip_price: 0\r\n        };\r\n\r\n        // 特殊处理服务包数据\r\n        if (this.isServicePackageData(data)) {\r\n          this.processServicePackageRow(row, combination, data);\r\n        }\r\n        // 判断商品类型是卡密/优惠券\r\n        let virtualType = this.formValidate.type;\r\n        if (virtualType == 3) {\r\n          //卡密\r\n          this.$set(row, \"virtual_list\", []);\r\n        } else if (virtualType == 2) {\r\n          //云盘\r\n          this.$set(row, \"cdkey\", {});\r\n          this.$set(row, \"coupon_name\", \"\");\r\n        } else if (virtualType == 4) {\r\n        }\r\n        for (let i = 0; i < combination.length; i++) {\r\n          const value = combination[i];\r\n          this.$set(row, data[i].value, value);\r\n          this.$set(row, \"title\", data[i].value);\r\n          this.$set(row, \"key\", data[i].value);\r\n          this.$set(row.detail, data[i].value, value);\r\n          // 如果manyFormValidate中存在该属性值，则赋值\r\n          for (let k = 0; k < this.ManyAttrValue.length; k++) {\r\n            const manyItem = this.ManyAttrValue[k];\r\n            // 对比两个数组是否完全相等\r\n            if (\r\n              k > 0 &&\r\n              manyItem.attr_arr &&\r\n              arraysEqual(manyItem.attr_arr, combination)\r\n            ) {\r\n              Object.assign(row, {\r\n                price: manyItem.price,\r\n                cost: manyItem.cost,\r\n                ot_price: manyItem.ot_price,\r\n                stock: manyItem.stock,\r\n                reservation: manyItem.reservation || [],\r\n                image: manyItem.image,\r\n                unique: manyItem.unique || \"\",\r\n                weight: manyItem.weight || \"\",\r\n                is_show: manyItem.is_show || 1,\r\n                is_default_select: manyItem.is_default_select || 0,\r\n                volume: manyItem.volume || 0,\r\n                is_virtual: manyItem.is_virtual,\r\n                extension_one: manyItem.extension_one,\r\n                extension_two: manyItem.extension_two,\r\n                svip_price: manyItem.svip_price\r\n              });\r\n              if (virtualType == 1) {\r\n                row.virtual_list = manyItem.virtual_list;\r\n              }\r\n            } else if (\r\n              k > 0 &&\r\n              manyItem.attr_arr.length &&\r\n              data[i].add_pic &&\r\n              combination.includes(val)\r\n            ) {\r\n              // data[i].detail中的value是规格值 存在与 manyItem.attr_arr 中的某一项\r\n              data[i].detail.map((e, ii) => {\r\n                combination.includes(e.value) &&\r\n                  this.$set(row, \"image\", e.image);\r\n              });\r\n            }\r\n          }\r\n        }\r\n        return row;\r\n      });\r\n      this.$nextTick(() => {\r\n        // rows数组第一项 新增默认数据 oneFormBatch\r\n        this.ManyAttrValue = [...this.oneFormBatch, ...rows];\r\n      });\r\n    },\r\n    // 规格值改变\r\n    attrDetailChangeValue(val, i) {\r\n      if (this.ManyAttrValue.length) {\r\n        let key = this.attrs[i].value;\r\n        this.ManyAttrValue.map((item, i) => {\r\n          if (i > 0) {\r\n            if (\r\n              Object.keys(item.detail).includes(key) &&\r\n              item.detail[key] === this.changeAttrValue\r\n            ) {\r\n              item.detail[key] = val;\r\n              let index = item.attr_arr.findIndex(\r\n                item => item === this.changeAttrValue\r\n              );\r\n              item.attr_arr[index] = val;\r\n            }\r\n          }\r\n        });\r\n        this.changeAttrValue = val;\r\n      } else {\r\n        this.generateAttr(this.attrs, 1);\r\n      }\r\n    },\r\n\r\n    // 生成规格组合\r\n    generateCombinations(arr, prefix = []) {\r\n      if (arr.length === 0) {\r\n        return [prefix];\r\n      }\r\n      const [first, ...rest] = arr;\r\n      return first.detail.flatMap(detail =>\r\n        this.generateCombinations(rest, [...prefix, detail.value])\r\n      );\r\n    },\r\n\r\n    // 判断是否为服务包数据\r\n    isServicePackageData(data) {\r\n      return data.some(attr => attr.value === 'Packages');\r\n    },\r\n\r\n    // 处理服务包行数据\r\n    processServicePackageRow(row, combination, data) {\r\n      // 查找Packages规格\r\n      const packagesAttr = data.find(attr => attr.value === 'Packages');\r\n      if (packagesAttr) {\r\n        const packageName = combination[data.indexOf(packagesAttr)];\r\n        const packageDetail = packagesAttr.detail.find(detail => detail.value === packageName);\r\n\r\n        if (packageDetail && packageDetail.data) {\r\n          // 设置服务包的价格和其他信息\r\n          row.price = packageDetail.data.price || 0;\r\n          row.ot_price = packageDetail.data.price || 0;\r\n          row.stock = 999; // 服务包默认库存\r\n\r\n          // 将服务包信息存储到detail中\r\n          row.detail.package_info = packageDetail.data;\r\n        }\r\n      }\r\n\r\n      // 处理额外服务的价格\r\n      for (let i = 0; i < combination.length; i++) {\r\n        const attrName = data[i].value;\r\n        if (attrName.startsWith('extra services')) {\r\n          const extraDetail = data[i].detail.find(detail => detail.value === combination[i]);\r\n          if (extraDetail && extraDetail.price) {\r\n            row.price += extraDetail.price;\r\n            row.ot_price += extraDetail.price;\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取接口详情\r\n    getInfo() {\r\n      this.fullscreenLoading = true;\r\n      let parmas = {};\r\n      if (this.$route.query.type == \"copy\") parmas.is_copy = 1;\r\n      let api =\r\n        this.$route.query.productType == 4\r\n          ? productReservationInfoApi\r\n          : productLstDetail;\r\n      api(this.$route.query.id, parmas)\r\n        .then(async res => {\r\n          this.generateArr = [];\r\n          let info = res.data;\r\n          this.getSpecsLst(info, true);\r\n          this.productCon();\r\n          if (info.type == 3) this.getCdkeyLibraryList();\r\n          if (info.mer_form_id) {\r\n            let time = new Date().getTime() * 1000;\r\n            let formUrl = `${\r\n              this.baseURL\r\n            }/pages/admin/system_form/index?inner_frame=1&time=${time}&form_id=${\r\n              info.mer_form_id\r\n            }`;\r\n            this.formUrl = formUrl;\r\n          }\r\n        })\r\n        .catch(res => {\r\n          this.fullscreenLoading = false;\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n\r\n    // 给商品表单赋值\r\n    infoData(data, type) {\r\n      if (type == \"taobao\") {\r\n        this.formValidate.type = Number(this.$route.query.productType);\r\n        if (this.formValidate.type == 4) {\r\n          if (data.attr.length > 0) {\r\n            data.attr = [data.attr[0]];\r\n          }\r\n        }\r\n      }\r\n      this.deduction_set = data.integral_rate == -1 ? -1 : 1;\r\n      this.goodList = data.goodList || [];\r\n      this.attrs = data.attr || [];\r\n      data.attrValue.forEach(val => {\r\n        this.$set(val, \"select\", true);\r\n        if (data.type == 3) {\r\n          this.$set(\r\n            val,\r\n            \"library_id\",\r\n            val.library_id == 0 ? \"\" : val.library_id\r\n          );\r\n        }\r\n      });\r\n      this.formValidate = data;\r\n      if (data.type != 4) {\r\n        this.formValidate.delivery_way =\r\n          data.delivery_way && data.delivery_way.length\r\n            ? data.delivery_way.map(String)\r\n            : this.deliveryType;\r\n      }\r\n\r\n      this.formValidate.temp_id = mateName(\r\n        this.shippingList,\r\n        \"shipping_template_id\",\r\n        data.temp_id\r\n      );\r\n      this.formValidate.mer_form_id = mateName(\r\n        this.formList,\r\n        \"form_id\",\r\n        data.mer_form_id\r\n      );\r\n\r\n      this.is_timed = data.auto_off_time ? 1 : 0;\r\n      this.formValidate.spec_type = Number(data.spec_type);\r\n      this.formValidate.params = data.params || [];\r\n      this.formValidate.couponData = data.coupon || [];\r\n      this.formValidate.mer_labels =\r\n        data.mer_labels && data.mer_labels.length\r\n          ? data.mer_labels.map(Number)\r\n          : [];\r\n      this.formValidate.guarantee_template_id = data.guarantee_template_id\r\n        ? data.guarantee_template_id\r\n        : \"\";\r\n\r\n      if (type == \"taobao\") {\r\n        let obj = {\r\n          reservation_time_type: 1,\r\n          reservation_type: 3,\r\n          reservation_start_time: \"\",\r\n          reservation_end_time: \"\",\r\n          show_num_type: 1,\r\n          sale_time_type: 1,\r\n          sale_time_start_day: \"\",\r\n          sale_time_end_day: \"\",\r\n          sale_time_week: [1, 2, 3, 4, 5, 6, 7],\r\n          show_reservation_days: 10,\r\n          is_advance: 0,\r\n          advance_time: 0,\r\n          is_cancel_reservation: 0,\r\n          cancel_reservation_time: 0,\r\n          reservation_form_type: 1\r\n        };\r\n        for (let key in obj) {\r\n          this.formValidate[key] = obj[key];\r\n        }\r\n      }\r\n\r\n      if (this.formValidate.type == 4) {\r\n        this.formValidate.reservation_time_type = Number(\r\n          data.reservation_time_type\r\n        );\r\n\r\n        this.formValidate.show_num_type = Number(data.show_num_type);\r\n        if (data.attrValue.length > 0) {\r\n          data.attrValue.forEach(item => {\r\n            if (item.reservation && item.reservation.length > 0) {\r\n              item.reservation.forEach(el => {\r\n                el.start = el.start_time;\r\n                el.end = el.end_time;\r\n              });\r\n            }\r\n          });\r\n        }\r\n      }\r\n      this.formValidate.type = Number(this.$route.query.productType);\r\n\r\n      if (data.spec_type == 0) {\r\n        this.ManyAttrValue = [];\r\n        (data.attrValue[0].list = []), (this.OneattrValue = data.attrValue);\r\n      } else {\r\n        if (this.formValidate.extend.length != 0) {\r\n          this.customBtn = 1;\r\n        }\r\n\r\n        this.generateHeader(this.attrs);\r\n\r\n        this.ManyAttrValue = [...this.oneFormBatch, ...data.attrValue];\r\n        if (type == \"taobao\" && this.formValidate.type == 4) {\r\n          this.ManyAttrValue = [];\r\n          this.generateAttr(this.attrs);\r\n        }\r\n      }\r\n      if (\r\n        this.formValidate.custom_temp_id.length > 0 ||\r\n        this.formValidate.param_temp_id\r\n      ) {\r\n        this.getSpecsList();\r\n      }\r\n\r\n      this.fullscreenLoading = false;\r\n      setTimeout(e => {\r\n        this.checkAllGroup(data.extension_type);\r\n      }, 1000);\r\n    },\r\n\r\n    checkAllGroup(data) {\r\n      let endLength = this.attrs.length + 3;\r\n      if (this.formValidate.spec_type === 0) {\r\n        if (data == 0) {\r\n          this.columnsInstall = this.columns2\r\n            .slice(0, endLength)\r\n            .concat(this.member);\r\n        } else if (data == 1) {\r\n          this.columnsInstall = this.columns2\r\n            .slice(0, endLength)\r\n            .concat(this.rakeBack);\r\n        } else {\r\n          this.columnsInstall = this.columns2.slice(0, endLength);\r\n        }\r\n      } else {\r\n        if (data == 0) {\r\n          this.columnsInstal2 = this.columnsInstalM\r\n            .slice(0, endLength)\r\n            .concat(this.member);\r\n        } else if (data == 1) {\r\n          this.columnsInstal2 = this.columnsInstalM\r\n            .slice(0, endLength)\r\n            .concat(this.rakeBack);\r\n        } else {\r\n          this.columnsInstal2 = this.columnsInstalM.slice(0, endLength);\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n", null]}