<template>
  <div>
     <!-- 发票信息弹窗 -->
    <el-dialog
      title="发票信息"
      :visible.sync="invoiceDialog"
      width="600px"
      :before-close="closeInvoice">
      <div class="invoice_data_container">
       <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" @submit.native.prevent>
        <el-form-item label="发票类型：">
          <div class="acea-row">
             <div v-for="(item, index) in invoiceTypeList" :key="index" class="invoice_item" :class="ruleForm.receipt_type == item.type ? 'checked' : ''">
              <div class="acea-row cont" @click="changeInvoiceType(item)" >
                <div class="acea-row row-middle name">{{item.name}}</div>
                <div class="iconfont icon-xuanzhong4"></div>
              </div>
            </div>
          </div>
          <div class="invoice_type_info">
                {{ruleForm.receipt_type == 1 ?
                '默认发送至所提供的电子邮件' :
                '纸质发票开出后将以邮寄形式交付'}}
              </div>
        </el-form-item>
        <el-form-item label="发票抬头：">
          <div class="acea-row">
             <div v-for="(item, index) in invoiceHeaderList" :key="index" class="invoice_item" :class="ruleForm.receipt_title_type == item.type ? 'checked' : ''">
              <div class="acea-row cont" @click="changeInvoiceHeader(item)" >
                <div class="acea-row row-middle name">{{item.name}}</div>
                <div class="iconfont icon-xuanzhong4"></div>
              </div>
            </div>
            <div style="margin-top: 20px; width: 100%;">
              <el-input v-model="ruleForm.receipt_title" placeholder="请填写发票抬头"></el-input>
            </div>
          </div>
        </el-form-item>
         <el-form-item v-show="ruleForm.receipt_title_type == '2'" label="单位税号：">
          <el-input v-model="ruleForm.duty_paragraph" placeholder="请填写纳税人识别号"></el-input>
        </el-form-item>
        <el-form-item label="收票邮箱：" prop="email">
          <el-input v-model="ruleForm.email" placeholder="请填写收票人邮箱"></el-input>
        </el-form-item>
        <div v-show="ruleForm.receipt_title_type == '2' && ruleForm.receipt_type == '2'">
          <el-form-item label="开户银行：">
            <el-input v-model="ruleForm.bank_name" placeholder="请填写开户银行"></el-input>
          </el-form-item>
          <el-form-item label="银行账号：">
            <el-input v-model="ruleForm.bank_code" placeholder="请填写银行账号"></el-input>
          </el-form-item>
          <el-form-item label="企业地址：">
            <el-input v-model="ruleForm.address" placeholder="请填写企业地址"></el-input>
          </el-form-item>
          <el-form-item label="企业电话：">
            <el-input v-model="ruleForm.tel" placeholder="请填写企业电话"></el-input>
          </el-form-item>
        </div>
      </el-form>
      </div>
      <span slot="footer" center class="dialog-footer">
         <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirmInvoice">确 定</el-button>
          <el-button @click="closeInvoice">取 消</el-button>
      </span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
export default {
  name: "addInvoicing",
  auth: "guest",
 
  data() {
    return {
      invoiceVisible: false,
      invoiceDialog: false,
      ruleForm: {
        receipt_title_type: 1, //发票抬头类型
        receipt_type: 1, //发票类型
        receipt_title: "", //发票抬头
        duty_paragraph: "", //税号
        email: "", //邮箱
        bank_name: "", //开户银行
        bank_code: "", //银行账号
        address: "", //企业地址
        tel: "", //电话

      },
      invoiceData: {},
      invoiceTypeList: [
        {
          type: '1',
				  name: '增值税电子普通发票'
				},
				{
				  type: '2',
				  name: '增值税专用发票'
				}
			],
      invoiceHeaderList: [
        {
          type: '1',
				  name: '个人'
				},
				{
				  type: '2',
				  name: '企业'
				}
      ],
      rules: {
        receipt_title: [
          {required: true, message: '请填写发票抬头', trigger: 'blur'}
        ]
      },
      invoiceIndex: '',
      modifyInvoice: false,
    }
  },
 
  beforeMount() {
    
  },
  methods: {
    dialogClose() {
      this.invoiceVisible = false
    },
    closeInvoice (){
      this.invoiceDialog = false  
    },
    /*打开发票弹窗*/
    showInvoicePupon(item){
      this.invoiceDialog = true;
    },
    /*选择发票类型*/
    changeInvoiceType(item) {
      this.ruleForm.receipt_type = item.type
      if(this.ruleForm.receipt_type == 2){
        this.invoiceHeaderList = [{
				  type: '2',
				  name: '企业'
				}]
        this.ruleForm.receipt_title_type = 2
      }else{
         this.invoiceHeaderList = [{
          type: '1',
				  name: '个人'
				},
				{
				  type: '2',
				  name: '企业'}]
      }
    },
    /*选择发票抬头*/
    changeInvoiceHeader(item) {
      this.ruleForm.receipt_title_type = item.type
    },
    /*提交发票信息*/
    confirmInvoice() {
      let that = this;
      let value = that.ruleForm;
			if (!value.receipt_title) return Message.error('请填写发票抬头');
			if (!value.email) return Message.error('请填写邮箱');
			if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value.email)) return Message.error('请输入正确的邮箱');
			if(value.receipt_title_type == 2){
				if (!value.duty_paragraph) return Message.error('请填写税号');
				if(value.receipt_type == '2'){
						if (!value.bank_name) return Message.error('请填写开户行');
						if (!value.bank_code) return Message.error('请填写银行账号');
						if (!value.address) return Message.error('请填写企业地址');
						if (!value.tel) return Message.error('请填写企业电话');
						if(!/^(\d{9}|\d{14}|\d{18})$/.test(value.bank_code)){
							return Message.error('请输入正确的银行账号');
						}
						if(!/(^(\d{3,4})?\d{7,8})$|(13[0-9]{9})/.test(value.tel)){
							return Message.error('请输入正确的电话号码');
						}
					}
				}
        that.getInvoiceDatas(value)
        that.invoiceDialog = false
    },
    /*获取开票信息*/
    getInvoiceDatas(value){
      let that = this;
      that.$emit('applyInvoice', value)
    },
  }
}
</script>

<style scoped lang="scss">
.invoice_description img{
  display: block;
  margin:  0 auto;
  width: 100%;
  max-width: 100%;
}
.invoice_data_container{
  padding-left: 26px;
  padding-right: 26px;
}
.invoice_item{
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 14px;
  &:last-child{
    margin-right: 0;
  }
  .cont{
      position: relative;
      height: 32px;
      border: 1px solid #d3d3d3;
      display: flex;
      align-items: center;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
        .iconfont{
          display: block;
        }
      }
      .name{
        padding: 0 40px;
        font-size: 14px;
        line-height: 32px;
      }
      .iconfont{
          position: absolute;
          right: -3px;
          bottom: -12px;
          font-size: 22px;
          display: none;
        }
}
.invoice_type_info{
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  margin-top: 10px;
}
</style>
