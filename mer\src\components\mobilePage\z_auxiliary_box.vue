<template>
    <div class="mobile-page">
        <div class="box" :style="{height:cSlider+'px',background:bgColor}"></div>
    </div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { mapState } from 'vuex'
export default {
    name: 'z_auxiliary_box',
    cname: this.$t(this.$t('辅助空白')),
    configName: 'c_auxiliary_box',
    icon: 'iconfuzhukongbai2',
    type:2,// 0 基础组件 1 营销组件 2工具组件
    defaultName:'blankPage', // 外面匹配名称
    props: {
        index: {
            type: null,
            default: -1
        },
        num: {
            type: null
        }
    },
    computed: {
        ...mapState('mobildConfig', ['defaultArray'])
    },
    watch: {
        pageData: {
            handler (nVal, oVal) {
                this.setConfig(nVal)
            },
            deep: true
        },
        num: {
            handler (nVal, oVal) {
                let data = this.$store.state.mobildConfig.defaultArray[nVal]
                this.setConfig(data)
            },
            deep: true
        },
        'defaultArray': {
            handler (nVal, oVal) {
                let data = this.$store.state.mobildConfig.defaultArray[this.num]
                this.setConfig(data);
            },
            deep: true
        }
    },
    data () {
        return {
            // 默认初始化数据禁止修改
            defaultConfig: {
                name: 'blankPage',
                timestamp: this.num,
                bgColor: {
                    title: leaveuKeyTerms['背景颜色'],
                    name: 'bgColor',
                    default: [{
                        item: '#f5f5f5'
                    }],
                    color: [
                        {
                            item: '#f5f5f5'
                        }
                    ]
                },
                heightConfig: {
                    title: leaveuKeyTerms['组件高度'],
                    val: 10,
                    min: 1
                }
            },
            cSlider: '',
            bgColor: '',
            confObj: {},
            pageData: {},
            edge: ''
        }
    },
    mounted () {
        this.$nextTick(() => {
            this.pageData = this.$store.state.mobildConfig.defaultArray[this.num]
            this.setConfig(this.pageData)
        })
    },
    methods: {
        setConfig (data) {
            if(!data) return
            if(data.heightConfig){
                this.cSlider = data.heightConfig.val;
                this.bgColor = data.bgColor.color[0].item
            }
        }
    }
}
</script>

<style scoped lang="scss">
    .box{
        height: 20px;
        background: #F5F5F5;
    }
       
</style>
