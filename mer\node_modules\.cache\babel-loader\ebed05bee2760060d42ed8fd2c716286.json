{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue", "mtime": 1750490861727}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\.babelrc", "mtime": 1749087282000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\babel.config.js", "mtime": 1735790252000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.string.starts-with\");\nrequire(\"core-js/modules/es6.array.find\");\nvar _toArray2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/toArray.js\"));\nrequire(\"core-js/modules/es6.array.find-index\");\nrequire(\"core-js/modules/es6.object.keys\");\nrequire(\"core-js/modules/es7.object.values\");\nvar _createForOfIteratorHelper2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js\"));\nvar _regenerator2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/regenerator.js\"));\nrequire(\"regenerator-runtime/runtime\");\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/objectWithoutProperties.js\"));\nrequire(\"core-js/modules/es6.regexp.split\");\nvar _objectSpread2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nvar _toConsumableArray2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es7.array.includes\");\nrequire(\"core-js/modules/es6.string.includes\");\nvar _defineProperty2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _leaveuKeyTerms = _interopRequireDefault(require(\"@/config/leaveuKeyTerms.js\"));\nvar _index = _interopRequireDefault(require(\"@/components/wangEditor/index.vue\"));\nvar _vuedraggable = _interopRequireDefault(require(\"vuedraggable\"));\nvar _sortablejs = _interopRequireDefault(require(\"sortablejs\"));\nvar _productInfo = _interopRequireDefault(require(\"./components/productInfo.vue\"));\nvar _productSpecs = _interopRequireDefault(require(\"./components/productSpecs.vue\"));\nvar _productDetail = _interopRequireDefault(require(\"./components/productDetail.vue\"));\nvar _productOther = _interopRequireDefault(require(\"./components/productOther.vue\"));\nvar _productMarket = _interopRequireDefault(require(\"./components/productMarket.vue\"));\nvar _reservationSetting = _interopRequireDefault(require(\"./components/reservationSetting.vue\"));\nvar _reservationSpecs = _interopRequireDefault(require(\"./components/reservationSpecs.vue\"));\nvar _reservationOther = _interopRequireDefault(require(\"./components/reservationOther.vue\"));\nvar _templatesFrom = _interopRequireDefault(require(\"@/components/templatesFrom\"));\nvar _utils = require(\"@/utils\");\nvar _TableHeadList = require(\"./TableHeadList.js\");\nvar _product = require(\"@/api/product\");\nvar _settings = require(\"@/settings\");\nvar _index2 = _interopRequireDefault(require(\"@/components/serviceGuarantee/index\"));\nvar _index3 = _interopRequireDefault(require(\"@/components/previewBox/index\"));\nvar _productParam = _interopRequireDefault(require(\"./components/productParam.vue\"));\nvar _attrList = _interopRequireDefault(require(\"@/components/attrList\"));\nvar _goodsList = _interopRequireDefault(require(\"@/components/goodsList\"));\nvar _settingMer = _interopRequireDefault(require(\"@/libs/settingMer\"));\nvar _auth = require(\"@/utils/auth\");\nvar _taoBao = _interopRequireDefault(require(\"./taoBao\"));\nvar _copyRecord = _interopRequireDefault(require(\"./copyRecord\"));\nvar _addCarMy = _interopRequireDefault(require(\"./addCarMy\"));\nvar _cdkeyLibrary = _interopRequireDefault(require(\"./cdkeyLibrary\"));\nvar _excluded = [\"stock\"],\n  _excluded2 = [\"is_show\", \"start\", \"end\", \"stock\", \"end_time\", \"start_time\"];\nvar _defaultObj; //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nvar defaultObj = (_defaultObj = {\n  image: \"\",\n  slider_image: [],\n  customize_time_period: [\"\"],\n  store_name: \"\",\n  store_info: \"\",\n  keyword: \"\",\n  brand_id: \"\",\n  // 品牌id\n  cate_id: \"\",\n  // 平台分类id\n  mer_cate_id: [],\n  // 商户分类id\n  param_temp_id: \"\",\n  unit_name: \"\",\n  sort: 0,\n  once_max_count: 0,\n  is_good: 0,\n  is_show: 1,\n  auto_on_time: \"\",\n  auto_off_time: \"\",\n  temp_id: \"\",\n  video_link: \"\",\n  guarantee_template_id: \"\",\n  delivery_way: [],\n  mer_labels: [],\n  delivery_free: 0,\n  pay_limit: 0,\n  once_min_count: 0,\n  svip_price_type: 0,\n  refund_switch: 1,\n  params: [],\n  custom_temp_id: [],\n  header: [],\n  attrValue: [{\n    image: \"\",\n    price: null,\n    cost: null,\n    ot_price: null,\n    svip_price: null,\n    select: false,\n    stock: null,\n    cdkey: {},\n    library_name: \"\",\n    library_id: \"\",\n    bar_code: \"\",\n    bar_code_number: \"\",\n    weight: null,\n    volume: null,\n    reservation: []\n  }],\n  specValue: [{\n    price: null,\n    ot_price: null\n  }],\n  attr: [],\n  extension_type: 0,\n  integral_rate: -1,\n  content: \"\",\n  spec_type: 0,\n  give_coupon_ids: [],\n  is_gift_bag: 0,\n  couponData: [],\n  extend: [],\n  // 自定义留言\n  type: 0,\n  product_type: 0\n}, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)(_defaultObj, \"is_show\", 1), \"time_period\", []), \"reservation_time_interval\", 60), \"reservation_times\", []), \"reservation_time_type\", 1), \"reservation_type\", 3), \"reservation_start_time\", \"\"), \"reservation_end_time\", \"\"), \"show_num_type\", 1), \"sale_time_type\", 1), (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)(_defaultObj, \"sale_time_start_day\", \"\"), \"sale_time_end_day\", \"\"), \"sale_time_week\", [1, 2, 3, 4, 5, 6, 7]), \"show_reservation_days\", 10), \"is_advance\", 0), \"advance_time\", 0), \"is_cancel_reservation\", 0), \"cancel_reservation_time\", 0), \"reservation_form_type\", 1), \"mer_form_id\", \"\"));\nvar objTitle = {\n  price: {\n    title: _leaveuKeyTerms.default['售价']\n  },\n  cost: {\n    title: _leaveuKeyTerms.default['成本价']\n  },\n  ot_price: {\n    title: _leaveuKeyTerms.default['划线价']\n  },\n  svip_price: {\n    title: _leaveuKeyTerms.default['付费会员价']\n  },\n  stock: {\n    title: _leaveuKeyTerms.default['库存']\n  },\n  bar_code: {\n    title: _leaveuKeyTerms.default['规格编码']\n  },\n  bar_code_number: {\n    title: _leaveuKeyTerms.default['条形码']\n  },\n  weight: {\n    title: _leaveuKeyTerms.default['重量（KG）']\n  },\n  volume: {\n    title: \"体积(m³)\"\n  }\n};\n// 定义验证规则数组，每个规则包含字段名、提示信息和验证函数\nvar validationRules = [{\n  field: \"store_name\",\n  message: _leaveuKeyTerms.default['基本信息-商品名称不能为空'],\n  validator: function validator(value) {\n    return !value.trim();\n  }\n},\n// {\n//   field: 'unit_name',\n//   message: '基本信息-单位不能为空',\n//   validator: value => !value\n// },\n{\n  field: \"cate_id\",\n  message: _leaveuKeyTerms.default['基本信息-平台商品分类不能为空'],\n  validator: function validator(value) {\n    return !value;\n  }\n}, {\n  field: \"image\",\n  message: _leaveuKeyTerms.default['基本信息-商品封面图不能为空'],\n  validator: function validator(value) {\n    return !value;\n  }\n}, {\n  field: \"slider_image\",\n  message: _leaveuKeyTerms.default['基本信息-商品轮播图不能为空'],\n  validator: function validator(value) {\n    return value.length === 0;\n  }\n}];\nvar _default = exports.default = {\n  name: \"ProductProductAdd\",\n  components: {\n    WangEditor: _index.default,\n    guaranteeService: _index2.default,\n    previewBox: _index3.default,\n    attrList: _attrList.default,\n    goodsList: _goodsList.default,\n    taoBao: _taoBao.default,\n    copyRecord: _copyRecord.default,\n    addCarMy: _addCarMy.default,\n    cdkeyLibrary: _cdkeyLibrary.default,\n    draggable: _vuedraggable.default,\n    templatesFrom: _templatesFrom.default,\n    productInfo: _productInfo.default,\n    productSpecs: _productSpecs.default,\n    productDetail: _productDetail.default,\n    productMarket: _productMarket.default,\n    productParam: _productParam.default,\n    productOther: _productOther.default,\n    reservationSetting: _reservationSetting.default,\n    reservationOther: _reservationOther.default,\n    reservationSpecs: _reservationSpecs.default\n  },\n  data: function data() {\n    var url = _settingMer.default.https + \"/upload/image/0/file?ueditor=1&token=\" + (0, _auth.getToken)();\n    return {\n      roterPre: _settings.roterPre,\n      baseURL: _settingMer.default.httpUrl || \"http://localhost:8080\",\n      formUrl: \"\",\n      tabs: [],\n      fullscreenLoading: false,\n      props: {\n        emitPath: false\n      },\n      active: 0,\n      deduction_set: -1,\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])],\n      // 单规格\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])],\n      // 多规格\n      ruleList: [],\n      createBnt: true,\n      showIput: false,\n      merCateList: [],\n      // 商户分类筛选\n      categoryList: [],\n      // 平台分类筛选\n      shippingList: [],\n      // 运费模板\n      guaranteeList: [],\n      // 服务保障模板\n      BrandList: [],\n      // 品牌\n      deliveryList: [],\n      labelList: [],\n      // 商品标签\n      formData: [],\n      //表单数据\n      formThead: Object.assign({}, objTitle),\n      formValidate: Object.assign({}, defaultObj),\n      picValidate: true,\n      formDynamics: {\n        template_name: \"\",\n        template_value: []\n      },\n      manyTabTit: {},\n      manyTabDate: {},\n      // 规格数据\n      formDynamic: {\n        attrsName: \"\",\n        attrsVal: \"\"\n      },\n      isBtn: false,\n      images: [],\n      currentTab: \"1\",\n      isChoice: \"\",\n      upload: {\n        videoIng: false // 是否显示进度条；\n      },\n      progress: 10,\n      // 进度条默认0\n      videoLink: \"\",\n      loading: false,\n      ruleValidate: {\n        give_coupon_ids: [{\n          required: true,\n          message: \"请选择优惠券\",\n          trigger: \"change\",\n          type: \"array\"\n        }],\n        store_name: [{\n          required: true,\n          message: \"请输入商品名称\",\n          trigger: \"blur\"\n        }],\n        cate_id: [{\n          required: true,\n          message: \"请选择平台分类\",\n          trigger: \"change\"\n        }],\n        keyword: [{\n          required: true,\n          message: \"请输入商品关键字\",\n          trigger: \"blur\"\n        }],\n        unit_name: [{\n          required: true,\n          message: \"请输入单位\",\n          trigger: \"blur\"\n        }],\n        store_info: [{\n          required: true,\n          message: \"请输入商品简介\",\n          trigger: \"blur\"\n        }],\n        temp_id: [{\n          required: true,\n          message: \"请选择运费模板\",\n          trigger: \"change\"\n        }],\n        once_max_count: [{\n          required: true,\n          message: \"请输入限购数量\",\n          trigger: \"change\"\n        }],\n        image: [{\n          required: true,\n          message: \"请上传商品图\",\n          trigger: \"change\"\n        }],\n        slider_image: [{\n          required: true,\n          message: \"请上传商品轮播图\",\n          type: \"array\",\n          trigger: \"change\"\n        }],\n        spec_type: [{\n          required: true,\n          message: \"请选择商品规格\",\n          trigger: \"change\",\n          validator: function validator(rule, value, callback) {\n            if (value === undefined || value === null || value === '') {\n              callback(new Error('请选择商品规格'));\n            } else if (![0, 1, 2].includes(value)) {\n              callback(new Error('规格类型必须在 0,1,2 范围内'));\n            } else {\n              callback();\n            }\n          }\n        }],\n        delivery_way: [{\n          required: true,\n          message: \"请选择送货方式\",\n          trigger: \"change\"\n        }]\n      },\n      attrInfo: {},\n      keyNum: 0,\n      extensionStatus: 0,\n      deductionStatus: 0,\n      previewVisible: false,\n      previewKey: \"\",\n      deliveryType: [],\n      customBtn: 0,\n      // 自定义留言开关\n      // 自定义留言下拉选择\n      CustomList: [{\n        value: \"text\",\n        label: \"文本框\"\n      }, {\n        value: \"number\",\n        label: \"数字\"\n      }, {\n        value: \"email\",\n        label: \"邮件\"\n      }, {\n        value: \"date\",\n        label: \"日期\"\n      }, {\n        value: \"time\",\n        label: \"时间\"\n      }, {\n        value: \"idCard\",\n        label: \"身份证\"\n      }, {\n        value: \"mobile\",\n        label: \"手机号\"\n      }, {\n        value: \"image\",\n        label: \"图片\"\n      }],\n      customess: {\n        content: []\n      },\n      // 自定义留言内容\n\n      headTab: [],\n      type: 0,\n      modals: false,\n      attrVal: {\n        price: null,\n        cost: null,\n        ot_price: null,\n        stock: null,\n        bar_code: null,\n        bar_code_number: null,\n        weight: null,\n        volume: null\n      },\n      specVal: {\n        price: null,\n        ot_price: null\n      },\n      open_svip: false,\n      svip_rate: 0,\n      extension_one_rate: \"\",\n      extension_two_rate: \"\",\n      deduction_ratio_rate: \"\",\n      customSpecs: [],\n      merSpecsSelect: [],\n      sysSpecsSelect: [],\n      attrs: [],\n      attrsList: [],\n      activeAtter: [],\n      attrShow: false,\n      createProduct: false,\n      generateArr: [],\n      createCount: this.$route.params.id ? 0 : -10,\n      virtualList: [],\n      formList: [],\n      carMyShow: false,\n      //是否开启卡密弹窗\n      tabIndex: 0,\n      tabName: \"\",\n      oneFormBatch: [{\n        image: \"\",\n        price: \"\",\n        cost: \"\",\n        ot_price: \"\",\n        svip_price: \"\",\n        stock: \"\",\n        cdkey: {},\n        code: \"\",\n        weight: \"\",\n        volume: \"\"\n      }],\n      headerCarMy: {\n        title: \"卡密设置\",\n        slot: \"fictitious\",\n        align: \"center\",\n        width: 95\n      },\n      product_id: \"\",\n      goodList: [],\n      unitList: [],\n      recommendVisible: false,\n      timeVal: \"\",\n      timeVal2: \"\",\n      is_timed: 0,\n      cdkeyId: null,\n      //卡密库id\n      cdkeyLibraryInfo: null,\n      //卡密库对象\n      selectedLibrary: [],\n      //已选择的卡密库\n      cdkeyLibraryList: [],\n      //可选的卡密库\n      columnsInstalM: [],\n      canSel: true,\n      // 规格图片添加判断\n      changeAttrValue: \"\",\n      //修改的规格值\n      tableKey: 0,\n      rakeBack: [{\n        title: \"一级返佣(元)\",\n        slot: \"extension_one\",\n        align: \"center\",\n        width: 95\n      }, {\n        title: \"二级返佣(元)\",\n        slot: \"extension_two\",\n        align: \"center\",\n        width: 95\n      }],\n      manyVipPrice: \"\",\n      manyBrokerage: \"\",\n      manyBrokerageTwo: \"\"\n    };\n  },\n  computed: {\n    attrValue: function attrValue() {\n      var obj = Object.assign({}, this.attrVal);\n      return obj;\n    },\n    specValue: function specValue() {\n      var obj = Object.assign({}, this.specVal);\n      return obj;\n    }\n  },\n  watch: {\n    \"formValidate.attr\": {\n      handler: function handler(val) {\n        if (this.formValidate.spec_type === 1) this.watCh(val);\n      },\n      immediate: false,\n      deep: true\n    },\n    \"$route.query.id\": {\n      handler: function handler(nVal, oVal) {\n        if (nVal !== oVal && nVal) {\n          this.initData();\n        }\n      },\n      immediate: false,\n      deep: true\n    },\n    \"$route.query.productType\": {\n      handler: function handler(nVal, oVal) {\n        if (nVal !== oVal && nVal) {\n          this.getHeaderTab();\n        }\n      },\n      immediate: false,\n      deep: true\n    }\n  },\n  created: function created() {\n    this.tempRoute = Object.assign({}, this.$route);\n    if (this.$route.query.id && this.formValidate.spec_type === 1) {\n      this.$watch(\"formValidate.attr\", this.watCh);\n    }\n  },\n  mounted: function mounted() {\n    if (this.$route.query.productType) {\n      this.formValidate.type = Number(this.$route.query.productType);\n      // 若选中的商品类型为卡密商品（ID 为 3），调用获取卡密库列表的方法\n      if (this.formValidate.type === 3) {\n        this.getCdkeyLibraryList();\n      }\n\n      // 获取商品配置信息\n      this.productCon();\n\n      // 根据商品类型显示规格信息\n      this.showSpecsByType();\n\n      // 修正拼写错误，将 arrs 改为 attrs\n      this.generateHeader(this.attrs);\n\n      // 触发 generateHeader 事件并传递规格数据\n      this.$emit(\"generateHeader\", this.attrs);\n    }\n    this.initData();\n    this.getHeaderTab();\n  },\n  destroyed: function destroyed() {\n    window.removeEventListener(\"popstate\", this.goBack, false);\n  },\n  methods: {\n    /**\r\n     * @description: 商品信息tab=1的相关方法\r\n     */\n    // 获取商品配置信息\n    productCon: function productCon() {\n      var _this = this;\n      (0, _product.productConfigApi)().then(function (res) {\n        _this.extensionStatus = Number(res.data.extension_status);\n        _this.deductionStatus = res.data.integral_status;\n        _this.deliveryType = res.data.delivery_way.map(String);\n        _this.open_svip = res.data.mer_svip_status == 1 && res.data.svip_switch_status == 1;\n        _this.svip_rate = Number(res.data.svip_store_rate);\n        _this.extension_one_rate = res.data.extension_one_rate + \"\";\n        _this.extension_two_rate = res.data.extension_two_rate + \"\";\n        _this.deduction_ratio_rate = res.data.integral_rate;\n        var name = _this.formValidate.type == 0 ? \"快递配送\" : _this.formValidate.type == 1 ? \"虚拟发货\" : \"卡密发货\";\n        if (!_this.$route.params.id) {\n          _this.formValidate.delivery_way = _this.deliveryType;\n        }\n        if (_this.deliveryType.length == 2) {\n          if (_this.formValidate.type == 2 || _this.formValidate.type == 3) {\n            _this.deliveryList = [{\n              value: \"2\",\n              name: name\n            }];\n          } else {\n            _this.deliveryList = [{\n              value: \"1\",\n              name: _leaveuKeyTerms.default['到店自提']\n            }, {\n              value: \"2\",\n              name: name\n            }];\n          }\n        } else {\n          if (_this.deliveryType.length == 1 && _this.deliveryType[0] == \"1\" && _this.formValidate.type != 2) {\n            _this.deliveryList = [{\n              value: \"1\",\n              name: _leaveuKeyTerms.default['到店自提']\n            }];\n          } else {\n            _this.deliveryList = [{\n              value: \"2\",\n              name: name\n            }];\n            _this.formValidate.delivery_way = [\"2\"];\n          }\n        }\n      }).catch(function (res) {\n        _this.$message.error(res.message);\n      });\n    },\n    // 动态tab头部数据\n    getHeaderTab: function getHeaderTab() {\n      this.headTab = [{\n        title: _leaveuKeyTerms.default['商品信息'],\n        name: \"1\"\n      }, {\n        title: _leaveuKeyTerms.default['规格设置'],\n        name: \"2\"\n      }, {\n        title: _leaveuKeyTerms.default['商品详情'],\n        name: \"3\"\n      }].concat((0, _toConsumableArray2.default)(this.formValidate.type === 4 ? [{\n        title: _leaveuKeyTerms.default['预约设置'],\n        name: \"7\"\n      }] : []), [{\n        title: _leaveuKeyTerms.default['营销设置'],\n        name: \"4\"\n      }, {\n        title: _leaveuKeyTerms.default['商品参数'],\n        name: \"5\"\n      }, {\n        title: _leaveuKeyTerms.default['其他设置'],\n        name: \"6\"\n      }]);\n    },\n    // 根据商品类型判断是否显示重量体积\n    showSpecsByType: function showSpecsByType() {\n      if (this.formValidate.type == 2 || this.formValidate.type == 3) {\n        delete this.attrValue.weight;\n        delete this.attrValue.volume;\n      } else {\n        this.attrValue.weight = \"\";\n        this.attrValue.volume = \"\";\n      }\n    },\n    // 根据商品平台分类获取参数模板\n    getSpecsLst: function getSpecsLst(info, isData) {\n      var _this2 = this;\n      var cate_id = info ? info.cate_id : this.formValidate.cate_id;\n      (0, _product.specsSelectedApi)({\n        cate_id: cate_id\n      }).then(function (res) {\n        _this2.merSpecsSelect = res.data.mer || [];\n        _this2.sysSpecsSelect = res.data.sys || [];\n        if (_this2.$route.query.type == 1 && isData) {\n          _this2.infoData(info, \"taobao\");\n        }\n        if (_this2.$route.query.type != 1 && isData) {\n          _this2.infoData(info);\n        }\n      }).catch(function (res) {\n        _this2.$message.error(res.message);\n      });\n    },\n    // 获取参数模板详情数据\n    getSpecsList: function getSpecsList() {\n      var _this3 = this;\n      var merParams = (0, _toConsumableArray2.default)(this.formValidate.custom_temp_id),\n        sysParams = [this.formValidate.param_temp_id].concat();\n      var params = [].concat((0, _toConsumableArray2.default)(merParams), (0, _toConsumableArray2.default)(sysParams));\n      if (params.length <= 0) {\n        this.formValidate.merParams = [];\n        this.formValidate.sysParams = [];\n      } else {\n        (0, _product.productSpecsDetailApi)({\n          template_ids: params.toString()\n        }).then(function (res) {\n          var arr = [];\n          _this3.formValidate.params.forEach(function (item, i) {\n            if (!item.parameter_id) arr.push(item);\n          });\n          _this3.formValidate.params = [].concat(arr, (0, _toConsumableArray2.default)(res.data));\n        }).catch(function (res) {\n          _this3.$message.error(res.message);\n        });\n      }\n    },\n    // 根据不同商品类型动态生成商品规格表头\n    generateHeader: function generateHeader(data) {\n      var _this4 = this;\n      var array = [];\n      data.forEach(function (item) {\n        if (item.detail.length === 0) {\n          return _this4.$message.error(\"\\u8BF7\\u6DFB\\u52A0\".concat(item.value, \"\\u7684\\u89C4\\u683C\\u503C\"));\n        } else {\n          array.push({\n            title: item.value,\n            key: item.value,\n            minWidth: 140,\n            fixed: \"left\"\n          });\n        }\n      });\n      var specificationsColumns = array;\n      var arr;\n      if (this.formValidate.type == 2) {\n        arr = [].concat(specificationsColumns, (0, _toConsumableArray2.default)(_TableHeadList.VirtualTableHead));\n        // 找到slot 等于 fictitious 将title改为规格名称\n        this.formValidate.header.map(function (item) {\n          if (item.slot === \"fictitious\") {\n            item.title = _leaveuKeyTerms.default['云盘设置'];\n          }\n        });\n      } else if (this.formValidate.type == 3) {\n        //卡密商品\n        arr = [].concat(specificationsColumns, (0, _toConsumableArray2.default)(_TableHeadList.VirtualTableHead2));\n      } else if (this.formValidate.type == 4) {\n        arr = [].concat(specificationsColumns, (0, _toConsumableArray2.default)(_TableHeadList.reservationTableHeard));\n      } else {\n        arr = [].concat(specificationsColumns, (0, _toConsumableArray2.default)(_TableHeadList.GoodsTableHead));\n      }\n      this.$set(this.formValidate, \"header\", arr);\n      this.tableKey += 1;\n      this.columnsInstalM = arr;\n    },\n    /**\r\n     * @description: 商品规格tab==2的相关方法\r\n     */\n    //  添加新规格\n    handleAddRole: function handleAddRole() {\n      var data = {\n        value: this.formDynamic.attrsName,\n        add_pic: 0,\n        detail: []\n      };\n      this.attrs.push(data);\n    },\n    // 子传父修改数据\n    setAttrs: function setAttrs(val) {\n      var _this5 = this;\n      var packageConfig = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      this.attrs = val;\n      // 设置规格属性\n      this.formValidate.attr = val;\n\n      // 检查是否是服务包数据（包含Packages规格）\n      var isServicePackage = val.some(function (attr) {\n        return attr.value === 'Packages';\n      });\n      if (isServicePackage) {\n        // 如果是服务包，确保切换到多规格模式\n        this.formValidate.spec_type = 1;\n      }\n\n      // 强制重新生成表头和数据\n      this.generateHeader(this.attrs);\n      this.generateAttr(this.attrs);\n\n      // 如果是服务包，需要设置价格\n      if (isServicePackage && packageConfig) {\n        this.setServicePackagePrices(packageConfig);\n      }\n\n      // 确保界面更新\n      this.$nextTick(function () {\n        _this5.$forceUpdate();\n      });\n    },\n    // 设置服务包价格\n    setServicePackagePrices: function setServicePackagePrices(packageConfig) {\n      console.log('设置服务包价格，配置数据：', packageConfig);\n      console.log('Basic配置：', packageConfig.basic);\n      console.log('Standard配置：', packageConfig.standard);\n      console.log('Premium配置：', packageConfig.premium);\n      console.log('当前ManyAttrValue：', this.ManyAttrValue);\n\n      // 为每个生成的规格行设置对应的价格\n      this.ManyAttrValue.forEach(function (item, index) {\n        console.log(\"\\u5904\\u7406\\u7B2C\".concat(index, \"\\u884C\\uFF1A\"), item);\n        if (index === 0) return; // 跳过第一行（批量设置行）\n\n        if (item.detail && item.detail['Packages']) {\n          var packageName = item.detail['Packages'];\n          console.log('处理套餐：', packageName);\n          console.log('套餐详情：', item.detail);\n\n          // 根据套餐名称设置价格\n          if (packageConfig.basic.enabled && packageName === packageConfig.basic.name) {\n            item.price = parseFloat(packageConfig.basic.price);\n            console.log('设置Basic价格：', packageConfig.basic.price, '转换后：', item.price);\n          } else if (packageConfig.standard.enabled && packageName === packageConfig.standard.name) {\n            item.price = parseFloat(packageConfig.standard.price);\n            console.log('设置Standard价格：', packageConfig.standard.price, '转换后：', item.price);\n          } else if (packageConfig.premium.enabled && packageName === packageConfig.premium.name) {\n            item.price = parseFloat(packageConfig.premium.price);\n            console.log('设置Premium价格：', packageConfig.premium.price, '转换后：', item.price);\n          }\n\n          // 设置默认值\n          if (!item.cost) item.cost = 0;\n          if (!item.ot_price) item.ot_price = item.price;\n          if (!item.stock) item.stock = 999;\n          console.log('最终设置的价格：', item.price);\n          console.log('完整的item：', item);\n        } else {\n          console.log('没有找到Packages规格或detail为空');\n        }\n      });\n\n      // 强制更新视图\n      this.$forceUpdate();\n    },\n    // 规格名称改变\n    attrChangeValue: function attrChangeValue(i, val) {\n      this.generateHeader(this.attrs);\n      this.generateAttr(this.attrs);\n    },\n    // 删除规格\n    handleRemoveRole: function handleRemoveRole(index) {\n      this.attrs.splice(index, 1);\n      if (!this.attrs.length) {\n        this.formValidate.header = [];\n        this.ManyAttrValue = [];\n      } else {\n        this.generateAttr(this.attrs);\n      }\n    },\n    // 删除表格中 对应属性\n    delAttrTable: function delAttrTable(val) {\n      for (var i = 0; i < this.ManyAttrValue.length; i++) {\n        var item = this.ManyAttrValue[i];\n        if (item.attr_arr && item.attr_arr.includes(val)) {\n          this.ManyAttrValue.splice(i, 1);\n          i--;\n        }\n      }\n    },\n    // 删除规格图片\n    delManyImg: function delManyImg(val, index, indexn) {\n      var newAttrs = (0, _toConsumableArray2.default)(this.attrs);\n      newAttrs[index].detail = (0, _toConsumableArray2.default)(newAttrs[index].detail);\n      newAttrs[index].detail[indexn] = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, newAttrs[index].detail[indexn]), {}, {\n        pic: \"\"\n      });\n      this.attrs = newAttrs;\n      this.ManyAttrValue.forEach(function (item) {\n        if (item.attr_arr && item.attr_arr.includes(val.value)) {\n          item.image = \"\";\n        }\n      });\n    },\n    //添加云盘链接\n    addVirtual: function addVirtual(type, index, name) {\n      this.tabIndex = index;\n      this.tabName = name;\n      if (type == 0) {\n        this.$refs.addCarMy.carMyShow = true;\n        this.virtualListClear();\n        this.$refs.addCarMy.fixedCar = {\n          is_type: 0,\n          key: \"\",\n          stock: 0\n        };\n      } else {\n        this.getSelectedLiarbry();\n        this.cdkeyLibraryInfo = {};\n        this.$refs.cdkeyLibrary.cdkeyShow = true;\n      }\n    },\n    // 查看云盘链接\n    seeVirtual: function seeVirtual(type, data, name, index) {\n      this.tabName = name;\n      this.tabIndex = index;\n      if (type == 0) {\n        this.virtualListClear();\n        this.$refs.addCarMy.fixedCar = {\n          is_type: 0,\n          key: \"\",\n          stock: 0\n        };\n        if (data.cdkey && data.cdkey.list && data.cdkey.list.length && data.cdkey.is_type == 1) {\n          this.$refs.addCarMy.fixedCar.is_type = 1;\n          this.virtualList = data.cdkey.list;\n        } else if (data.cdkey && data.cdkey.key) {\n          this.$refs.addCarMy.fixedCar.is_type = 0;\n          this.$refs.addCarMy.fixedCar.key = data.cdkey.key;\n          this.$refs.addCarMy.fixedCar.stock = data.stock;\n        }\n        this.$refs.addCarMy.carMyShow = true;\n      } else {\n        this.cdkeyLibraryInfo = {\n          id: data.library_id,\n          name: data.library_name\n        };\n        this.getSelectedLiarbry(data);\n        this.$refs.cdkeyLibrary.cdkeyShow = true;\n      }\n    },\n    //提交云盘链接\n    fixdBtn: function fixdBtn(e) {\n      if (e.is_type == 0) {\n        this.$set(this[this.tabName][this.tabIndex][\"cdkey\"], \"key\", e.key);\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", Number(e.stock));\n        this[this.tabName][this.tabIndex][\"cdkey\"].list = [];\n      } else {\n        this.$set(this[this.tabName][this.tabIndex][\"cdkey\"], \"list\", e.list);\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", e.list.length);\n        this[this.tabName][this.tabIndex][\"cdkey\"].key = \"\";\n      }\n      this.$set(this[this.tabName][this.tabIndex][\"cdkey\"], \"is_type\", e.is_type);\n      this.$refs.addCarMy.carMyShow = false;\n    },\n    // 关闭云盘弹窗\n    closeCarMy: function closeCarMy() {\n      this.$refs.addCarMy.carMyShow = false;\n    },\n    // 清除属性\n    batchDel: function batchDel() {\n      this.oneFormBatch = [{\n        image: \"\",\n        price: \"\",\n        cost: \"\",\n        ot_price: \"\",\n        stock: \"\",\n        bar_code: \"\",\n        weight: \"\",\n        volume: \"\",\n        virtual_list: []\n      }];\n    },\n    //卡密列表\n    getCdkeyLibraryList: function getCdkeyLibraryList() {\n      var _this6 = this;\n      (0, _product.productUnrelatedListApi)().then(function (res) {\n        _this6.cdkeyLibraryList = res.data.data;\n      });\n    },\n    //添加倒入卡密的值\n    changeVirtual: function changeVirtual(e) {\n      this.virtualList = this.virtualList.concat(e);\n    },\n    // 取出来已选择的卡密库\n    getSelectedLiarbry: function getSelectedLiarbry(data, array) {\n      var _this7 = this;\n      this.selectedLibrary = [];\n      array.forEach(function (item, index) {\n        if (item.library_id) _this7.selectedLibrary.push(item.library_id);\n      });\n    },\n    //选择卡密库回调\n    handlerChangeCdkeyIdSubSuccess: function handlerChangeCdkeyIdSubSuccess(row) {\n      if (!row) {\n        this.$set(this[this.tabName][this.tabIndex], \"cdkeyLibrary\", {});\n        this.$set(this[this.tabName][this.tabIndex], \"library_name\", \"\");\n        this.$set(this[this.tabName][this.tabIndex], \"library_id\", 0);\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", 0);\n      } else {\n        this.$set(this[this.tabName][this.tabIndex], \"library_id\", row.id);\n        this.$set(this[this.tabName][this.tabIndex][\"cdkeyLibrary\"], \"name\", row.name);\n        this.$set(this[this.tabName][this.tabIndex], \"stock\", row.total_num - row.used_num);\n      }\n    },\n    //提交属性值；\n    subAttrs: function subAttrs(e) {\n      var _this8 = this;\n      var selectData = [];\n      this.attrsList.forEach(function (el, index) {\n        var obj = [];\n        el.details.forEach(function (label) {\n          if (label.select) {\n            obj.push(label.name);\n          }\n        });\n        if (obj.length) {\n          selectData.push(obj);\n        }\n      });\n      var newData = [];\n      if (selectData.length) {\n        newData = this.doCombination(selectData);\n      }\n      this.attrShow = false;\n      this.activeAtter = selectData;\n      this.oneFormBatch[0].attr = newData.length ? newData.join(\";\") : \"全部\";\n      var manyAttr = this.ManyAttrValue;\n      manyAttr.forEach(function (j) {\n        _this8.$set(j, \"select\", false);\n        if (newData.length) {\n          newData.forEach(function (item) {\n            if (j.sku && j.sku.split(\"\").length == item.split(\"\").length) {\n              if (j.sku == item) {\n                _this8.$set(j, \"select\", true);\n              }\n            } else {\n              if (j.sku && j.sku == item) {\n                _this8.$set(j, \"select\", true);\n              }\n            }\n          });\n        } else {\n          _this8.$set(j, \"select\", true);\n        }\n      });\n      this.$nextTick(function () {\n        this.$set(this, \"ManyAttrValue\", manyAttr);\n      });\n    },\n    watCh: function watCh(val) {\n      var tmp = {};\n      var tmpTab = {};\n      this.formValidate.attr.forEach(function (o, i) {\n        tmp[\"value\" + i] = {\n          title: o.value\n        };\n        tmpTab[\"value\" + i] = o.detail;\n      });\n      // this.ManyAttrValue = this.attrFormat(val)\n      this.manyTabTit = tmp;\n      this.manyTabDate = tmpTab;\n      this.formThead = Object.assign({}, this.formThead, tmp);\n    },\n    //清空卡密\n    virtualListClear: function virtualListClear() {\n      this.virtualList = [{\n        is_type: 0,\n        key: \"\",\n        stock: \"\"\n      }];\n    },\n    /**\r\n     * @description: 商品详情tab==3的相关方法\r\n     */\n    // 商品详情\n    getEditorContent: function getEditorContent(data) {\n      this.formValidate.content = data;\n    },\n    /**\r\n     * @description: 营销设置tab==4的相关方法\r\n     */\n    // 选择店铺推荐商品\n    openRecommend: function openRecommend() {\n      this.recommendVisible = true;\n    },\n    /**\r\n     * @description: 其他设置tab==6的相关方法\r\n     */\n    // 运费模板\n    addTem: function addTem() {\n      this.$refs.templateForm.dialogVisible = true;\n      this.$refs.templateForm.resetData();\n    },\n    // 系统表单下拉数据\n    getFormList: function getFormList() {\n      var _this9 = this;\n      (0, _product.associatedFormList)().then(function (res) {\n        _this9.formList = res.data;\n      }).catch(function (res) {\n        _this9.$message.error(res.message);\n      });\n    },\n    // 关联的表单信息\n    getFormInfo: function getFormInfo() {\n      if (!this.formValidate.mer_form_id) {\n        return;\n      } else {\n        var time = new Date().getTime() * 1000;\n        var formUrl = \"\".concat(this.baseURL, \"/pages/admin/system_form/index?inner_frame=1&time=\").concat(time, \"&form_id=\").concat(this.formValidate.mer_form_id);\n        this.formUrl = formUrl;\n      }\n    },\n    // 添加服务保障模板\n    addServiceTem: function addServiceTem() {\n      this.$refs.serviceGuarantee.add();\n    },\n    /**\r\n     * 页面点击操作的相关方法\r\n     */\n    // 表单验证\n    validate: function validate(prop, status, error) {\n      if (status === false) {\n        this.$message.warning(error);\n      }\n    },\n    // 返回上一页\n    goBack: function goBack() {\n      sessionStorage.clear();\n      window.history.back();\n    },\n    // 点击上一步\n    handleSubmitUp: function handleSubmitUp() {\n      if (this.formValidate.type === 4) {\n        if (this.currentTab === \"7\") {\n          this.currentTab = \"3\";\n        } else if (this.currentTab === \"4\") {\n          this.currentTab = \"7\";\n        } else {\n          this.currentTab = (Number(this.currentTab) - 1).toString();\n        }\n      } else {\n        this.currentTab = (Number(this.currentTab) - 1).toString();\n      }\n    },\n    // 点击下一步\n    handleSubmitNest: function handleSubmitNest(name) {\n      var _this0 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this0.formValidate.type == 4) {\n            if (_this0.currentTab == 7) {\n              _this0.currentTab = \"4\";\n            } else if (_this0.currentTab == 3) {\n              _this0.currentTab = \"7\";\n            } else {\n              _this0.currentTab = (Number(_this0.currentTab) + 1).toString();\n            }\n          } else {\n            _this0.currentTab = (Number(_this0.currentTab) + 1).toString();\n          }\n        }\n      });\n    },\n    switchTimed: function switchTimed(val) {\n      this.is_timed = val;\n    },\n    // 点击提交按钮\n    handleSubmit: function handleSubmit(name) {\n      var _this1 = this;\n      // 检查 reservationSetting 引用是否存在，避免出现未定义错误\n      this.$store.dispatch(\"settings/setEdit\", false);\n      var ids = [];\n      this.goodList.forEach(function (item, index) {\n        ids.push(item.product_id);\n      });\n      this.formValidate.good_ids = ids;\n      this.formValidate.auto_off_time = this.is_timed ? this.formValidate.auto_off_time : \"\";\n      // 遍历验证规则数组进行验证\n      for (var _i = 0, _validationRules = validationRules; _i < _validationRules.length; _i++) {\n        var rule = _validationRules[_i];\n        var field = rule.field,\n          message = rule.message,\n          validator = rule.validator;\n        if (validator(this.formValidate[field])) {\n          return this.$message.warning(message);\n        }\n      }\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this1.formValidate.spec_type == 1) {\n            // 多规格模式（包括服务包转换后的多规格）\n            if (_this1.ManyAttrValue.length < 2) return _this1.$message.warning(\"商品规格-规格数量最少1个\");\n            // 删除第一项\n            var newData = JSON.parse(JSON.stringify(_this1.ManyAttrValue));\n            newData.shift();\n            _this1.formValidate.attrValue = newData;\n          } else {\n            _this1.formValidate.attrValue = _this1.OneattrValue;\n            _this1.formValidate.attr = [];\n          }\n\n          // 预约商品的数据和验证逻辑\n          if (_this1.formValidate.type == 4) {\n            if (_this1.formValidate.reservation_time_type == 1 && !_this1.formValidate.reservation_start_time) {\n              return _this1.$message.warning(\"请选择预约时间段\");\n            }\n            if (!_this1.formValidate.time_period && _this1.formValidate.time_period.length) {\n              return _this1.$message.warning(\"请选择预约时间段\");\n            }\n            // 移除time_period中的stock字段\n            _this1.formValidate.time_period = _this1.formValidate.time_period.map(function (_ref) {\n              var stock = _ref.stock,\n                rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n              return rest;\n            });\n            // 检查每个属性的预约设置\n            var hasEmptyReservation = _this1.formValidate.attrValue.some(function (attr) {\n              if (!attr && !attr.reservation && attr.reservation.length == 0) {\n                _this1.$message.warning(\"请设置预约数量\");\n                return true;\n              }\n\n              // 移除reservation中的is_show字段\n              attr.reservation = attr.reservation.map(function (_ref2) {\n                var is_show = _ref2.is_show,\n                  start = _ref2.start,\n                  end = _ref2.end,\n                  stock = _ref2.stock,\n                  end_time = _ref2.end_time,\n                  start_time = _ref2.start_time,\n                  rest = (0, _objectWithoutProperties2.default)(_ref2, _excluded2);\n                return {\n                  start_time: start || start_time,\n                  end_time: end || end_time,\n                  stock: stock\n                };\n              });\n              return false;\n            });\n            if (hasEmptyReservation) return;\n          }\n          _this1.fullscreenLoading = true;\n          _this1.loading = true;\n          var disCreate = _this1.$route.query.id && !_this1.$route.query.type;\n          disCreate ? _this1.productUpdate() : _this1.productCreate();\n        }\n      });\n    },\n    // 点击预览按钮\n    handlePreview: function handlePreview(name) {\n      var _this10 = this;\n      if (this.formValidate.spec_type === 1) {\n        var newData = JSON.parse(JSON.stringify(this.ManyAttrValue));\n        newData.shift();\n        this.formValidate.attrValue = newData;\n      } else {\n        this.formValidate.attrValue = this.OneattrValue;\n        this.formValidate.attr = [];\n      }\n      (0, _product.productPreviewApi)(this.formValidate).then(/*#__PURE__*/function () {\n        var _ref3 = (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee(res) {\n          return (0, _regenerator2.default)().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                _this10.previewVisible = true;\n                _this10.previewKey = res.data.preview_key;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref3.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this10.$message.error(res.message);\n      });\n    },\n    // 添加商品\n    productCreate: function productCreate() {\n      var _this11 = this;\n      var api = this.formValidate.type === 4 ? _product.productReservationCreateApi : _product.productCreateApi;\n      api(this.formValidate).then(/*#__PURE__*/function () {\n        var _ref4 = (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee2(res) {\n          return (0, _regenerator2.default)().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                _this11.fullscreenLoading = false;\n                _this11.$message.success(res.message);\n                _this11.$router.push({\n                  path: _this11.roterPre + \"/product/list\"\n                });\n                _this11.loading = false;\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref4.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this11.fullscreenLoading = false;\n        _this11.loading = false;\n        _this11.ManyAttrValue = [].concat((0, _toConsumableArray2.default)(_this11.oneFormBatch), (0, _toConsumableArray2.default)(_this11.formValidate.attrValue));\n        _this11.$message.error(res.message);\n      });\n    },\n    // 编辑商品\n    productUpdate: function productUpdate() {\n      var _this12 = this;\n      var api = this.formValidate.type === 4 ? _product.productReservationEditApi : _product.productUpdateApi;\n      api(this.$route.query.id, this.formValidate).then(/*#__PURE__*/function () {\n        var _ref5 = (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee3(res) {\n          return (0, _regenerator2.default)().w(function (_context3) {\n            while (1) switch (_context3.n) {\n              case 0:\n                _this12.fullscreenLoading = false;\n                _this12.$message.success(res.message);\n                _this12.$router.push({\n                  path: _this12.roterPre + \"/product/list\"\n                });\n                _this12.formValidate.slider_image = [];\n                _this12.loading = false;\n              case 1:\n                return _context3.a(2);\n            }\n          }, _callee3);\n        }));\n        return function (_x3) {\n          return _ref5.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this12.fullscreenLoading = false;\n        _this12.loading = false;\n        _this12.ManyAttrValue = [].concat((0, _toConsumableArray2.default)(_this12.oneFormBatch), (0, _toConsumableArray2.default)(_this12.formValidate.attrValue));\n        _this12.$message.error(res.message);\n      });\n    },\n    // 商品编辑-获取商品的信息\n    initData: function initData() {\n      this.getShippingList();\n      this.getGuaranteeList();\n      this.productGetRule();\n      this.getFormList();\n      this.$store.dispatch(\"settings/setEdit\", true);\n      this.formValidate.slider_image = [];\n      if (this.$route.query.id || this.$route.query.type == \"copy\") {\n        this.product_id = this.$route.query.id;\n        this.setTagsViewTitle();\n        this.getInfo();\n      } else {\n        this.productCon();\n        if (this.deduction_set == -1) this.formValidate.integral_rate = -1;\n      }\n      if (this.$route.query.type == 1) {\n        this.type = this.$route.query.type;\n        this.$refs.taoBao.modals = true;\n      } else {\n        this.type = 0;\n      }\n    },\n    setTagsViewTitle: function setTagsViewTitle() {\n      var title = _leaveuKeyTerms.default['编辑商品'];\n      var route = Object.assign({}, this.tempRoute, {\n        title: \"\".concat(title, \"-\").concat(this.$route.query.id)\n      });\n      this.$store.dispatch(\"tagsView/updateVisitedView\", route);\n    },\n    // 获取服务保障模板\n    getGuaranteeList: function getGuaranteeList() {\n      var _this13 = this;\n      (0, _product.guaranteeListApi)().then(function (res) {\n        _this13.guaranteeList = res.data;\n      });\n    },\n    // 获取商品属性模板；\n    productGetRule: function productGetRule() {\n      var _this14 = this;\n      (0, _product.templateLsitApi)().then(function (res) {\n        _this14.ruleList = res.data;\n      });\n    },\n    // 运费模板；\n    getShippingList: function getShippingList() {\n      var _this15 = this;\n      (0, _product.shippingListApi)().then(function (res) {\n        _this15.shippingList = res.data;\n      });\n    },\n    doCombination: function doCombination(arr) {\n      var count = arr.length - 1; //数组长度(从0开始)\n      var tmp = [];\n      var totalArr = []; // 总数组\n      return doCombinationCallback(arr, 0); //从第一个开始\n      //js 没有静态数据，为了避免和外部数据混淆，需要使用闭包的形式\n      function doCombinationCallback(arr, curr_index) {\n        var _iterator = (0, _createForOfIteratorHelper2.default)(arr[curr_index]),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var val = _step.value;\n            tmp[curr_index] = val; //以curr_index为索引，加入数组\n            //当前循环下标小于数组总长度，则需要继续调用方法\n            if (curr_index < count) {\n              doCombinationCallback(arr, curr_index + 1); //继续调用\n            } else {\n              totalArr.push(tmp.join(\",\")); //(直接给push进去，push进去的不是值，而是值的地址)\n            }\n            //js  对象都是 地址引用(引用关系)，每次都需要重新初始化，否则 totalArr的数据都会是最后一次的 tmp 数据；\n            var oldTmp = tmp;\n            tmp = [];\n            for (var _i2 = 0, _oldTmp = oldTmp; _i2 < _oldTmp.length; _i2++) {\n              var index = _oldTmp[_i2];\n              tmp.push(index);\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n        return totalArr;\n      }\n    },\n    getRecommend: function getRecommend(selected) {\n      this.goodList = selected && selected.length <= 18 ? selected : selected.slice(0, 18);\n      this.recommendVisible = false;\n    },\n    closeRecommend: function closeRecommend() {\n      this.recommendVisible = false;\n    },\n    // 删除店铺推荐商品\n    deleteRecommend: function deleteRecommend(index) {\n      this.goodList.splice(index, 1);\n    },\n    delSpecs: function delSpecs(index) {\n      this.formValidate.params.splice(index, 1);\n    },\n    attrFormat: function attrFormat(arr) {\n      var data = [],\n        that = this;\n      var res = [];\n      return format(arr);\n      function format(arr) {\n        if (arr.length > 1) {\n          arr.forEach(function (v, i) {\n            if (i === 0) data = arr[i][\"detail\"];\n            var tmp = [];\n            data.forEach(function (vv) {\n              arr[i + 1] && arr[i + 1][\"detail\"] && arr[i + 1][\"detail\"].forEach(function (g) {\n                var rep2 = (i !== 0 ? \"\" : arr[i][\"value\"] + \"_$_\") + vv + \"-$-\" + arr[i + 1][\"value\"] + \"_$_\" + g;\n                tmp.push(rep2);\n                if (i === arr.length - 2) {\n                  var rep4 = {\n                    image: \"\",\n                    price: 0,\n                    cost: 0,\n                    ot_price: 0,\n                    select: true,\n                    sku: \"\",\n                    stock: 0,\n                    cdkey: {},\n                    cdkeyLibrary: {},\n                    library_name: \"\",\n                    library_id: \"\",\n                    bar_code: \"\",\n                    weight: 0,\n                    volume: 0,\n                    extension_one: 0,\n                    extension_two: 0\n                  };\n                  rep2.split(\"-$-\").forEach(function (h, k) {\n                    var rep3 = h.split(\"_$_\");\n                    if (!rep4[\"detail\"]) rep4[\"detail\"] = {};\n                    rep4[\"detail\"][rep3[0]] = rep3.length > 1 ? rep3[1] : \"\";\n                  });\n                  // if(rep4.detail !== 'undefined' && rep4.detail !== null){\n                  Object.values(rep4.detail).forEach(function (v, i) {\n                    rep4[\"value\" + i] = v;\n                  });\n                  // }\n                  res.push(rep4);\n                }\n              });\n            });\n            data = tmp.length ? tmp : [];\n          });\n        } else {\n          var dataArr = [];\n          arr.forEach(function (v, k) {\n            v[\"detail\"].forEach(function (vv, kk) {\n              dataArr[kk] = v[\"value\"] + \"_\" + vv;\n              res[kk] = {\n                image: \"\",\n                price: 0,\n                cost: 0,\n                ot_price: 0,\n                select: true,\n                sku: \"\",\n                stock: 0,\n                cdkey: {},\n                cdkeyLibrary: {},\n                library_name: \"\",\n                library_id: \"\",\n                bar_code: \"\",\n                weight: 0,\n                volume: 0,\n                extension_one: 0,\n                extension_two: 0,\n                detail: (0, _defineProperty2.default)({}, v[\"value\"], vv)\n              };\n              Object.values(res[kk].detail).forEach(function (v, i) {\n                res[kk][\"value\" + i] = v;\n              });\n            });\n          });\n          data.push(dataArr.join(\"__SCRIPT_BLOCK_0__\"));\n        }\n        if (that.generateArr.length > 0) {\n          that.generateArr.forEach(function (v, i) {\n            res[i][\"image\"] = v.image || v.pic;\n            res[i][\"price\"] = v.price;\n            res[i][\"cost\"] = v.cost;\n            res[i][\"ot_price\"] = v.ot_price;\n            res[i][\"sku\"] = v.sku;\n            res[i][\"stock\"] = v.stock;\n            res[i][\"unique\"] = v.unique;\n            res[i][\"bar_code\"] = v.bar_code;\n            res[i][\"volume\"] = v.volume;\n            res[i][\"weight\"] = v.weight;\n            res[i][\"extension_one\"] = v.extension_one;\n            res[i][\"extension_two\"] = v.extension_two;\n            res[i][\"cdkey\"] = v.cdkey && v.cdkey.length && v.cdkey[0] || null;\n            res[i][\"cdkeyLibrary\"] = v.cdkeyLibrary || {};\n            res[i][\"library_name\"] = v.cdkeyLibrary && v.cdkeyLibrary.name;\n            res[i][\"library_id\"] = v.library_id || \"\";\n            res[i][\"svip_price\"] = v.svip_price || \"\";\n          });\n        }\n        return res;\n      }\n    },\n    handleFocus: function handleFocus(val) {\n      this.changeAttrValue = val;\n    },\n    handleBlur: function handleBlur() {\n      this.changeAttrValue = \"\";\n    },\n    //选中属性\n    activeAttr: function activeAttr(e) {\n      this.attrsList = e;\n    },\n    //关闭属性弹窗\n    labelAttr: function labelAttr() {\n      this.attrShow = false;\n    },\n    // 立即生成\n    generateAttr: function generateAttr(data, val) {\n      var _this16 = this;\n      // 判断该段Js执行时间\n      this.generateHeader(data);\n      var combinations = this.generateCombinations(data);\n      var rows = combinations.map(function (combination) {\n        var row = {\n          attr_arr: combination,\n          detail: {},\n          cdkey: {},\n          title: \"\",\n          key: \"\",\n          price: 0,\n          image: \"\",\n          ot_price: 0,\n          cost: 0,\n          stock: 0,\n          is_show: 1,\n          is_default_select: 0,\n          unique: \"\",\n          weight: \"\",\n          extension_one: 0,\n          extension_two: 0,\n          svip_price: 0\n        };\n\n        // 特殊处理服务包数据\n        if (_this16.isServicePackageData(data)) {\n          _this16.processServicePackageRow(row, combination, data);\n        }\n        // 判断商品类型是卡密/优惠券\n        var virtualType = _this16.formValidate.type;\n        if (virtualType == 3) {\n          //卡密\n          _this16.$set(row, \"virtual_list\", []);\n        } else if (virtualType == 2) {\n          //云盘\n          _this16.$set(row, \"cdkey\", {});\n          _this16.$set(row, \"coupon_name\", \"\");\n        } else if (virtualType == 4) {}\n        for (var i = 0; i < combination.length; i++) {\n          var value = combination[i];\n          _this16.$set(row, data[i].value, value);\n          _this16.$set(row, \"title\", data[i].value);\n          _this16.$set(row, \"key\", data[i].value);\n          _this16.$set(row.detail, data[i].value, value);\n          // 如果manyFormValidate中存在该属性值，则赋值\n          for (var k = 0; k < _this16.ManyAttrValue.length; k++) {\n            var manyItem = _this16.ManyAttrValue[k];\n            // 对比两个数组是否完全相等\n            if (k > 0 && manyItem.attr_arr && (0, _utils.arraysEqual)(manyItem.attr_arr, combination)) {\n              Object.assign(row, {\n                price: manyItem.price,\n                cost: manyItem.cost,\n                ot_price: manyItem.ot_price,\n                stock: manyItem.stock,\n                reservation: manyItem.reservation || [],\n                image: manyItem.image,\n                unique: manyItem.unique || \"\",\n                weight: manyItem.weight || \"\",\n                is_show: manyItem.is_show || 1,\n                is_default_select: manyItem.is_default_select || 0,\n                volume: manyItem.volume || 0,\n                is_virtual: manyItem.is_virtual,\n                extension_one: manyItem.extension_one,\n                extension_two: manyItem.extension_two,\n                svip_price: manyItem.svip_price\n              });\n              if (virtualType == 1) {\n                row.virtual_list = manyItem.virtual_list;\n              }\n            } else if (k > 0 && manyItem.attr_arr.length && data[i].add_pic && combination.includes(val)) {\n              // data[i].detail中的value是规格值 存在与 manyItem.attr_arr 中的某一项\n              data[i].detail.map(function (e, ii) {\n                combination.includes(e.value) && _this16.$set(row, \"image\", e.image);\n              });\n            }\n          }\n        }\n        return row;\n      });\n      this.$nextTick(function () {\n        // rows数组第一项 新增默认数据 oneFormBatch\n        _this16.ManyAttrValue = [].concat((0, _toConsumableArray2.default)(_this16.oneFormBatch), (0, _toConsumableArray2.default)(rows));\n      });\n    },\n    // 规格值改变\n    attrDetailChangeValue: function attrDetailChangeValue(val, i) {\n      var _this17 = this;\n      if (this.ManyAttrValue.length) {\n        var key = this.attrs[i].value;\n        this.ManyAttrValue.map(function (item, i) {\n          if (i > 0) {\n            if (Object.keys(item.detail).includes(key) && item.detail[key] === _this17.changeAttrValue) {\n              item.detail[key] = val;\n              var index = item.attr_arr.findIndex(function (item) {\n                return item === _this17.changeAttrValue;\n              });\n              item.attr_arr[index] = val;\n            }\n          }\n        });\n        this.changeAttrValue = val;\n      } else {\n        this.generateAttr(this.attrs, 1);\n      }\n    },\n    // 生成规格组合\n    generateCombinations: function generateCombinations(arr) {\n      var _this18 = this;\n      var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n      if (arr.length === 0) {\n        return [prefix];\n      }\n      var _arr = (0, _toArray2.default)(arr),\n        first = _arr[0],\n        rest = _arr.slice(1);\n      return first.detail.flatMap(function (detail) {\n        return _this18.generateCombinations(rest, [].concat((0, _toConsumableArray2.default)(prefix), [detail.value]));\n      });\n    },\n    // 判断是否为服务包数据\n    isServicePackageData: function isServicePackageData(data) {\n      return data.some(function (attr) {\n        return attr.value === 'Packages';\n      });\n    },\n    // 处理服务包行数据\n    processServicePackageRow: function processServicePackageRow(row, combination, data) {\n      // 查找Packages规格\n      var packagesAttr = data.find(function (attr) {\n        return attr.value === 'Packages';\n      });\n      if (packagesAttr) {\n        var packageName = combination[data.indexOf(packagesAttr)];\n        var packageDetail = packagesAttr.detail.find(function (detail) {\n          return detail.value === packageName;\n        });\n        if (packageDetail && packageDetail.data) {\n          // 设置服务包的价格和其他信息\n          row.price = packageDetail.data.price || 0;\n          row.ot_price = packageDetail.data.price || 0;\n          row.stock = 999; // 服务包默认库存\n\n          // 将服务包信息存储到detail中\n          row.detail.package_info = packageDetail.data;\n        }\n      }\n\n      // 处理额外服务的价格\n      var _loop = function _loop(i) {\n        var attrName = data[i].value;\n        if (attrName.startsWith('extra services')) {\n          var extraDetail = data[i].detail.find(function (detail) {\n            return detail.value === combination[i];\n          });\n          if (extraDetail && extraDetail.price) {\n            row.price += extraDetail.price;\n            row.ot_price += extraDetail.price;\n          }\n        }\n      };\n      for (var i = 0; i < combination.length; i++) {\n        _loop(i);\n      }\n    },\n    // 获取接口详情\n    getInfo: function getInfo() {\n      var _this19 = this;\n      this.fullscreenLoading = true;\n      var parmas = {};\n      if (this.$route.query.type == \"copy\") parmas.is_copy = 1;\n      var api = this.$route.query.productType == 4 ? _product.productReservationInfoApi : _product.productLstDetail;\n      api(this.$route.query.id, parmas).then(/*#__PURE__*/function () {\n        var _ref6 = (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee4(res) {\n          var info, time, formUrl;\n          return (0, _regenerator2.default)().w(function (_context4) {\n            while (1) switch (_context4.n) {\n              case 0:\n                _this19.generateArr = [];\n                info = res.data;\n                _this19.getSpecsLst(info, true);\n                _this19.productCon();\n                if (info.type == 3) _this19.getCdkeyLibraryList();\n                if (info.mer_form_id) {\n                  time = new Date().getTime() * 1000;\n                  formUrl = \"\".concat(_this19.baseURL, \"/pages/admin/system_form/index?inner_frame=1&time=\").concat(time, \"&form_id=\").concat(info.mer_form_id);\n                  _this19.formUrl = formUrl;\n                }\n              case 1:\n                return _context4.a(2);\n            }\n          }, _callee4);\n        }));\n        return function (_x4) {\n          return _ref6.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this19.fullscreenLoading = false;\n        _this19.$message.error(res.message);\n      });\n    },\n    // 给商品表单赋值\n    infoData: function infoData(data, type) {\n      var _this20 = this;\n      if (type == \"taobao\") {\n        this.formValidate.type = Number(this.$route.query.productType);\n        if (this.formValidate.type == 4) {\n          if (data.attr.length > 0) {\n            data.attr = [data.attr[0]];\n          }\n        }\n      }\n      this.deduction_set = data.integral_rate == -1 ? -1 : 1;\n      this.goodList = data.goodList || [];\n      this.attrs = data.attr || [];\n      data.attrValue.forEach(function (val) {\n        _this20.$set(val, \"select\", true);\n        if (data.type == 3) {\n          _this20.$set(val, \"library_id\", val.library_id == 0 ? \"\" : val.library_id);\n        }\n      });\n      this.formValidate = data;\n      if (data.type != 4) {\n        this.formValidate.delivery_way = data.delivery_way && data.delivery_way.length ? data.delivery_way.map(String) : this.deliveryType;\n      }\n      this.formValidate.temp_id = (0, _utils.mateName)(this.shippingList, \"shipping_template_id\", data.temp_id);\n      this.formValidate.mer_form_id = (0, _utils.mateName)(this.formList, \"form_id\", data.mer_form_id);\n      this.is_timed = data.auto_off_time ? 1 : 0;\n      this.formValidate.spec_type = Number(data.spec_type);\n      this.formValidate.params = data.params || [];\n      this.formValidate.couponData = data.coupon || [];\n      this.formValidate.mer_labels = data.mer_labels && data.mer_labels.length ? data.mer_labels.map(Number) : [];\n      this.formValidate.guarantee_template_id = data.guarantee_template_id ? data.guarantee_template_id : \"\";\n      if (type == \"taobao\") {\n        var obj = {\n          reservation_time_type: 1,\n          reservation_type: 3,\n          reservation_start_time: \"\",\n          reservation_end_time: \"\",\n          show_num_type: 1,\n          sale_time_type: 1,\n          sale_time_start_day: \"\",\n          sale_time_end_day: \"\",\n          sale_time_week: [1, 2, 3, 4, 5, 6, 7],\n          show_reservation_days: 10,\n          is_advance: 0,\n          advance_time: 0,\n          is_cancel_reservation: 0,\n          cancel_reservation_time: 0,\n          reservation_form_type: 1\n        };\n        for (var key in obj) {\n          this.formValidate[key] = obj[key];\n        }\n      }\n      if (this.formValidate.type == 4) {\n        this.formValidate.reservation_time_type = Number(data.reservation_time_type);\n        this.formValidate.show_num_type = Number(data.show_num_type);\n        if (data.attrValue.length > 0) {\n          data.attrValue.forEach(function (item) {\n            if (item.reservation && item.reservation.length > 0) {\n              item.reservation.forEach(function (el) {\n                el.start = el.start_time;\n                el.end = el.end_time;\n              });\n            }\n          });\n        }\n      }\n      this.formValidate.type = Number(this.$route.query.productType);\n      if (data.spec_type == 0) {\n        this.ManyAttrValue = [];\n        data.attrValue[0].list = [], this.OneattrValue = data.attrValue;\n      } else {\n        if (this.formValidate.extend.length != 0) {\n          this.customBtn = 1;\n        }\n        this.generateHeader(this.attrs);\n        this.ManyAttrValue = [].concat((0, _toConsumableArray2.default)(this.oneFormBatch), (0, _toConsumableArray2.default)(data.attrValue));\n        if (type == \"taobao\" && this.formValidate.type == 4) {\n          this.ManyAttrValue = [];\n          this.generateAttr(this.attrs);\n        }\n      }\n      if (this.formValidate.custom_temp_id.length > 0 || this.formValidate.param_temp_id) {\n        this.getSpecsList();\n      }\n      this.fullscreenLoading = false;\n      setTimeout(function (e) {\n        _this20.checkAllGroup(data.extension_type);\n      }, 1000);\n    },\n    checkAllGroup: function checkAllGroup(data) {\n      var endLength = this.attrs.length + 3;\n      if (this.formValidate.spec_type === 0) {\n        if (data == 0) {\n          this.columnsInstall = this.columns2.slice(0, endLength).concat(this.member);\n        } else if (data == 1) {\n          this.columnsInstall = this.columns2.slice(0, endLength).concat(this.rakeBack);\n        } else {\n          this.columnsInstall = this.columns2.slice(0, endLength);\n        }\n      } else {\n        if (data == 0) {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, endLength).concat(this.member);\n        } else if (data == 1) {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, endLength).concat(this.rakeBack);\n        } else {\n          this.columnsInstal2 = this.columnsInstalM.slice(0, endLength);\n        }\n      }\n    }\n  }\n};", null]}