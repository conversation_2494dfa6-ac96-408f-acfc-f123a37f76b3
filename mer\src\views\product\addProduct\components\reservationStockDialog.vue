<template>
  <div>
    <el-dialog
      :title="$t('可约数量设置')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <el-table
        :data="gridData"
        :key="itemKey"
        max-height="500"
        style="width: 100%"
        :class="!isShow ? 'hide-first-row' : ''"
      >
        <el-table-column property="image" :label="$t('图片')" width="150">
          <template slot-scope="scope">
            <div class="upLoadPicBox specPictrue">
              <div
                v-if="scope.row.image || scope.row.pic"
                class="pictrue tabPic"
              >
                <img :src="scope.row.image || scope.row.pic" />
              </div>
              <div v-else>--</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          property="price"
          :label="$t('产品规格')"
          width="200"
          v-if="type != '0'"
        >
          <template slot-scope="scope">
            <div class="time-height">
              {{ scope.row.attr_arr ? scope.row.attr_arr[0] : scope.row.sku }}
            </div>
          </template>
        </el-table-column>

        <el-table-column property="address" :label="$t('时间段')">
          <template slot-scope="scope">
            <div
              v-for="(item, index) in scope.row.reservation"
              :key="index"
              class="time-height"
            >
              {{ item.start || item.start_time }}-{{
                item.end || item.end_time
              }}
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="$t('预约数量')">
          <template slot="header" slot-scope="scope">
            <span>{{ $t('预约数量') }}</span>
            <el-popover placement="top" trigger="manual" v-model="visible">
              <div class="title">{{ $t('批量修改') }}</div>
              <div class="flex">
                <el-input-number
                  v-model="batchStockValue"
                  :min="0"
                  :max="99999"
                  :controls="false"
                  :precision="0"
                  :label="$t('请输入预约数量')"
                  style="width: 120px;"
                  size="small"
                  class="mr10"
                ></el-input-number>
                <el-button size="small" @click="close">{{ $t('取消') }}</el-button>
                <el-button type="primary" size="small" @click="batchUpdateStock"
                  >{{ $t('确定') }}</el-button
                >
              </div>
              <span
                slot="reference"
                class="el-icon-edit-outline"
                @click="visible = true"
              ></span>
            </el-popover>
          </template>

          <template slot-scope="scope">
            <div
              v-for="(item, index) in scope.row.reservation"
              :key="index"
              class="mb10"
            >
              <el-input-number
                v-model="item.stock"
                :min="0"
                :max="99999"
                :precision="0"
                :controls="false"
                :label="$t('请输入预约数量')"
                style="width: 120px;"
                size="small"
              ></el-input-number>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">{{ $t('取 消') }}</el-button>
        <el-button type="primary" @click="submitOk" size="small"
          >{{ $t('确 定') }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "reservationStockDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: true
    },
    gridData: {
      type: Array,
      default: () => []
    },
    type: {
      // 0:单规格  1:多规格
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      visible: false,
      batchStockValue: 0,
      itemKey: 0
    };
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        this.itemKey = Math.random();
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit("close");
      this.visible = false;
    },
    close() {
      this.visible = false;
      this.batchStockValue = 0;
    },

    batchUpdateStock() {
      // 检查批量修改的值是否为有效的数字
      const stockValue = Number(this.batchStockValue);

      if (isNaN(stockValue)) {
        console.warn("批量修改的预约数量不是有效的数字");
        return;
      }

      // 使用 for...of 循环替代 forEach，便于后续可能的提前终止操作
      for (const item of this.gridData) {
        for (const res of item.reservation) {
          res.stock = stockValue;
        }
      }
      this.itemKey = Math.random();
      // 关闭批量修改弹窗并重置值
      this.close();
    },

    submitOk() {
      this.$emit("submitOk", this.gridData);
      this.visible = false;
    },

    handleStockInput(value, item) {
      let num = parseInt(value);
      if (isNaN(num) || num < 0) {
        item.stock = 0;
      } else if (num > 99999) {
        item.stock = 99999;
      } else {
        item.stock = num;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.upLoadPicBox {
  position: relative;

  &.specPictrue {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upLoad {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -o-box-orient: vertical;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    line-height: 20px;
  }

  span {
    font-size: 10px;
  }
}

.time-height {
  height: 32px;
  line-height: 32px;
  font-size: 13px;
  margin-bottom: 10px;
}

.title {
  font-size: 13px;
  margin-bottom: 10px;
}
/* 隐藏表格的第一行 */
.hide-first-row {
  /deep/ .el-table__body tr:nth-child(1) {
    display: none;
  }
}
</style>
