<template>
  <div class="divBox">
    <div class="selCard">
      <el-form :model="tableFrom" ref="searchForm" size="small" label-width="85px" inline>                   
        <el-form-item :label="$t('时间选择：')">
          <el-date-picker v-model="timeVal" value-format="yyyy/MM/dd" format="yyyy/MM/dd" type="daterange" placement="bottom-end" :placeholder="$t('自定义时间')" style="width: 280px;" :picker-options="pickerOptions" @change="onchangeTime" />
        </el-form-item>
        <el-form-item :label="$t('消息名称：')" prop="keyword">
          <el-input v-model="tableFrom.keyword" @keyup.enter.native="getLists(1)" :placeholder="$t('请输入消息名称')" clearable class="selWidth" />                      
        </el-form-item> 
        <el-form-item>
          <el-button type="primary" size="small" @click="getList(1)">{{ $t('搜索') }}</el-button>
          <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
        </el-form-item>               
      </el-form>
    </div>
    <el-card class="mt14">
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
        @expand-change="handleRead"
        :row-key="getRowKeys"
        :expand-row-keys="expands"
      >
        <el-table-column :label="$t('序号')" min-width="50">
          <template scope="scope">
            <span>{{ scope.$index+(tableFrom.page - 1) * tableFrom.limit + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('消息名称')"
          min-width="150"
        >
          <template scope="scope">
            <span class="circle" :class="scope.row.is_read === 0 ? 'red' : 'gray'"></span>
            <span :class="scope.row.is_read === 0 ? 'unread' : 'read'">{{ scope.row.notice_title }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('日期')"
          min-width="180"
        >
          <template scope="scope">
            <span :class="scope.row.is_read === 0 ? 'unread' : 'read'">{{ scope.row.create_time }}</span>
          </template>
        </el-table-column>
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-form-item>
                <span style="color: #333;">{{ (props.row.notice_content) ? props.row.notice_content : ''}}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { stationNewsList, stationNoticeRead } from '@/api/system'
import { roterPre } from '@/settings'
import timeOptions from '@/utils/timeOptions';
export default {
  name: 'SystemLog',
  data() {
    return {
      pickerOptions: timeOptions,
      props: {
        emitPath: false
      },
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      dialogVisible: false,
      categoryList: [],
      merCateList: [],
      merSelect: [],
      fullscreenLoading: false,
      roterPre: roterPre,
      timeVal: [],    
      tableFrom: {
        page: 1,
        limit: 20,
        date: "",
        keyword: '',  
      },
      // 要展开的行，数值的元素是row的key值
      expands: [],
      idx: 0
    }
  },
  mounted() {
    this.getList('');
  },
  methods: {
    /**重置 */
    searchReset(){
      this.timeVal = []
      this.tableFrom.date = ""
      this.$refs.searchForm.resetFields()
      this.getList(1)
    },
    // 获取row的key值
    getRowKeys(row) {
      return row.notice_log_id;
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.tableFrom.date = e ? this.timeVal.join("-") : "";
      this.getLists(1);
    },
    // 列表
    getList(num) {
      let that = this
      that.listLoading = true
      that.tableFrom.page = num ? num : that.tableFrom.page;
      stationNewsList(that.tableFrom).then(res => {
        that.tableData.data = res.data.list
        that.tableData.total = res.data.count
        that.listLoading = false
        if(that.$route.params.id){
          that.handleRead({notice_log_id: that.$route.params.id,is_read: 0})
          that.tableData.data.forEach((item,index)  => {
            if (item.notice_log_id == that.$route.params.id) {
              that.idx = index
              that.expands.push(that.tableData.data[that.idx].notice_log_id);
            }
          })
        }
      }).catch(res => {
        this.listLoading = false
        this.$message.error(res.message)
      })
    },
     // 列表
    getLists(num) {
      let that = this
      that.listLoading = true
      that.tableFrom.page = num ? num : that.tableFrom.page;
      stationNewsList(that.tableFrom).then(res => {
        that.tableData.data = res.data.list
        that.tableData.total = res.data.count
        that.listLoading = false
      }).catch(res => {
        this.listLoading = false
        this.$message.error(res.message)
      })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getLists('')
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getLists(1)

    },
    handleRead(data){
      if(data.is_read === 0){
        stationNoticeRead(data.notice_log_id).then(res => {
          this.listLoading = false
          data.is_read = 1
          if(this.$route.params.id && this.tableFrom.page === 1){
            this.tableData.data[this.idx].is_read = 1
          }
          // this.$refs.headerNotice.getList("");
        }).catch(res => {
          this.listLoading = false
          this.$message.error(res.message)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-table__expanded-cell,::v-deep .el-table__expanded-cell:hover{
  background-color: #f4f5f9!important;
}
.read{
  color: #999;
}
.circle{
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 100%;
  box-shadow: 0 0 0 1px #fff;
}
.red{
  background: #ed4014;
}
.gray{
  background: #c5c8ce;
}
::v-deep .el-table th:nth-child(2){
  padding-left: 15px;
}
</style>
