<template>
    <div class="user-index">
      <div class="user-com-title" style="padding-left: 16px">账户管理</div>
      <div class="user-content">
        <div class="item user-info">
          <div class="title">我的信息</div>
          <div class="info">
            <span class="label">我的头像：</span>
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="avatar">
            <img v-else src="~assets/images/f.png" />
          </div>
        </div>
        <div class="item text-info">
          <span class="label">我的昵称：</span>
          <span class="txt line1" style="display: inline-block;vertical-align: middle">{{userInfo.nickname}}</span>
        </div>
       
        <div class="item text-info">
          <span class="label">我的ID：</span>
          <span class="txt">{{userInfo.uid}}</span>
        </div>
         <div class="item text-info">
          <span class="label">手机号：</span>
          <span class="txt">{{userInfo.phone}}</span>
          <div class="edit-txt" @click="isPhone = true">修改</div>
        </div>
        <div class="item text-info">
          <span class="label">我的密码：</span>
          <span class="txt">******</span>
          <div class="edit-txt" @click="isPassword = true">修改</div>
        </div>
        <div class="out-btn">
          <span @click="longOut">退出登录</span>
        </div>
      </div>
      <!-- 修改密码 -->
      <el-dialog
        title="修改密码"
        :visible.sync="isPassword"
        width="545px"
        :before-close="handleClose">
        <div class="form-box">
          <div class="input-item">
            <el-input placeholder="请输入手机号码" v-model="userInfo.phone" disabled>
              <template slot="prepend">+86</template>
            </el-input>
          </div>
          <div class="input-item">
            <el-input placeholder="请输入验证码" v-model="passwordData.code">
            </el-input>
            <el-button plain class="code-box" @click="getVerify(0)" :disabled="disabled">{{ text }}</el-button>
          </div>
          <div class="input-item" v-if="isShowCode">
            <el-input v-model="codeVal" placeholder="请输入验证码" class="verifiCode"></el-input>
            <span class="imageCode" @click="again"><img :src="codeUrl" alt=""></span>
          </div>
          <div class="input-item">
            <el-input placeholder="请输入新密码" type="password" v-model="passwordData.newPassword">
            </el-input>
          </div>
        </div>
        <div class="dialog-footer">
          <span slot="footer" >
            <el-button type="primary" @click="bindPassword">提 交</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </span>
        </div>
      </el-dialog>
      <!-- 修改手机号码 -->
      <el-dialog
        title="修改手机号码"
        :visible.sync="isPhone"
        width="545px"
        :before-close="handleClose">
        <div class="form-box">
          <div class="input-item">
            <el-input placeholder="请输入新手机号码" v-model="phoneData.newPhone">
              <template slot="prepend">+86</template>
            </el-input>
          </div>
          <div class="input-item">
            <el-input placeholder="请输入验证码" v-model="phoneData.code">
            </el-input>
            <el-button plain class="code-box" @click="getVerify(1)" :disabled="disabled">{{ text }}</el-button>
          </div>

        </div>
        <div class="dialog-footer">
          <span slot="footer" >
            <el-button type="primary" @click="bindNewPhone">提 交</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </span>
        </div>

      </el-dialog>
      <Verify @success="verifySuccess" captchaType="clickWord" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import sendVerifyCode from "@/mixins/SendVerifyCode";
import {Message, MessageBox} from 'element-ui';
import Verify from '@/components/verifition/Verify';
export default {
        name: "index",
        auth: "guest",
        components: {Verify},
        mixins: [sendVerifyCode],
        data(){
          return {
            userInfo:{},
            fileList:[],
            upLoadUrl:process.env.BASE_URL+'/upload/image',
            myHeaders: { },
            isPassword:false, //修改密码号码弹窗
            passwordData:{
              phone:'',
              code:'',
              newPassword:''
            },
            isPhone:false, //修改手机号码弹窗
            phoneData:{
              code:'',
              newPhone:''
            },
            keyCode:'',
            isShowCode: false,
            codeUrl: '',
            codeVal: '',
            codeKey: '',
            currentType: 0
          }
        },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "账户管理-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
      },
      mounted() {
          let local = this.$cookies.get('auth.strategy')
          this.myHeaders = {
            'Authorization': this.$cookies.get(`auth._token.${local}`)
          }
          this.userInfo = this.$auth.user
          
      },
     methods:{
      again() {
        this.getcaptcha()
      },
      // 获取验证码的key
      getcaptcha() {
        let that = this;
        that.$axios.post("/api/captcha").then(res=>{
          that.codeUrl = data.data.captcha; //图片路径
          that.codeVal = data.data.code; //图片验证码
          that.codeKey = data.data.key //图片验证码key
          that.isShowCode = true;
        }).catch(err => {
            that.$message.error(err);
        });
      },
      // 退出登录
      async longOut(){
        let val = this.$cookies.get('auth.strategy')
        await this.$auth.logout(val).then(res=>{
          this.$store.commit('cartNum', 0);
          this.$router.replace({
            path:'/'
          })
        })
      },
      // 修改密码
      bindPassword(){
        let that = this
        if(!that.passwordData.code) return  Message.error('请填写验证码');
        if(!that.passwordData.newPassword) return  Message.error('请填写新密码');
        if (that.isShowCode && !that.codeVal) return Message.error('请填写图片验证码');
        this.$axios.post('/api/user/change_pwd',{
          phone:that.userInfo.phone,
          sms_code:that.passwordData.code,
          pwd:that.passwordData.newPassword
        }).then(res=>{
          Message.success(res.message);
          this.isPassword = false
          this.passwordData.phone = ''
          this.passwordData.code = ""
          this.passwordData.newPassword = ""
        }).catch(res=>{
        //   that.getcaptcha();
        })
      },
      handleClose(){
        this.isPassword = false
        this.isPhone = false
        this.passwordData.phone = ''
        this.passwordData.code = ""
        this.passwordData.newPassword = ""
        this.phoneData.code = ""
        this.phoneData.newPhone = ""
      },
      // 发送验证码
      async getCode(type, data){
        let that = this;
        if(type == 0){
          await this.$axios.post("/api/auth/verify",{
            phone: that.userInfo.phone,
            type: 'change_pwd',
            // code: that.codeVal,
            captchaType: "clickWord",
            captchaVerification: data.captchaVerification
          }).then(res=>{
            Message.success(res.message);
            that.sendCode();

          }).catch(res => {
            Message.error(res);
          });
        }else{
          await this.$axios.post("/api/auth/verify",{
            phone: that.phoneData.newPhone,
            type: 'change_phone',
            code: that.phoneData.code,
            captchaType: "clickWord",
            captchaVerification: data.captchaVerification
          }).then(res=>{
            Message.success(res.message);
            that.sendCode();
          }).catch(res => {
            Message.error(res);
          });
        }
      },
      // 绑定新手机号码
      async bindNewPhone(){
        let that = this
        if (!that.phoneData.newPhone) return Message.error('请填写新手机号码');
        if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phoneData.newPhone)) return Message.error('请输入正确的手机号码');
        if(!that.phoneData.code) return  Message.error('请填写验证码');
        this.$axios.post('/api/user/change/phone',{
        phone:that.phoneData.newPhone,
        sms_code:that.phoneData.code,
        // key:that.keyCode
      }).then(res=>{
        Message.success(res.message);
        this.isPhone = false
        this.phoneData.newPhone = ''
        this.phoneData.code = ""
      }).catch(err=>{
        return Message.error(err);
      })
    },
    getVerify(type) {
      this.currentType = type;
      this.$refs.verify.show();
    },
    verifySuccess(params) {
      this.closeModel(params);
    },
    // 关闭模态框
    closeModel(params) {
      this.isShow = false;
      this.getCode(this.currentType, params);
    },
  },
}
</script>

<style lang="scss" scoped>
.user-index{
  .user-content{
    padding:34px 0;
    .item{
      padding-left: 16px;
      position: relative;
      font-size: 14px;
      border-bottom: 1px dashed #DDDDDD;
      margin-right: 54px;
      .edit-txt{
        position: absolute;
        right: 0;
        bottom: 16px;
        color: #E93323;
        cursor: pointer;
      }
      .label{
        display: inline-block;
        width: 80px;
        color: #777777;
      }
      &.user-info{
        padding-bottom: 20px;
        .title{
          color: #282828;
          font-size: 18px;
        }
        .info{
          margin-top: 20px;
          color: #777777;
          font-size: 14px;
          img{
            width: 80px;
            height: 80px;
            border-radius: 50%;
            vertical-align: -44px;
            display: inline-block;
          }
        }
      }
      &.text-info{
        height: 70px;
        line-height: 70px;
        .edit-txt{
          top: 0;
        }
        .txt{
          color: #282828;
          width: 700px;
        }
      }
    }
    .out-btn{
      text-align: right;
      margin-right: 54px;
      span{
        display: inline-block;
        width: 130px;
        height: 40px;
        margin-top: 38px;
        line-height: 40px;
        text-align: center;
        background: #E93323;
        color: #fff;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
}
.input-item{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .code-box{
    width: 115px;
    height: 40px;
    text-align: center;
    cursor: pointer;
    margin-left: 30px;
  }
}
.dialog-footer{
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 40px;
  border-top: 1px solid #EFEFEF;
  button{
    width: 190px;
    height: 45px;
  }
}
</style>
