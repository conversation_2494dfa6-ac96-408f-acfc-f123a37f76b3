<template>
  <div>
    <div class="categoryMain">
      <div class="categoryCount">
        <div class="list acea-row row-middle">
            <div class="categoryBtn" @mouseenter="seen = true">
                商品分类
            </div>
            <a @click="goUrl(item.link)" :class="{ 'font-color': item.link === $route.path || item.link === $route.fullPath }" class="item-link" v-for="(item, index) in headerList" :key="index">
              {{item.label}}
            </a>
       </div>
       <div v-if="seen" class="category acea-row row-middle" @mouseleave="leave()">
        <div class="sort" :class="categoryList.length > 10 ? 'sort-scroll' : ''">
          <div
            class="item acea-row row-between-wrapper"
            :class="index === current ? 'bg-color' : ''"
            v-for="(item, index) in categoryList"
            :key="index"
            @mouseenter="enter(index)"
          >
            <div class="name line1" @click="goCate('','',item)">{{ item.cate_name }}</div>
            <div class="iconfont icon-you"></div>
          </div>
        </div>
        <!-- <div class="sortCon" v-if="seen"> -->
        <div class="sortCon" v-if="categoryCurrent">
          <div class="erSort">
            <div
              class="item acea-row"
              v-for="(item, index) in categoryCurrent.children"
              :key="index"
            >
              <div class="name line1" @click="goCate('',categoryCurrent,item)">{{ item.cate_name }}</div>
              <div class="cateList">
                <span class="cateItem"
                 @click="goCate(categoryCurrent,item,itemn)"
                 v-for="(itemn, indexn) in item.children" :key="indexn">
                    {{ itemn.cate_name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "categoryList",
      data(){
        return{
          headerList:[],
          seen: false,
          current: -1,
          categoryList: [],
          categoryCurrent: null,
        }
      },
      head() {

      },
      beforeMount(){
        this.getHeaderList();
        this.getCategoryList();
      },
      mounted(){
      },
      methods:{
        enter(index) {
            this.seen = true;
            this.current = index;
            this.categoryCurrent = this.categoryList[index];
        },
        leave() {
            this.seen = false;
            this.current = -1;
        },
        getCategoryList(){
          this.$axios.get("/api/store/product/category/lst").then(res=>{
            this.categoryList = res.data.list;
          })
        },
        goCate(categoryCurrent,item,itemn){
          this.seen = false
          let name = '';
          if(categoryCurrent && item){
            name = categoryCurrent.cate_name+'_'+categoryCurrent.store_category_id+','+item.cate_name+'_'+item.store_category_id+','+itemn.cate_name+'_'+itemn.store_category_id
          }else{
            if(item){
              name = item.cate_name+'_'+item.store_category_id+','+itemn.cate_name+'_'+itemn.store_category_id
            }else{
              name = itemn.cate_name+'_'+itemn.store_category_id
            }
          }
            this.$router.push({
                path: `/goods_cate`,
                query:{
                    sid:itemn.store_category_id,
                    name: encodeURI(name)
                }
            })
        },
        goUrl(url){
          if(!url)return;
          let isHttp = url.substring(0,4) == 'http' ? true : false;
          if(isHttp){
            location.href = url;
          }else{
            this.$router.push({ path: url });
          }
        },
        /**获取导航 */
        getHeaderList(){
            this.$axios.get("/api/pc/home").then(res=>{
                this.headerList = res.data.pc_home_tab;               
            })
        }
      }
    }
</script>

<style scoped lang="scss">
.sort-scroll::-webkit-scrollbar{
  width: 0;
}
.sort-scroll::-webkit-scrollbar-track{
  background: transparent;
}
.sort-scroll::-webkit-scrollbar-thumb{
  background: transparent;
}
.sort-scroll::-webkit-scrollbar-corner{
  background: transparent;
}
.categoryMain{
    background: #fff;
    .categoryCount{
        position: relative;
        width: 1200px;
        margin: 0 auto;
    }
    .categoryBtn{
      width: 208px;
      height: 44px;
      line-height: 44px;
      color: #fff;
      background-color: #E93323;
      text-align: center;
      cursor: pointer;
    }
    .item-link{
       padding: 11.5px 10px;
       margin-right: 13px;
       color: #282828;
       font-size: 16px;
       font-weight: 400;
       cursor: pointer;
       min-width: 90px;
       display: inline-block;
       text-align: center;
       &:hover{
          color: #E93323;
       }
    }
    .category {
      position: absolute;
      top: 44px;
      left: 0;
      z-index: 9;
      height: 450px;
    }
    .sort {
      width: 208px;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.96);
      color: #fff;
      padding: 14px 0;
      .item {
        height: 35px;
        padding: 0 21px;
        cursor: pointer;
        .name {
          width: 150px;
        }
        .iconfont {
          font-size: 10px;
        }
      }
      &.sort-scroll{
        overflow-y: scroll;
        .name {
          width: 125px;
        }
      }
    }
    .sortCon {
      width: 664px;
      height: 100%;
      background-color: #fff;
      box-shadow: 5px 1px 10px rgba(0, 0, 0, 0.2);
      border: 1px solid #f2f2f2;
      border-left: 0;
      border-right: 0;
      padding: 0 5px 20px 29px;
      .title {
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f1f1;
        .font-color {
          padding-bottom: 8px;
          font-size: 16px;
          border-bottom: 2px solid #e93323;
        }
      }
      .erSort {
        overflow-x: hidden;
        overflow-y: auto;
        max-height: 400px;
        padding-top: 20px;
        .item {
          margin-top: 10px;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          .name {
            color: #282828;
            margin-left: 14px;
            width: 66px;
            font-weight: bold;
            cursor: pointer;
          }
        }
        .cateList{
            width: 548px;
            .cateItem{
                text-align: center;
                padding: 0 20px;
                display: inline-block;
                color: #888888;
                font-size: 12px;
                margin-bottom: 12px;
                cursor: pointer;
                &:hover{
                    color: #e93323;
                }

            }
        }

      }
    }
}

</style>
