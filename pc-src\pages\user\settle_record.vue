<template>
    <div class="user-index">
      <div class="user-com-title" style="padding-left: 16px">入驻记录</div>
      <div class="user-content">
        <div v-if="recordList.length" class="settled-count">
            <div class="settled-item" v-for="(item, index) in recordList" :key="index">
                <div class="title">
                    申请信息
                    <div v-if="item.status == 0" class="settled-status"><span class="el-icon-info status0"></span>待审核</div>
                    <div v-else-if="item.status == 1" class="settled-status"><span class="el-icon-success status1"></span>审核通过</div>
                    <div v-else-if="item.status == 2" class="settled-status"><span class="el-icon-error status2"></span>审核未通过</div>
                </div>
                <div class="item-content">
                    <div class="list">商户名称：{{item.mer_name}}</div>
                    <div class="list">提交时间：{{item.create_time}}</div>
                </div>
                <div class="item-operate">
                    <el-button v-if="item.status == 0" class="settled-btn" type="primary" size="small" @click="goInfo(item.mer_intention_id)">编辑</el-button>
                    <el-button v-else-if="item.status == 1" class="settled-btn" type="primary" size="small" @click="goDetail(item.mer_intention_id)">查看</el-button>
                    <div v-else-if="item.status == 2">
                        <span class="reason">原因： {{ item.fail_msg }}</span>
                        <el-button class="settled-btn" type="primary" size="small" @click="goInfo(item.mer_intention_id)">重新提交</el-button>
                    </div>

                </div>
            </div>
        </div>
        <div class="empty-box" v-else>
            <img src="~assets/images/nosort.png" alt="">
            <p>亲，暂无记录</p>
        </div>
      </div>
      <!-- 审核通过弹窗 -->
      <el-dialog
        :modal="true"
        :visible.sync="isDialog"
        width="380"
        :show-close="true"
      >
        <div class="merchant-details">
            <div class="top">
                <img class="img" src="~/assets/images/successTop.png" alt="" />
                <div class="title">
                    恭喜，您的申请已通过！
                </div>
            </div>
            <div class="msg" v-if="resData.mer_id > 0 && resData.login_url">
                <div class="url">
                    <span class="head">登录地址：</span>
                    <a :href="resData.login_url" target="blank" class="content">{{resData.login_url}}</a>
                </div>
                <div class="phone">
                    <div class="">
                        <span class="head">商户账号：</span>
                        <span class="content">{{resData.phone}}</span>
                    </div>
                </div>
            </div>
            <div class="btn" v-if="resData.mer_id > 0">
                <div class="">
                    温馨提示：初始密码默认为手机号后六位，请初次登录后及时修改
                </div>
            </div>
        </div>
      </el-dialog>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
        name: "index",
        auth: "guest",
        data(){
          return {
            recordList: [],
            isDialog: false,
		    resData: {}
          }
        },
      fetch({ store }) {
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "入驻记录-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
      },
      mounted() {
        this.getRecordList();
      },
      methods:{
        getRecordList(){
            this.$axios.get(`/api/intention/lst`).then(res => {
                this.recordList = res.data.list;
            });
        },
        goDetail(id){
            this.isDialog = true;
            this.$axios.get(`/api/intention/detail/`+id).then(res => {
                this.resData = res.data;
            });
        },
        goInfo(id){
            this.$router.push({
                path: `/merchant_settled`,
                    query:{
                        intention_id: id
                    }
            })
        },
        onCopy(e) {
            this.$message.success("复制成功");
        },
        onError(e) {
            this.$message.error("复制出错");
        },
    }
  }
</script>
<style lang="scss" scoped>
.user-index{
  .user-content{
    padding:34px 0;
  }
}
.settled-count{
    .settled-item{
        width: 950px;
        box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.06);
        margin-bottom: 20px;
        .title{
            height: 53px;
            line-height: 53px;
            font-size: 16px;
            padding: 0 20px;
            border-bottom: 1px dashed #ECECEC;
            .settled-status{
                float: right;
                color: #282828;
                font-weight: bold;
                font-size: 16px;
                span{
                    font-size: 22px;
                    margin-right: 10px;
                    position: relative;
                    top: 2.2px;
                }
                .status0{
                    color: rgb(51,130,247);
                }
                .status1{
                    color: rgb(97,206,116);
                }
                .status2{
                    color: rgb(240,68,28);
                }
            }

        }
         .item-content{
            padding: 20px 20px 10px;
            .list{
                color: #666666;
                line-height: 30px;
                font-size: 16px;
            }
         }
         .item-operate{
             padding: 0 20px 20px;
             overflow: hidden;
             .settled-btn{
                 float: right;
                 border: 1px solid #999999;
                 background-color: #fff;
                 color: #666666;
                 border-radius: 4px;
             }
             .reason{
                 color: #F0441C;

             }
         }
    }
}
::v-deep .el-dialog{
    border-radius: 12px;
    width: 480px;
}
::v-deep .el-dialog__body{
    padding: 10px 20px;
}
.merchant-details {
  text-align: center;
	background-color: #fff;
	position: relative;
	.top {
		width: 237px;
        height: 195px;
        display: inline-block;
		.img {
			width: 100%;
			height: 100%;
		}
		.title {
			font-size: 20px;
			color: #59B413;
		}
	}
	.msg {
		background-color: #F6F6F6;
		border-radius: 6px;
        margin: 20px 0;
        padding: 10px 30px;
        line-height: 30px;
		.url {
			display: flex;
			flex-wrap: nowrap;
			margin-bottom: 20px;
		}
		.phone {
			display: flex;
			justify-content: space-between;
		}
		.head {
			color: #333333;
			font-size: 14px;
			font-weight: 500;
			white-space: nowrap;
		}
		.content {
			color: #999999;
			font-size: 13px;
		}
	}
	.btn {
		color: #999999;
        margin-bottom: 10px;
	}
}

</style>
