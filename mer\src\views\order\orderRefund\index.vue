<template>
  <div class="divBox">
    <div class="selCard">
        <div class="container">
          <el-form :model="tableFrom" ref="searchForm" size="small" label-width="85px" inline>
            <el-form-item :label="$t('订单状态：')" style="display: block;" prop="status">
              <el-radio-group v-model="tableFrom.status" type="button" @change="getList(1)">
                <el-radio-button label>全部 {{ '(' +orderChartType.all?orderChartType.all:0 + ')' }}</el-radio-button>
                <el-radio-button
                  label="0"
                >待审核 {{ '(' +orderChartType.audit?orderChartType.audit:0+ ')' }}</el-radio-button>
                <el-radio-button
                  label="-1"
                >审核未通过 {{ '(' +orderChartType.refuse?orderChartType.refuse:0+ ')' }}</el-radio-button>
                <el-radio-button
                  label="1"
                >审核通过 {{ '(' +orderChartType.agree?orderChartType.agree:0+ ')' }}</el-radio-button>
                <el-radio-button
                  label="2"
                >待收货 {{ '(' +orderChartType.backgood?orderChartType.backgood:0+ ')' }}</el-radio-button>
                <el-radio-button
                  label="4"
                >维权中 {{ '(' +orderChartType.platform?orderChartType.platform:0+ ')' }}</el-radio-button>
                <el-radio-button
                  label="3"
                >已完成 {{ '(' +orderChartType.end?orderChartType.end:0+ ')' }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('时间选择：')">
              <el-date-picker
                v-model="timeVal"
                value-format="yyyy/MM/dd"
                format="yyyy/MM/dd"
                size="small"
                type="daterange"
                placement="bottom-end"
                :placeholder="$t('自定义时间')"
                style="width: 280px;"
                :picker-options="pickerOptions"
                @change="onchangeTime"
              />
            </el-form-item>
            <el-form-item :label="$t('退款单号：')" prop="refund_order_sn">
              <el-input
                v-model="tableFrom.refund_order_sn"
                clearable
                :placeholder="$t('请输入退款单号')"
                class="selWidth"
                @keyup.enter.native="getList(1)"
              />
            </el-form-item>
            <el-form-item :label="$t('退款类型：')" prop="refund_type">
              <el-select
                v-model="tableFrom.refund_type"
                :placeholder="$t('请选择')"
                class="selWidth"
                clearable
                @change="getList(1)"
              >
                <el-option
                  v-for="item in refundTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
             <el-form-item :label="$t('发起方：')" prop="user_type">
              <el-select
                v-model="tableFrom.user_type"
                :placeholder="$t('请选择')"
                class="selWidth"
                clearable
                @change="getList(1)"
              >
                <el-option
                  v-for="item in refundUserList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('订单单号：')" prop="order_sn">
              <el-input
                v-model="tableFrom.order_sn"
                clearable
                :placeholder="$t('请输入订单号')"
                class="selWidth"
                @keyup.enter.native="getList(1)"
              />
            </el-form-item>
            <!-- <el-form-item label="关键字：" prop="username">
              <el-input
                v-model="tableFrom.username"
                clearable
                placeholder="请输入用户昵称、退货人姓名、手机号"
                class="selWidth"
                @keyup.enter.native="getList(1)"
              /> 
            </el-form-item> -->
            <select-search 
              ref="selectSearch" 
              :select="select" 
              :searchSelectList="searchSelectList" 
              @search="searchList" />
            <el-form-item :label="$t('退货单号：')" prop="delivery_id">
              <el-input
                v-model="tableFrom.delivery_id"
                clearable
                :placeholder="$t('请输入退货快递单号')"
                class="selWidth"
                @keyup.enter.native="getList(1)"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="getSearchList">{{ $t('搜索') }}</el-button>
              <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
            </el-form-item>
          </el-form>
         </div>
      </div>
      <el-card class="mt14">
        <el-button size="small" type="primary" @click.native="exports">{{ $t('导出退款单') }}</el-button>
        <el-table
          v-loading="listLoading"
          :data="tableData.data"
          size="small"
          class="table mt20"
          highlight-current-row
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" inline class="demo-table-expand demo-table-expands">
                <el-form-item :label="$t('订单号：')">
                  <span>{{ props.row.order.order_sn }}</span>
                </el-form-item>
                <el-form-item :label="$t('退款商品总价：')">
                  <span v-if="props.row.order.activity === 2">{{ getPresellTotal(props.row.refundProduct) }}</span>
                  <span v-else-if="props.row.order.activity === 3">{{ getAssistTotal(props.row.refundProduct) }}</span>
                  <span v-else>{{ getTotal(props.row.refundProduct) }}</span>
                </el-form-item>
                <el-form-item :label="$t('退款商品总数：')">
                  <span>{{ props.row.refund_num }}</span>
                </el-form-item>
                <el-form-item :label="$t('申请退款时间：')">
                  <span>{{ props.row.create_time | filterEmpty }}</span>
                </el-form-item>
                <el-form-item :label="$t('用户备注：')">
                  <span>{{ props.row.mark | filterEmpty }}</span>
                </el-form-item>
                <el-form-item :label="$t('商家备注：')">
                  <span>{{ props.row.mer_mark | filterEmpty }}</span>
                </el-form-item>
                <el-form-item v-if="props.row.status === 2" :label="$t('快递公司：')">
                  <span>{{ props.row.delivery_type | filterEmpty }}</span>
                </el-form-item>
                <el-form-item v-if="props.row.status === 2" :label="$t('快递单号：')">
                  <span class="mr5">{{ props.row.delivery_id | filterEmpty }}</span>
                  <el-button type="text" @click="openLogistics(props.row)">{{ $t('物流查询') }}</el-button>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column :label="$t('退款单号')" min-width="100">
            <template slot-scope="scope">
              <span style="display: block;" v-text="scope.row.refund_order_sn" />
              <span v-if="scope.row.status == 4" style="color: #ED4014;display: block;">{{ $t('平台介入') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="user.nickname" :label="$t('用户信息')" min-width="100" />
          <el-table-column prop="refund_price" :label="$t('退款金额')" min-width="80" />
          <el-table-column prop="nickname" :label="$t('商品信息')" min-width="250">
            <template slot-scope="scope">
              <div
                v-for="(val, i ) in scope.row.refundProduct"
                :key="i"
                class="tabBox acea-row row-middle"
              >
                <div class="demo-image__preview">
                  <el-image
                    :src="val.product && val.product.cart_info.product.image"
                    :preview-src-list="[val.product && val.product.cart_info.product.image]"
                  />
                </div>
                <span
                  class="tabBox_tit"
                >{{ val.product && val.product.cart_info.product.store_name + ' | ' }}{{ val.product && val.product.cart_info.productAttr.sku }}</span>
                <span
                  v-if="scope.row.order && scope.row.order.activity_type === 2 && val.product && val.product.cart_info.productPresellAttr"
                  class="tabBox_pice"
                >{{ '￥'+ val.product && val.product.cart_info.productPresellAttr.presell_price + ' x '+ val.refund_num }}</span>
                <span
                  v-else-if="scope.row.order && scope.row.order.activity_type === 3 && val.product && val.product.cart_info.productAssistAttr.assist_price"
                  class="tabBox_pice"
                >{{ '￥'+ val.product && val.product.cart_info.productAssistAttr.assist_price + ' x '+ val.refund_num }}</span>
                <span
                  v-else
                  class="tabBox_pice"
                >{{ '￥'+ val.product && val.product.cart_info.productAttr.price + ' x '+ val.refund_num }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('退款类型')" min-width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.refund_type == 1 ? '退款' : '退货退款' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="order.real_name" :label="$t('退货人')" min-width="90" />
          <el-table-column prop="create_user" :label="$t('退款发起方')" min-width="90" />
          <el-table-column prop="serviceScore" :label="$t('退款单状态')" min-width="90">
            <template slot-scope="scope">
              <span class="tags" :class="'tag-color'+scope.row.status">{{ scope.row.status | orderRefundFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('说明')" min-width="220">
            <template slot-scope="scope">
              <span v-if="scope.row.status == -1" style="display: block">拒绝原因：{{ scope.row.fail_message }}</span>
              <span style="display: block">退款原因：{{ scope.row.refund_message }}</span>
              <span style="display: block">状态变更：{{ scope.row.status_time }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('操作')" min-width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="onOrderDetail(scope.row.order.order_id)">{{ $t('订单详情') }}</el-button>
              <el-button v-if="scope.row.status === 0" type="text" size="small" @click="onRefundOrderDetail(scope.row.refund_order_id)">{{ $t('退款单详情') }}</el-button>
              <el-button v-if="scope.row.status === 0" type="text" size="small" @click="onOrderStatus(scope.row.refund_order_id)">{{ $t('退款审核') }}</el-button>
              <!-- <el-button type="text" size="small" @click="onOrderLog(scope.row.refund_order_id)">订单记录</el-button> -->
              <el-button v-if="scope.row.status === 2" type="text" size="small" @click="confirmReceipt(scope.row)">{{ $t('退货审核') }}</el-button>
              <el-button type="text" size="small" @click="onOrderMark(scope.row.refund_order_id)">{{ $t('订单备注') }}</el-button> 
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            background
            :page-size="tableFrom.limit"
            :current-page="tableFrom.page"
            layout="total, prev, pager, next, jumper"
            :total="tableData.total"
            @size-change="handleSizeChange"
            @current-change="pageChange"
          />
        </div>
      </el-card> 
    
    <!--记录-->
    <el-dialog :title="$t('操作记录')" :visible.sync="dialogVisible" width="700px">
      <el-table v-loading="LogLoading" border :data="tableDataLog.data" style="width: 100%">
        <el-table-column prop="refund_order_id" align="center" :label="$t('退款单ID')" min-width="80" />
        <el-table-column prop="change_message" :label="$t('操作记录')" align="center" min-width="280" />
        <el-table-column prop="change_time" :label="$t('操作时间')" align="center" min-width="280" />
      </el-table>
      <div class="block">
        <el-pagination
          :page-size="tableFromLog.limit"
          :current-page="tableFromLog.page"
          layout="prev, pager, next, jumper"
          :total="tableDataLog.total"
          @size-change="handleSizeChangeLog"
          @current-change="pageChangeLog"
        />
      </div>
    </el-dialog>
    <!--退货审核-->  
    <return-review ref="returnReview" :returnData="returnData" @getList="getList" />
    <!--详情-->
    <details-from ref="orderDetail" :order-datalist="orderDatalist" @get-logistics="openLogistics" />
    <el-dialog
      v-if="dialogLogistics"
      :title="$t('物流查询')"
      :visible.sync="dialogLogistics"
      width="350px"
      :before-close="handleClose"
    >
      <logistics-from
        v-if="orderDetails"
        :order-datalist="orderDetails"
        :result="result"
        :logistics-name="logisticsName"
      />
    </el-dialog>
    <!--订单详情-->
    <order-detail
      ref="orderDetail"
      :orderId="orderId"
      @closeDrawer="closeDrawer"
      @changeDrawer="changeDrawer"
      @getList="getList"
      :disabled="true"
      :drawer="drawer"
    ></order-detail>
    <!--退款单详情-->
    <refund-details
      ref="refundDetails"
      :orderId="orderId"
      @get-logistics="getOrderData" 
      @onOrderStatus="onOrderStatus"
      @onOrderMark="onOrderMark"
      @closeDrawer="closeRefundDrawer"
      @changeDrawer="changeRefundDrawer"
      @getList="getList"
      :disabled="true"
      :drawer="refundDrawer"
    ></refund-details>
  </div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {
  refundorderStatusApi,
  refundorderListApi,
  orderUpdateApi,
  orderDeliveryApi,
  refundorderLogApi,
  refundorderDeleteApi,
  refundorderMarkApi,
  refundorderDetailApi,
  refundorderExpressApi,
  confirmReceiptApi,
  refundListImportApi
} from '@/api/order'
import detailsFrom from './refundDetail'
import refundDetails from './refundDetails'
import returnReview from './returnReview'
import logisticsFrom from '../logistics'
import { roterPre } from '@/settings'
import createWorkBook from '@/utils/newToExcel.js'
import timeOptions from '@/utils/timeOptions';
import orderDetail from '../orderDetails.vue';
import selectSearch from '@/components/base/selectSearch';
export default {
  name: 'OrderRefund',
  components: { detailsFrom, refundDetails, logisticsFrom, orderDetail, selectSearch, returnReview },
  data() {
    return {
      pickerOptions: timeOptions,
      logisticsName: 'refund',
      dialogLogistics: false,
      orderId: 0,
      drawer: false,
      refundDrawer: false,
      tableData: {
        data: [],
        total: 0
      },
      refundTypeList: [
        { value: 1, label: leaveuKeyTerms['退款'] },
        { value: 2, label: leaveuKeyTerms['退款退货'] }
      ],
      refundUserList: [
        { value: 1, label: leaveuKeyTerms['用户'] },
        { value: 3, label: leaveuKeyTerms['商户'] },
        { value: 4, label: leaveuKeyTerms['客服'] }
      ],
      listLoading: true,
      roterPre: roterPre,
      select: "nickname",
      searchSelectList: [
        {label: leaveuKeyTerms['昵称'], value: "nickname"},
        {label: leaveuKeyTerms['用户ID'], value: "uid"},
        {label: leaveuKeyTerms['手机号'], value: "phone"},
      ],
      tableFrom: {
        delivery_id: '',
        refund_type: '',
        user_type: '',
        status: '',
        date: '',
        username: '',
        page: 1,
        limit: 20,
        order_sn: this.$route.query.sn ? this.$route.query.sn : '',
        refund_order_sn: this.$route.query.refund_order_sn ? this.$route.query.refund_order_sn : '',
        id: this.$route.query.id ? this.$route.query.id : ''
      },
      orderChartType: {},
      timeVal: [],
      fromList: {
        title: leaveuKeyTerms['选择时间'],
        custom: true,
        fromTxt: [
          { text: leaveuKeyTerms['全部'], val: '' },
          { text: leaveuKeyTerms['今天'], val: 'today' },
          { text: leaveuKeyTerms['昨天'], val: 'yesterday' },
          { text: leaveuKeyTerms['最近7天'], val: 'lately7' },
          { text: leaveuKeyTerms['最近30天'], val: 'lately30' },
          { text: leaveuKeyTerms['本月'], val: 'month' },
          { text: leaveuKeyTerms['本年'], val: 'year' }
        ]
      },
      selectionList: [],
      tableFromLog: {
        page: 1,
        limit: 10
      },
      tableDataLog: {
        data: [],
        total: 0
      },
      LogLoading: false,
      dialogVisible: false,
      orderDatalist: null,
      orderDetails: {},
      result: [],
      returnData: {}
    }
  },
  mounted() {
    if (this.$route.query.hasOwnProperty('sn')) {
      this.tableFrom.order_sn = this.$route.query.sn
    } else {
      this.tableFrom.order_sn = ''
    }
    this.getList(1)
  },
  // 被缓存接收参数
  activated() {
    if (this.$route.query.hasOwnProperty('sn')) {
      this.tableFrom.order_sn = this.$route.query.sn
    } else {
      this.tableFrom.order_sn = ''
    }
    this.getList(1)
  },
  methods: {
    /**重置 */
    searchReset(){
      this.timeVal = []
      this.tableFrom.date = ""
      this.$refs.searchForm.resetFields()
      this.tableFrom.order_sn = ""
      this.$refs.selectSearch.resetParmas()
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    closeDrawer() {
      this.drawer = false;
    },
    // 订单详情
    onOrderDetail(order_id) {
      this.orderId = order_id
      this.$refs.orderDetail.getInfo(order_id);
      this.drawer = true;
    },
    // 退款
    onOrderStatus(id) {
      this.$modalForm(refundorderStatusApi(id)).then(() => this.getList(''),this.$refs.refundDetails.getRefundInfo(id))
    },
    // 确认收货
    confirmReceipt(row) {
      const that = this
      this.returnData = {
        delivery_name: row.delivery_type,
        delivery_id: row.delivery_id,
        refund_order_id: row.refund_order_id
      }
      that.$refs.returnReview.dialogVisible = true;
      // const h = this.$createElement
      // this.$msgbox({
      //   title: '是否确认收货？',
      //   message: h('div', null, [
      //     h('p', null, '退货物流公司：' + row.delivery_type),
      //     h('p', null, '退货快递单号：' + row.delivery_id)
      //   ]),
      //   showCancelButton: true,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   beforeClose: (action, instance, done) => {
      //     if (action === 'confirm') {
      //       confirmReceiptApi(row.refund_order_id)
      //         .then(res => {
      //           that.$message.success(res.message)
      //           done()
      //           that.getList('')
      //         })
      //         .catch(({ res }) => {
      //           that.$message.error(res.message)
      //         })
      //     } else {
      //       done()
      //     }
      //   }
      // }).then(action => {}).catch(e => e)
    },
    /**导出退款单 */
    async exports() {
      let excelData = JSON.parse(JSON.stringify(this.tableFrom)), data = []
      excelData.page = 1
      let pageCount = 1
      let lebData = {};
      for (let i = 0; i < pageCount; i++) {
        lebData = await this.downData(excelData)
        pageCount = Math.ceil(lebData.count/excelData.limit)
        if (lebData.export.length) {
          data = data.concat(lebData.export)
          excelData.page++
        }  
      }
      createWorkBook(lebData.header, lebData.title, data, lebData.foot,lebData.filename);
      return
    },
    /**资金流水 */
    downData(excelData) {
      return new Promise((resolve, reject) => {
        refundListImportApi(excelData).then((res) => {
          return resolve(res.data)
        })
      })
    },
    getTotal(row) {
      let sum = 0
      for (let i = 0; i < row.length; i++) {
        sum += row[i].product ? row[i].product.cart_info.productAttr.price * row[i].refund_num : ''
      }
      return sum
    },
    // 预售商品退款金额
    getPresellTotal(row) {
      let sum = 0
      for (let i = 0; i < row.length; i++) {
        sum += row[i].product ? row[i].product.cart_info.productPresellAttr.presell_price * row[i].refund_num : ''
      }
      return sum
    },
    // 助力商品退款金额
    getAssistTotal(row) {
      let sum = 0
      for (let i = 0; i < row.length; i++) {
        sum += row[i].product ? row[i].product.cart_info.productAssistAttr.assist_price * row[i].refund_num : ''
      }
      return sum
    },
    // 退款单详情
    onRefundOrderDetail(id) {
      this.orderId = id
      this.refundDrawer = true
      this.$refs.refundDetails.getRefundInfo(id)
    },
    changeRefundDrawer(v) {
      this.refundDrawer = v;
    },
    closeRefundDrawer() {
      this.refundDrawer = false;
    },
    // 详情
    // onRefundOrderDetail(id) {
    //   this.orderId = id
    //   this.$refs.orderDetail.dialogVisible = true
    //   this.loading = true
    //   refundorderDetailApi(id)
    //     .then(res => {
    //       this.orderDatalist = res.data
    //       this.loading = false
    //       this.$refs.orderDetail.onOrderLog(id)
    //     })
    //     .catch(({ message }) => {
    //       this.loading = false
    //       this.$message.error(message)
    //     })
    // },
    // 订单记录
    onOrderLog(id) {
      this.dialogVisible = true
      this.LogLoading = true
      refundorderLogApi(id, this.tableFromLog)
        .then(res => {
          this.tableDataLog.data = res.data.list
          this.tableDataLog.total = res.data.count
          this.LogLoading = false
        })
        .catch(res => {
          this.$message.error(res.message)
          this.LogLoading = false
        })
    },
    pageChangeLog(page) {
      this.tableFromLog.page = page
      this.getList('')
    },
    handleSizeChangeLog(val) {
      this.tableFromLog.limit = val
      this.getList('')
    },
    // 订单删除
    handleDelete(row, idx) {
      if (row.is_del === 1) {
        this.$modalSure().then(() => {
          refundorderDeleteApi(row.refund_order_id)
            .then(({ message }) => {
              this.$message.success(message)
              this.tableData.data.splice(idx, 1)
            })
            .catch(({ message }) => {
              this.$message.error(message)
            })
        })
      } else {
        this.$confirm(
          '您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！',
          '提示',
          {
            confirmButtonText: leaveuKeyTerms['确定'],
            type: 'error'
          }
        )
      }
    },
    // 备注
    onOrderMark(id) {
      this.$modalForm(refundorderMarkApi(id)).then(() => this.getList(''))
    },
    // 选择时间
    selectChange(tab) {
      this.tableFrom.date = tab
      this.timeVal = []
      this.getList(1)
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e
      this.tableFrom.date = e ? this.timeVal.join('-') : ''
      this.getList(1)
    },
    // 编辑
    edit(id) {
      this.$modalForm(orderUpdateApi(id)).then(() => this.getList(''))
    },
    // 发货
    send(id) {
      this.$modalForm(orderDeliveryApi(id)).then(() => this.getList(''))
    },
    searchList(data) {
      this.tableFrom = {...this.tableFrom, ...data};
      this.getList(1)
    },
    getSearchList() {
      this.$refs.selectSearch.changeSearch()
    },
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableFrom.page = num || this.tableFrom.page
      refundorderListApi(this.tableFrom)
        .then(res => {
          this.orderChartType = res.data.stat
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          this.listLoading = false
        })
        .catch(res => {
          this.$message.error(res.message)
          this.listLoading = false
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    openLogistics(row) {
      this.orderDetails = row
      this.getOrderData(row.refund_order_id)
      this.dialogLogistics = true
    },
    handleClose() {
      this.dialogLogistics = false
      this.dialogConfirm = false
    },
    // 获取订单物流信息
    getOrderData(id) {
      refundorderExpressApi(id)
        .then(async res => {
          this.dialogLogistics = true
          this.result = res.data
        })
        .catch(res => {
          this.$message.error(res.message)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-table-expands ::v-deep label {
  width: 110px !important;
  color: #99a9bf;
}
::v-deep .el-input--suffix .el-input__inner{
  padding-right: 10px;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.tabBox_tit {
  max-width: 50%;
  font-size: 12px !important;
  margin: 0 2px 0 10px;
  letter-spacing: 1px;
  padding: 5px 0;
  box-sizing: border-box;
}
.tags {
  padding: 3px 8px;
  border-radius: 2px;
  border: 1px solid #ccc;
  color: #666;
  font-size: 12px;
}
.tag-color4 {
  border-color: #ff7d00;
  color: #ff7d00;
}
.tag-color-1 {
  border-color: #e93323;
  color: #e93323;
}
.tag-color3 {
  border-color: rgb(15, 198, 194);
  color: rgb(15, 198, 194);
}


</style>
