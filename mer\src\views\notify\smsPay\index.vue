<template>
  <div>
    <div class="i-layout-page-header">
      <el-card class="product_tabs">
        <div>
          <router-link :to="{path:roterPre+'/setting/sms/sms_config/index'}"><el-button icon="el-icon-arrow-left" size="mini" class="mr20">{{ $t('返回') }}</el-button></router-link>
          <span class="mr20" v-text="$t($route.meta.title)" />
        </div>
      </el-card>
    </div>
    <el-card class="ivu-mt">
      <el-tabs v-model="isChecked" @tab-click="onChangeType">
        <el-tab-pane :label="$t('商品采集')" name="copy" />
        <el-tab-pane :label="$t('电子面单打印')" name="dump" />
      </el-tabs>
      <el-row v-loading="spinShow" :gutter="16" class="mt50">
        <el-col :span="24" class="ivu-text-left mb20">
          <el-col :xs="12" :sm="6" :md="4" :lg="2" class="mr20">
            <span class="ivu-text-right ivu-block">{{ $t('当前剩余条数：') }}</span>
          </el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <span>{{ numbers }}</span>
          </el-col>
        </el-col>
        <el-col :span="24" class="ivu-text-left mb20">
          <el-col :xs="12" :sm="6" :md="4" :lg="2" class="mr20">
            <span class="ivu-text-right ivu-block">{{ $t('选择套餐：') }}</span>
          </el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <el-row :gutter="20">
              <el-col v-for="(item, index) in list" :key="index" :xxl="4" :xl="8" :lg="8" :md="12" :sm="24" :xs="24">
                <div
                  class="list-goods-list-item mb15"
                  :class="{active:index === current}"
                  @click="check(item,index)"
                >
                  <div class="list-goods-list-item-title" :class="{active:index === current}">¥ <i>{{ item.price }}</i></div>
                  <div class="list-goods-list-item-price" :class="{active:index === current}">
                    <span>{{ all[isChecked] }}条数: {{ item.num }}</span>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-col>
        <el-col v-if="checkList" :span="24" class="ivu-text-left mb20">
          <el-col :xs="12" :sm="6" :md="4" :lg="2" class="mr20">
            <span class="ivu-text-right ivu-block">{{ $t('充值条数：') }}</span>
          </el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <span>{{ checkList.num }}</span>
          </el-col>
        </el-col>
        <el-col v-if="checkList" :span="24" class="ivu-text-left mb20">
          <el-col :xs="12" :sm="6" :md="4" :lg="2" class="mr20">
            <span class="ivu-text-right ivu-block">{{ $t('支付金额：') }}</span>
          </el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <span class="list-goods-list-item-number">￥{{ checkList.price }}</span>
          </el-col>
        </el-col>
        <el-col :span="24" class="ivu-text-left mb20">
          <el-col :xs="12" :sm="6" :md="4" :lg="2" class="mr20">
            <span class="ivu-text-right ivu-block">{{ $t('付款方式：') }}</span>
          </el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <span class="list-goods-list-item-pay">{{ $t('微信支付') }}<i v-if="code.endtime">{{ '  （ 支付码过期时间：' + code.endtime + ' ）' }}</i></span>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :xs="12" :sm="6" :md="4" :lg="3" class="mr20">&nbsp;</el-col>
          <el-col :xs="11" :sm="13" :md="19" :lg="20">
            <div class="list-goods-list-item-code mr20">
              <!-- <img v-if="code.code_url" v-lazy="code.code_url"> -->
              <vue-qr class="bicode" :text="codeUrl" :size="310" />
            </div>
          </el-col>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { smsPriceApi, payCodeApi, serveInfoApi } from '@/api/setting'
import { roterPre } from '@/settings'
// 二维码组件
import VueQr from 'vue-qr'
export default {
  name: 'CopyPay',
  components: { VueQr },
  data() {
    return {
      roterPre: roterPre,
      all: { 'copy': '商品采集', 'dump': '电子面单打印' },
      isChecked: 'copy',
      numbers: '',
      account: '',
      list: [],
      current: 0,
      checkList: {},
      spinShow: false,
      codeUrl: '',
      code: {}
    }
  },
  created() {
    this.isChecked = this.$route.query.type
    this.getServeInfo()
    this.getPrice()
  },
  methods: {
    // 平台用户信息
    getServeInfo() {
      serveInfoApi().then(async res => {
        const data = res.data
        switch (this.isChecked) {
          case 'copy':
            this.numbers = data.copy_product_num
            break
          case 'dump':
            this.numbers = data.export_dump_num
            break
        }
      }).catch(res => {
        this.$message.error(res.message)
      })
    },
    onChangeType(val) {
      this.current = 0
      this.getPrice()
      this.getServeInfo()
    },
    // 支付套餐
    getPrice() {
      this.spinShow = true
      smsPriceApi({ type: this.isChecked }).then(async res => {
        setTimeout(() => {
          this.spinShow = false
        }, 800)
        const data = res.data
        this.list = data.list
        this.checkList = this.list[0]
        if (this.checkList) this.getCode(this.checkList)
      }).catch(res => {
        this.spinShow = false
        this.$message.error(res.message)
        this.list = []
      })
    },
    // 选中
    check(item, index) {
      this.spinShow = true
      this.current = index
      setTimeout(() => {
        this.getCode(item)
        this.checkList = item
        this.spinShow = false
      }, 800)
    },
    // 支付码
    getCode(item) {
      const data = {
        pay_type: 1,
        meal_id: item.meal_id
      }
      payCodeApi(data).then(async res => {
        this.code = res.data
        this.codeUrl = res.data.config
      }).catch(res => {
        this.code = ''
        this.$message.error(res.message)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
    .active{
        background: #0091FF;
        box-shadow:0px 6px 20px 0px rgba(0, 145, 255, 0.3);
        color: #fff !important;
    }
    .list-goods-list-item{
        border: 1px solid #DADFE6;
        padding: 20px 10px;
        box-sizing: border-box;
        border-radius:3px;
    }
    .list-goods-list{
        &-item{
            text-align: center;
            position: relative;
            cursor: pointer;
            img{
                width: 60%;
            }
            .ivu-tag{
                position: absolute;
                top: 10px;
                right: 10px;
            }
            &-title{
                font-size: 16px;
                font-weight: bold;
                color: #0091FF;
                margin-bottom: 3px;
                i{
                    font-size: 30px;
                    font-style: normal;
                }
            }
            &-desc{
                font-size: 14px;
                color: #808695;
            }
            &-price{
                font-size: 14px;
                color: #000000;
                s{
                    color: #c5c8ce;
                }
            }
            &-number{
                font-size: 14px;
                color: #ED4014;
            }
            &-pay{
                font-size: 14px;
                color: #00C050;
                i{
                    font-size: 12px;
                    font-style: normal;
                    color: #6D7278;
                }
            }
            &-code{
                width: 130px;
                height: 130px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
</style>
