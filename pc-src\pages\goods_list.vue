<template>
  <div class="goodsSearch">
    <category-List></category-List>
    <div class="goodsBanner" v-if="banner">
      <div v-swiper:mySwiper="swiperOption">
        <div class="swiper-wrapper">
          <nuxt-link
            :to="item.link === undefined ? '' : item.link"
            class="swiper-slide"
            v-for="(item, index) in banner"
            :key="index">
            <img :src="item.pic" />
          </nuxt-link>
        </div>
        <div class="swiper-pagination paginationBanner" slot="pagination"></div>
        <div class="swiper-button-prev" slot="pagination"></div>
        <div class="swiper-button-next" slot="pagination"></div>
      </div>
    </div>
    <div class="wrapper_1200">
      <div class="goods-title">
        <div class="goods-name">
          <img :src='"../assets/images/classifiy-"+headerTitle+".png"' alt="">
        </div>
        <div class="goods-desc">{{goodsTitle}}</div>
      </div>
      <div class="list acea-row row-middle" v-if="productslist.length">
        <div class="item" v-for="(item, index) in productslist" :key="index" @click="goDetail(item)">
          <div class="pictrue"><img :src="item.image"><div v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></div></div>
          <div class="top">
            <div class="info line2">{{item.store_name}}</div>
            <div class="price"><span class="font-color">¥{{item.price}}</span></div>
          </div>
        </div>
      </div>
      <div class="pages-box" v-if="total > 0">
        <el-pagination
        background
        layout="prev, pager, next"
        @current-change="bindPageCur"
        :pageSize="limit"
        :total="total">
        </el-pagination>
      </div>
      <div class="noCart" v-if="!productslist.length">
        <div class="pictrue"><img src="../assets/images/noGoods.png"></div>
        <div class="tip">亲，暂无商品哟~</div>
      </div>
      <hotGoods v-if="!productslist.length"></hotGoods>
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import hotGoods from '@/components/hotGoods';
import categoryList from "@/components/categoryList";
export default {
    name: "goods_list",
    auth: false,
    components:{
      hotGoods,
      categoryList
    },
    data(){
      return{
        productslist:[],
        pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
        page: 1, //代表页面的初始页数
        limit:11,
        banner: '',
        scollY: null,// 离底部距离有多少
        total: 0, //总页数
        title:'下拉加载更多',
        headerTitle: 'best',
        name: '精品推荐',
        goodsTitle: '严格选品标准，精挑细选3W+最优好货',
        nameUrl: '../assets/images/classifiy-best.png',
        typeData: {
            best:{
                nameUrl:'../assets/images/classifiy-best.png',
                name:'精品推荐',
                title: '严格选品标准，精挑细选3W+最优好货'
            },
            hot:{
                nameUrl:'../assets/images/classifiy-hot.png',
                name:'热门榜单',
                title: '严格选品标准，精挑细选3W+最优好货'
            },
            new:{
                nameUrl:'../assets/images/classifiy-new.png',
                name:'首页新品',
                title: '严格选品标准，精挑细选3W+最优好货'
            },
            good:{
                nameUrl:'../assets/images/classifiy-good.png',
                name:'推荐单品',
                title: '严格选品标准，精挑细选3W+最优好货'
            }
        },
        swiperOption: {
          pagination: {
            el: ".paginationBanner",
            clickable: true
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev"
          },
          autoplay: {
            disableOnInteraction: false,
            delay: 5000
          },
          loop: true,
          speed: 1000,
          observer: true,
          observeParents: true,
          id: ''
        },
      }
    },
    watch:{
      $route: {
        handler: function(newVal, oldVal){
          this.headerTitle = newVal.query.type ? newVal.query.type : oldVal;
          if(newVal){
            this.getProductslist();
            this.getTitle();
            this.getBanner();
          }
        },
        // 深度观察监听
        deep: true
      }
    },
    async asyncData({app,query,error}){
      try{
        let [productslist] = await Promise.all([
          app.$axios.get("/api/product/spu/hot/"+query.type, {
            params: {
              page: 1, limit: 11, common: 1
            }
          }),
        ]);
        return {
          headerTitle:query.type,
          productslist: productslist.data.list,
          total: productslist.data.count
        }
      }catch (e){
        error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
      }

    },
    fetch({ store}) {
      store.commit('isHeader', true);
      store.commit('isFooter', true);
    },
    head() {
      return {
        title: "商品列表-"+this.$store.state.titleCon
      }
    },
    beforeMount(){
      this.getTitle();
      this.getBanner();
    },
    mounted(){
    //   this.pullRefresh();
    },
    beforeDestroy() {
      window.onscroll = null;
    },
    methods:{
      getTitle: function(){
        this.nameUrl = this.typeData[this.headerTitle] ? this.typeData[this.headerTitle]['nameUrl'] : '../assets/images/classifiy-best.png';
        this.goodsTitle = this.typeData[this.headerTitle] ? this.typeData[this.headerTitle]['title'] : '严格选品标准，精挑细选3W+最优好货';
      },
      goDetail: function (item) {
        if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
        }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
        }
      },
      getProductslist(){
        let _this = this,currentPage = {page: this.page,limit:this.limit, common: 1};
        _this.$axios.get('/api/product/spu/hot/'+ _this.headerTitle, {
          params: currentPage
        }).then(function (res) {
          _this.total = res.data.count;
          _this.productslist = res.data.list;
        }).catch(function (err) {
          _this.$message.error(err);
        })
      },
      getBanner(){
        let _this = this
        _this.banner = []
        _this.$axios.get('/api/pc/hot_banner/'+ _this.headerTitle).then(function(res) {
          _this.banner = res.data.length ? res.data : []
        }).catch(function (err) {
          _this.$message.error(err);
        })
      },
       // 分页点击
      bindPageCur(data){
        this.page = data
        window.scrollTo({
          top: 0,
          left: 0,
        });
        this.getProductslist()
       }
    }
  }
</script>

<style scoped lang="scss">
    .goodsBanner{
      width: 100%;
      height: 500px;
      margin: 0 auto;
      .swiper-slide{
        width: 1200px;
        height: 500px;
        img{
          object-fit: none;
          width: 100%;
          height: 100%;
        }
      }

    }
    .goods-title{
        text-align: center;
        padding: 60px 0 40px;
        .goods-name{
            width: 450px;
            height: 42px;
            display: inline-block;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .goods-desc{
            color: #333333;
            font-size: 16px;
            margin-top: 10px;
        }
    }
  .nav{
    width: 100%;
    height: 96px;
    background: #FFFFFF;
    .navCon{
      height:100%;
      .textPic{
        height: 100%;
        .icon{
          font-size: 100px;
        }
        .list{
          margin-left: 85px;
          .item{
            padding: 15px 10px;
            margin-right: 13px;
            color: #282828;
            font-size: 16px;
            font-weight: 400;
            cursor: pointer;
            &:hover{
              color: #E93323;
            }
          }

        }
      }
      .search{
        width: 360px;
        height: 40px;
        border: 1px solid #E93323;
        border-radius: 2px;
        cursor: pointer;
        .text{
          width: 290px;
          padding-left: 14px;
          color: #C1C1C1;
          input{
            width: 250px;
            height: 36px;
            border:none;
            outline: none;
            padding-left: 10px;
            margin-top: -2px;
          }
          .iconfont{
            font-size: 15px;
            margin-right: 5px;
          }
        }
        .bnt{
          width: 64px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          color: #fff;
        }
      }
    }
  }
  .goodsSearch{
    .title{
      height: 60px;
      line-height: 60px;
      color: #999999;
      .home{
        color: #282828;
        font-size: 14px;
      }
    }
    .list{
      width: 1250px;
      .item{
        padding: 16px;
        width: 270px;
        height: 360px;
        background-color: #fff;
        margin: 0 40px 20px 0;
        cursor: pointer;
        &:hover{
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
        &:nth-child(4n-1){
            margin-right: 0;
        }
        .pictrue{
            position: relative;
          width: 231px;
          height: 231px;
          img{
            width: 100%;
            height: 100%;
          }
          .border-picture {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: center/cover no-repeat;
          }
        }
        .top{
          margin-top: 10px;
          .font-color{
            font-size: 22px;
            font-weight: bold;
          }
        }
        .info{
          margin-top: 8px;
          color: #5A5A5A;
        }
        .price{
            margin: 15px auto 0;
            text-align: center;
        }
        &:nth-child(1){
            width: 580px;
            height: 360px;
            overflow: hidden;
            padding: 50px 24px;
            .top{
                float: left;
                width: 230px;
                position: relative;
                top: 75px;
            }
            .pictrue{
                float: right;
                width: 260px;
                height: 260px;
            }
        }
      }
    }
    .noCart{
      text-align: center;
      margin-bottom: 70px;
      .pictrue{
        width: 216px;
        height: 136px;
        margin: 0 auto;
        img{
          width: 100%;
          height: 100%;
        }
        .tip{
          margin-top: 12px;
          color: #646464;
        }
      }
    }
  }
</style>
