<template>
  <div class="user-order-list">
    <div class="user-com-tab">
       <span class="item" :class="{on:tabCur === item.key}" v-for="(item,index) in tabList" :key="index" @click="bindTab(item)">{{item.title}}</span>
    </div>
    <div v-if="orderList.length > 0" class="order-list">
      <ul>
        <li v-for="(item,index) in orderList" :key="index">
          <div class="bd">
            <div class="title" @click="goStore(item)">
				      <span class="txt">{{item.merchant&&item.merchant.mer_name}}</span>
			      </div>
            <div class="content">
              <div v-for="goods in item.refundProduct" :key="goods.order_product_id" class="goods-item">
                <div class="img-box" v-if="goods.product.cart_info && goods.product.cart_info.product.image"><img :src='goods.product.cart_info.product.image' alt=""></div>
                <div class="info-txt">
                    <div class="title line2">{{goods.product.cart_info.product.store_name}}</div>
                    <div class="price">退款： {{ item.refund_price }}</div>
                    <span class="num">x{{ goods.refund_num }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="foot">
            <p>共{{ getRefundTotal(item)}}件商品，订单总金额 <span>￥{{ item.refund_price }} </span>
            </p>
            <div class="btn-wrapper" v-if="item.status == 1">
              <div class="rest" @click="goPage(item.refund_order_id)">退回商品</div>
              <div class="pay" @click="goDetail(item)">查看详情</div>
            </div>
            <div class="btn-wrapper" v-else-if="item.status == -1">
              <div class="rest" @click="applyAgain(item)">再次申请</div>
              <div class="pay" @click="goDetail(item)">查看详情</div>
            </div>
            <div class="btn-wrapper" v-else>
              <div class="rest" v-if="item.status == 3" @click="bindDetele(item,index)">删除记录</div>
              <div class="pay" @click="goDetail(item)">查看详情</div>
            </div>
          </div>
          <div class="refund-icon">
            <div class="iconfont icon-shenhezhong1 red-color" v-if="item.status == 0"></div>
            <div class="iconfont icon-daituihuo" v-if="item.status == 1"></div>
            <div class="iconfont icon-tuihuozhong" v-if="item.status == 2"></div>
            <div class="iconfont icon-yiquxiao" v-if="item.status == -2"></div>
            <div class="iconfont icon-yituikuan" v-if="item.status == 3"></div>
            <div class="iconfont icon-yijujue1" v-if="item.status == -1"></div>
          </div>
        </li>
      </ul>
    </div>
    <div class="pages-box" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="bindPageCur"
          :pageSize="limit"
          :total="total">
        </el-pagination>
      </div>
      <div class="empty-box" v-if="orderList.length == 0">
        <img src="~assets/images/noorder.png" alt="">
        <p>亲，暂无订单信息哟~</p>
      </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message, MessageBox} from "element-ui";
export default {
    name: "orderList",
    auth: "guest",
    scrollToTop: true,
    data() {
      return {
        tabCur: '0',
        tabList: [
          {
            title: '全部',
            key: '0'
          },
          {
            title: '处理中',
            key: '1'
          },
          {
            title: '已处理',
            key: '2'
          }
        ],
        orderList: [
        ],
        orderData: {},
        orderStatus: -1,
        total:0,
        page: 1,
        limit: 10
      }
    },
    fetch({ store }) {
      store.commit("isBanner", false);
      store.commit("isHeader", true);
      store.commit("isFooter", true);
    },
    head() {
      return {
        title: "售后-"+this.$store.state.titleCon
      }
    },
    beforeMount(){
    },
    mounted() {
      Promise.all([this.getRefundList()])
    },
    methods: {
      getRefundTotal(refund){
        return refund.refundProduct.reduce((i,pro)=>{
          return i + pro.refund_num;
        }, 0)
      },
      // 选项卡
      bindTab(item){
        this.tabCur = item.key
        this.page = 1
        this.orderList = []
        this.getRefundList()
      },
      /**退款订单 */
      getRefundList(){
        this.$axios.get('/api/refund/list',{
          params:{
            type: this.tabCur,
            page:this.page,
            limit:this.limit
          }
        }).then(res => {
          this.orderList = res.data.list
          this.total = res.data.count
        })
      },
      // 退回商品
      goPage(id){
		    this.$router.push({
          path: `/refund_goods`,
          query:{
            id:id
          }
        })
	    },
      // 查看详情
      goDetail(item){
        this.$router.push({
          path: `/refund_detail`,
          query:{
            id:item.refund_order_id
          }
        })
      },
      // 再次申请
      applyAgain(item){
        this.$router.push({
          path: `/order_detail`,
          query:{
            orderId:item.refundProduct[0].product.order_id
          }
        })
      },
      // 删除记录
      bindDetele(item, index){
        let that = this;
        MessageBox.confirm("确定删除该记录吗？", "提示").then(res => {
          that.$axios.post("/api/refund/del/"+item.refund_order_id)
          .then(data => {
            that.orderList.splice(index,1);
            return Message.success("删除成功");
          });
        });
      },
      //店铺首页
      goStore(item){

      },
      // 分页点击
      bindPageCur(data){
        this.page = data
        this.getRefundList();
      }
    }
  }
</script>
<style lang="scss" scoped>
  .user-com-tab {
    .item {
      padding: 0 10px;
    }
  }
  .user-order-list {
    li {
      position: relative;
      padding: 30px 0 26px;
      border-bottom: 1px solid #ECECEC;
      .refund-icon{
        position: absolute;
        right: 30px;
        top: 30px;
        .iconfont{
          font-size: 72px;
          color: #818181;
          opacity: .6;
        }
        .red-color{
          color: #E93323;
          opacity: .6;
        }
      }
      .img-box {
        width: 120px;
        height: 120px;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .bd {
        padding-right: 40px;
        border-bottom: 1px dashed #E1E1E1;
        cursor: pointer;
        .order-txt {
          color: #282828;
          font-size: 14px;
          .status {
            float: right;
            color: #E93323;
          }
        }
        .content {
          margin-top: 20px;
          .goods-item {
            display: flex;
            position: relative;
            margin-bottom: 20px;
            .info-txt {
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: center;
              width: 500px;
              margin-left: 24px;
              font-size: 14px;
              .info{
                font-size: 12px;
                color: #aaa;
                margin-top: 4px;
              }
              .price {
                margin-top: 15px;
                color: #E93323;
              }
              .num {
                position: absolute;
                right: 0;
                top: 60%;
                transform: translateY(-50%);
                color: #999999;
              }
            }
          }
        }
      }
      .foot {
        padding-top: 26px;
        padding-right: 32px;
        p {
          text-align: right;
          color: #666;
          font-size: 14px;
          span {
            color: #E93323;
          }
        }
        .btn-wrapper {
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
          div {
            width: 110px;
            height: 36px;
            text-align: center;
            line-height: 34px;
            margin-left: 20px;
            border: 1px solid #999999;
            border-radius: 4px;
            font-size: 14px;
            color: #666666;
            cursor: pointer;
            &.pay {
              border-color: #E93323;
              background: #E93323;
              color: #fff;
            }
          }
        }
      }
    }
  }
  .pages-box{
    margin-top: 30px;
    text-align: right;
  }
</style>
