// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const exportFileRouter  =
  {
    path: `${roterPre}/export`,
    name: 'exportFile',
    meta: {
      icon: '',
      title: leaveuKeyTerms['导出文件']
    },
    alwaysShow: true,
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'exportList',
        meta: {
          title: leaveuKeyTerms['导出文件']
        },
        component: () => import('@/views/exportFile/index')
      }
    ]
  }

export default exportFileRouter
