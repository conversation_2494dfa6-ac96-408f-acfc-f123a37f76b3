<template>
  <div class="order-detail wrapper_1200">
    <div class="header">
      <nuxt-link to="/">首页></nuxt-link>
      <nuxt-link :to="{path:'/user',query:{type:0}}">个人中心></nuxt-link>
      <span>订单详情</span>
    </div>
    <div v-if="orderData.orderList[0].activity_type != 2" class="section process">
      <div class="section-hd">
        订单状态：待付款
      </div>

      <div  class="section-bd">
        <ul class="">
          <li class="now">
            <div class="line"></div>
            <div class="iconfont icon-xuanzhong11"></div>
            <div class="iconfont icon-fukuan">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待付款</div>
              <div>{{ orderData.create_time }}</div>
            </div>
          </li>
          <li>
            <div class="line"></div>
            <div class="iconfont icon-weixuan"></div>
            <div class="iconfont icon-peihuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待发货</div>
            </div>
          </li>
          <li>
            <div class="line"></div>
            <div class="iconfont icon-weixuan"></div>
            <div class="iconfont icon-fahuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待收货</div>
            </div>
          </li>
          <li>
            <div class="line"></div>
            <div class="iconfont icon-weixuan"
            ></div>
            <div class="iconfont icon-pingjia1">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待评价</div>
            </div>
          </li>
          <li>
            <div class="iconfont icon-weixuan"
            ></div>
            <div class="iconfont icon-wancheng"></div>
            <div class="info">
              <div>已完成</div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div v-else class="section process">
      <div class="section-hd presell-hd">
        订单状态：{{orderData.orderList[0].orderProduct[0].cart_info.productPresell.presell_type==1?'待支付':'待付定金'}}
        <div>请在{{orderData.cancel_time}}前完成支付,超时订单将自动取消！</div>
      </div>

    </div>
    <div class="section">
      <div class="section-hd">收货信息</div>
      <div class="section-bd">
        <ul>
          <li class="acea-row row-middle">
            <div>收货人：</div>
            <div>{{ orderData.real_name }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>联系电话：</div>
            <div>{{ orderData.user_phone }}</div>
          </li>
          <li class="acea-row">
            <div>收货地址：</div>
            <div>{{ orderData.user_address }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section" v-if="orderData.orderList && orderData.orderList[0].order_extend && orderData.orderList[0].order_extend.length>0">
      <div class="section-hd">留言信息</div>
      <div class="section-bd">
        <ul v-for="(item,index) in orderData.orderList[0].order_extend" :key="index">
          <li v-for="(itm,idx) in item" :key="idx"  class='acea-row row-middle'>
            <div>{{idx}}：</div>
            <div v-if="!Array.isArray(itm)" class='conter'>{{itm}}</div>
            <div v-else class='conter virtual_image'>
              <img v-for="(pic,i) in itm" :key="i" class="picture" :src="pic"/>
            </div>
          </li>
         
        </ul>
      </div>
    </div>
     
    <div class="section">
      <div class="section-bd">
        <ul class="goods">
          <li
            v-for="(item,index) in orderData.orderList"
            :key="index"
          >
           <div v-if="item.merchant" class="section-hd" style="padding: 0 22px 18px;">
            <span class="mer_name">{{item.merchant.mer_name}}</span>
            <div class="contactService" @click="chatShow(item.merchant.mer_id)" v-if="item.merchant.services_type && item.merchant.services_type.services_type == 1">联系客服 <span class="iconfont icon-lianxikefu"></span>
            </div>
            <div class="contactService" v-else-if="item.merchant.services_type && item.merchant.services_type.services_type == 2 && item.merchant.services_type.service_phone">
              <el-tooltip popper-class="tps" effect="dark" :content="'客服电话：'+item.merchant.services_type.service_phone" placement="right">       
                <div>联系客服 <span class="iconfont icon-lianxikefu"></span></div>
              </el-tooltip> 
            </div>
            <a class="contactService" :href="item.merchant.services_type.mer_customer_link" target="blank" v-else-if="item.merchant.services_type && item.merchant.services_type.services_type == 4">联系客服 <span class="iconfont icon-lianxikefu"></span>
            </a>
           </div>
           <div v-for="(goods,j) in item.orderProduct" :key="j">
              <div class="acea-row row-middle" style="width: 100%; padding: 22px;">
                <div class="image">
                  <img v-if="goods.cart_info && goods.cart_info.product" :src="goods.cart_info.product.image" />
                </div>
                <div>
                <div v-if="goods.cart_info && goods.cart_info.product">{{ goods.cart_info.product.store_name }}</div>
                <div class="info" v-if="goods.cart_info && goods.cart_info.productAttr">
                  {{ goods.cart_info.productAttr.sku }}
                </div>
                <div>
                  <span class="money" v-if="goods.cart_info && goods.cart_info.productAttr">￥{{ goods.cart_info.productAttr.price }}</span>
                  <span>x{{ goods.product_num }}</span>
                </div>
                </div>
              </div>
              <div v-if="item.activity_type === 2 && goods.cart_info.productPresell.presell_type !=1" class="presell_process">
                <div class="process_count">
                  <div class="acea-row row-between">
                    <div> 阶段1：等待买家付款</div>
                    <div class="font-color">￥{{ item.pay_price }}</div>
                  </div>
                  <div class="acea-row row-between mt10">
                    <div>阶段2：未开始</div>
                    <div class="font-color" style="margin-top: 0;"> ￥{{ item.presellOrder.pay_price }}</div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
        <ul>
          <li class="acea-row row-middle">
            <div>运费：</div>
            <div>
              {{ orderData.pay_postage <= 0 ? "包邮" : orderData.pay_postage }}
            </div>
          </li>
          <li class="acea-row row-middle" v-if="orderData.postage_price">
            <div>运费折扣：</div>
            <div>
              {{ orderData.postage_price }}
            </div>
          </li>
          <li class="acea-row row-middle" v-if="orderData.member_price">
            <div>会员折扣：</div>
            <div>
              {{ orderData.member_price }}
            </div>
          </li>
          <li  v-if='orderData.coupon_price>0' class="acea-row row-middle coupon">
            <div>优惠券抵扣：</div>
            <div>
              <span>-￥{{ orderData.coupon_price }}</span>
            </div>
          </li>
          <li
            class="acea-row row-middle"
            v-if="parseFloat(orderData.deduction_price) > 0"
          >
            <div>积分抵扣：</div>
            <div>-￥{{ orderData.deduction_price }}</div>
          </li>
        </ul>
        <ul class="total">
          <li class="acea-row row-middle row-between">
            <div>
              共{{ orderData.total_num }}件商品，订单总金额为：<span class="money"
                >￥<b>{{ orderData.pay_price }}</b></span
              >
            </div>
            <div class="footerState acea-row row-middle">
              <div class="orderBnt" @click.stop="cancelOrder">取消订单</div>
              <div class="orderBnt on" @click="goPay">立即付款</div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <chat-room
      v-if="chatPopShow"
      :chatId="mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message, MessageBox} from "element-ui";
import ChatRoom from "@/components/ChatRoom";

export default {
  auth: "guest",
  components: { ChatRoom },
  data() {
    return {
      chatPopShow: false,
      mer_id: 0,
      orderData: {},
    };
  },
  async asyncData({ app, query }) {
    let [orderData]  = await Promise.all([
        app.$axios.get('/api/order/group_order_detail/'+query.orderId),
    ]);
    return {
      id: query.orderId,
      orderData: orderData.data,
      // mer_service: orderData.data.merchant.services_type,
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "订单详情-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
  },
  mounted() {
      this.getOrderInfo()
  },
  methods: {
    chatShow(mer_id) {
      if(this.$auth.loggedIn){
        this.mer_id = mer_id;
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    getOrderInfo() {
      this.$axios.get(`/api/order/group_order_detail/`+this.id).then(res => {
        this.orderData = res.data;
    });
    },
    // 取消订单
    cancelOrder() {
      let that = this;
      MessageBox.confirm("确定取消该订单吗？", "提示").then(res => {
        that.$axios
          .post("/api/order/cancel/"+that.orderData.group_order_id)
          .then(data => {
            Message.success(data.message);
            that.$router.replace({path:'/user/order_list',query:{type:1}});
          });
      });
    },
    //去支付
    goPay() {
      this.$router.push({
        path: "/payment",
        query: { result: this.orderData.group_order_id }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.order-detail {
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    background-color: unset;
    .home {
      color: #282828;
    }
  }
  > div {
    background-color: #ffffff;
    &.order-number {
      li {
        div {
          &:nth-child(2) {
            flex: none;
          }
        }
        a {
          margin-left: 30px;
          font-size: 16px;
          color: #236fe9;
          .iconfont {
            font-size: 12px;
          }
        }
      }
    }
    ~ div {
      margin-top: 14px;
    }
    > div {
      ~ div {
        border-top: 1px dashed #cecece;
      }
    }
    &.process {
      margin-top: 0;
      div {
        border-top: none;
        &.section-hd {
          padding: 26px 22px 0;
        }
        &.presell-hd {
          padding: 26px 22px 32px;
          div{
            margin-top: 20px;
          }
        }
        ul {
          padding: 27px 0 94px;
          &::after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
          }
        }
        li {
          position: relative;
          float: left;
          margin-top: 0;
          margin-left: 222px;
          &:first-child {
            margin-left: 111px;
          }
          .line {
            position: absolute;
            top: 50%;
            left: 16px;
            width: 226px;
            height: 4px;
            background: #c7c7c7;
            transform: translateY(-50%);
          }
          .iconfont {
            position: relative;
            width: auto;
            font-size: 18px;
            line-height: 1;
            color: #c7c7c7;
            + .iconfont {
              position: absolute;
              top: 50%;
              left: 50%;
              display: none;
              width: 40px;
              height: 40px;
              border: 4px solid #e93323;
              border-radius: 50%;
              background: #ffffff;
              transform: translate(-50%, -50%);
              font-size: 20px;
              line-height: 32px;
              text-align: center;
              color: #e93323;
            }
          }
          .arrow {
            position: absolute;
            top: 50%;
            left: 100%;
            display: none;
            width: 80px;
            height: 16px;
            background: #e93323;
            transform: translateY(-50%);
            &::after {
              content: "";
              position: absolute;
              top: 0;
              left: 100%;
              border-width: 8px;
              border-style: solid;
              border-color: transparent transparent transparent #e93323;
            }
          }
          .info {
            position: absolute;
            top: 42px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            text-align: center;
            color: #9a9a9a;
            width: 100px;
            div {
              &:first-child {
                margin-bottom: 4px;
                font-size: 16px;
                color: #282828;
              }
            }
          }
          &.past {
            .line {
              background: rgba(233, 51, 35, 0.6);
            }
            .iconfont {
              color: #e93323;
            }
          }
          &.now {
            .info {
              div {
                &:first-child {
                  color: #e93323;
                }
              }
            }
            .iconfont {
              + .iconfont {
                display: block;
              }
            }
            .arrow {
              display: block;
            }
          }
        }
      }
    }
    &.reject {
      position: relative;
      padding: 30px 22px;
      background: #666666;
      overflow: hidden;
      margin-top: 0;
      .iconfont {
        position: absolute;
        top: -20px;
        right: 28px;
        font-size: 112px;
        color: #818181;
      }
      div {
        border-top: none;

        &.section-hd {
          padding: 0;
          font-weight: bold;
          color: #ffffff;
        }

        ul {
          padding: 0;
          margin-top: 8px;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }

    &.reason {
      padding: 26px 22px;
      div {
        border-top: none;
        &.section-hd {
          padding: 0;
          .iconfont {
            margin-right: 8px;
          }
        }
        ul {
          padding: 0;
          margin-top: 15px;
          color: #7e7e7e;
        }
      }
    }
  }
  .virtual_image{
    display: flex;
    align-items: center;
    img{
      width: 70px;
      height: 70px;
      border-radius: 4px;
      margin-right: 5px;
    }
  }
  .section-hd {
        padding: 22px 18px;
        font-size: 18px;
        color: #282828;
        position: relative;
    ~ div {
        border-top: 1px dashed #cecece;
      }
      .contactService{
        color: #E93323;
        font-size: 18px;
        position: absolute;
        right: 22px;
        top: 0;
        cursor: pointer;
        .qrcode{
          box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
          background: #fff;
          position: absolute;
          right: 0;
          top: 30px;
          z-index: 10;
          display: none;
          padding: 6px;
          margin-top: 0px !important;
          img{
            width: 100%;
            display: block !important;
          }
        }
        &:hover{
            .qrcode{
                display: inline;
            }
        }
        .iconfont{
            font-size: 20px;
        }
    }
  }

  .section-bd {
    ul {
      padding: 22px;
      font-size: 16px;
      color: #282828;
     &.goods{
         padding: 22px 0;
     }
      ~ ul {
        border-top: 1px dashed #cecece;
      }
    }

    li {
      .time {
        margin-left: 10px;
      }
      ~ li {
        margin-top: 20px;
      }

      &.coupon {
        span {
          ~ span {
            margin-left: 18px;
          }
        }
      }
    }

    .money {
      color: #e93323;

      b {
        font-weight: normal;
        font-size: 22px;
      }
    }

    .goods {
      .info {
        font-size: 12px;
        color: #aaa;
        margin-top: 4px;
      }

      li {
        position: relative;
        ~ li {
          margin-top: 22px;
        }

         div {
          .image {
            width: 86px;
            height: 86px;
            overflow: hidden;
            margin-right: 15px;
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
          }

          &:nth-child(2) {
            > div {
              &:first-child {
                overflow: hidden;
              }

              &:last-child {
                margin-top: 10px;

                del {
                  margin-left: 12px;
                  font-size: 14px;
                  color: #919191;
                }
              }
            }
          }

          &:nth-child(3) {
            font-size: 14px;
            color: #b1b1b1;
          }
        }
      }
    }

    .total {
      padding: 28px 22px;

      .footerState {
        cursor: pointer;
      }

      .service {
        width: 114px;
        height: 40px;
        margin-left: 18px;
        background: #e93323;
        color: #fff;
        font-size: 16px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
      }

      div {
        &:first-child {
          width: auto;
        }

        &:last-child {
          flex: none;

          div {
            padding-right: 30px;
            padding-left: 30px;

            ~ div {
              border-left: 1px solid #cecece;
            }
          }

          .orderBnt {
            width: 114px;
            height: 40px;
            padding: 0;
            border: 1px solid #999999;
            border-radius: 2px;
            background: none;
            outline: none;
            font-size: 16px;
            line-height: 40px;
            text-align: center;
            color: #282828;
            ~ .orderBnt {
              margin-left: 18px;
            }
            &.on {
              border-color: #e93323;
              background: #e93323;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
.presell_process{
  padding: 20px 22px 0;
  .process_count{
    background: #FFF8F7;
    padding: 21px 20px;
    >div{
      color: #282828;
      width: 60%;
    }
  }
  .mt10{
    margin-top: 10px;
  }

}
.evaluate_btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
</style>
