<template>
  <div class="login-count">
    <!-- 登录弹窗 -->
    <div class="wrapper-count">
      <span class="closeBtn iconfont icon-guanbi2" @click="closeLogin"></span>
      <div class="wrapper" v-show="current === 1">
        <div class="title"><span class="item_title" @click="current = 2">账号登录</span><span class="font_red item_title">快速登录</span>
        </div>
        <div class="iconfont icon-erweima2" @click="current = 3"></div>
        <div class="item phone acea-row row-middle">
          <div class="number">+86</div>
          <input type="text" placeholder="请输入手机号" v-model="account" autocomplete="new-password">
        </div>
        <div class="item verificat acea-row row-between-wrapper">
          <input type="text" autocomplete="new-password" placeholder="请输入验证码" v-model="captcha">
          <button class="code font-color" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="getVerify('login')">
            {{ text }}
          </button>
        </div>
        <div v-if="isShowCode" class="item verificat acea-row row-between-wrapper">
          <input type="text" placeholder="填写验证码" v-model="codeVal" autocomplete="new-password">
          <div class="code" @click="again"><img :src="codeUrl"/></div>
        </div>
        <div class="checkbox-wrapper item_protocol">
          <label class="well-check">
            <input
              type="checkbox"
              name=""
              value=""
              :checked="isAgree"
              @click="isAgree = !isAgree"
            />
            <i class="icon"></i>
            <span>我已阅读并同意</span>
          </label>
          <nuxt-link
             :to="{path:`/privacy_agreement`,query:{type:'sys_user_agree'}}"
            target="_blank"
            class="show_protocol"
          >《用户协议》
          </nuxt-link>
          与
          <nuxt-link
            :to="{path:`/privacy_agreement`,query:{type:'sys_userr_privacy'}}"
            target="_blank"
            class="show_protocol"
          >《隐私政策》
          </nuxt-link>
        </div>
        <div class="signIn bg-color" @click="loginMobile">登录</div>
        <div class="fastLogin">没有账号？ <span class="font-color" @click="current = 5">立即注册</span></div>
      </div>
      <div class="wrapper" v-show="current === 2">
        <div class="title"><span class="item_title font_red" @click="current = 2">账号登录</span><span class="item_title" @click="current = 1">快速登录</span>
        </div>
        <div class="iconfont icon-erweima2" @click="current = 3"></div>
        <div class="item phone acea-row row-middle">
          <!-- <div class="number">+86</div> -->
          <input type="text" placeholder="请输入账号" v-model="account" autocomplete="new-password">
        </div>
        <div class="item pwd">
          <input type="password" placeholder="请输入密码" v-model="password" autocomplete="new-password">
        </div>
        <div class="checkbox-wrapper item_protocol">
          <label class="well-check">
            <input
              type="checkbox"
              name=""
              value=""
              :checked="isAgree"
              @click="isAgree = !isAgree"
            />
            <i class="icon"></i>
            <span>我已阅读并同意</span>
          </label>
          <nuxt-link
            :to="{path:`/privacy_agreement`,query:{type:'sys_user_agree'}}"
            target="_blank"
            class="show_protocol"
          >《用户协议》
          </nuxt-link>
          与
          <nuxt-link
            :to="{path:`/privacy_agreement`,query:{type:'sys_userr_privacy'}}"
            target="_blank"
            class="show_protocol"
          >《隐私政策》
          </nuxt-link>
          <div class="forget_password" @click="current = 4"><span class="iconfont-h5 icon-icon_question_2"></span>忘记密码</div>
        </div>
        <div class="signIn bg-color" @click="loginH5">登录</div>
        <div class="fastLogin">没有账号？ <span class="font-color" @click="current = 5">立即注册</span></div>
      </div>
      <div class="wxLogin wrapper" v-if="current === 3">
        <div class="inner">扫码登录</div>
        <div class="iconfont icon-zhanghaodenglu1" @click="current = 1"></div>
        <div class="wxCode">
          <span class="iconfont icon-erweimabianjiao"></span>
          <span class="iconfont icon-erweimabianjiao"></span>
          <span class="iconfont icon-erweimabianjiao"></span>
          <span class="iconfont icon-erweimabianjiao"></span>
          <img v-if="qrCode" :src="qrCode">
        </div>
        <div class="tip">请使用微信扫一扫登录</div>
      </div>
      <!--忘记密码-->
      <div class="wrapper" v-show="current === 4">
        <div class="title">忘记密码</div>
        <div class="item phone acea-row row-middle">
          <div class="number">+86</div>
          <input type="text" placeholder="请输入手机号" v-model="account">
        </div>
        <div class="item pwd">
          <input type="password" placeholder="填写您的新密码" v-model="password" autocomplete="new-password">
        </div>
        <div class="item pwd">
          <input type="password" placeholder="再次输入新密码" v-model="confirm_pwd" autocomplete="new-password">
        </div>
        <div class="item verificat acea-row row-between-wrapper">
          <input type="text" autocomplete="new-password" placeholder="请输入验证码" v-model="captcha">
          <button class="code font-color" :disabled="disabled" :class="disabled === true ? 'on' : ''"
                 @click="getVerify('change_pwd')">
            {{ text }}
          </button>
        </div>
        <div v-if="isShowCode" class="item verificat acea-row row-between-wrapper">
          <input type="text" placeholder="填写验证码" v-model="codeVal" autocomplete="new-password">
          <div class="code" @click="again"><img :src="codeUrl"/></div>
        </div>
        <div class="signIn bg-color" @click="registerReset">确认</div>
        <div class="fastLogin font-color" @click="current = 2">立即登录</div>
      </div>
      <!--注册-->
      <div class="wrapper" v-show="current === 5">
        <div class="title">注册账号</div>
        <div class="item phone acea-row row-middle">
          <div class="number">+86</div>
          <input type="text" placeholder="请输入手机号" v-model="account" autocomplete="new-password">
        </div>
        <div class="item verificat acea-row row-between-wrapper">
          <input type="text" autocomplete="new-password" placeholder="请输入验证码" v-model="captcha">
          <button class="code font-color" :disabled="disabled" :class="disabled === true ? 'on' : ''"
              @click="getVerify('login')">
            {{ text }}
          </button>
        </div>
        <div class="item pwd">
          <input type="password" placeholder="请输入您的登录密码" v-model="password" autocomplete="new-password">
        </div>
        <div v-if="isShowCode" class="item verificat acea-row row-between-wrapper">
          <input type="text" placeholder="填写验证码" v-model="codeVal">
          <div class="code" @click="again"><img :src="codeUrl"/></div>
        </div>
        <div class="checkbox-wrapper item_protocol">
          <label class="well-check">
            <input
              type="checkbox"
              name=""
              value=""
              :checked="isAgree"
              @click="isAgree = !isAgree"
            />
            <i class="icon"></i>
            <span>我已阅读并同意</span>
          </label>
          <nuxt-link
            :to="{path:`/privacy_agreement`,query:{type:'sys_user_agree'}}"
            target="_blank"
            class="show_protocol"
          >《用户协议》
          </nuxt-link>
          与
          <nuxt-link
            :to="{path:`/privacy_agreement`,query:{type:'sys_userr_privacy'}}"
            target="_blank"
            class="show_protocol"
          >《隐私政策》
          </nuxt-link>
        </div>
        <div class="signIn bg-color" @click="register">注册</div>
        <div class="fastLogin">已有账号？ <span class="font-color" @click="current = 2">立即登录</span></div>
      </div>
    </div>
    <Verify @success="success" captchaType="clickWord" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import sendVerifyCode from "@/mixins/SendVerifyCode";
import Verify from '@/components/verifition/Verify'
export default {
  name: "loginStatus",
  components: {Verify},
  mixins: [sendVerifyCode],
  data() {
    return {
      current: 1,
      account: "",
      password: "",
      confirm_pwd: "",
      captcha: "",
      keyCode: "",
      qrCode: '',
      isShow: true,
      disabled: false,
      codeCheck: null,
      isAgree: false,
      isShowCode: false,
      codeVal: "",
      codeUrl: "",
      codeKey: "",
      currentType: ''
    }
  },
  watch: {
    current(n) {
      if (n === 3) {
        this.loginCode();
      } else if (n === 4) {
        this.account = '';
        this.password = '';
        this.confirm_pwd = '';
        this.captcha = '';
      } else {
        this.clearCodeCheck();
      }
    }
  },
  head() {
    return {}
  },
  mounted() {
    window.addEventListener('keydown', this.keyDown);
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false);
    this.clearCodeCheck();
  },
  methods: {
    keyDown(e) {
      if (e.keyCode === 13) {
        if (this.current === 1) {
          this.loginMobile();
        } else if (this.current === 2) {
          this.loginH5();
        } else if (this.current === 3) {
          this.loginCode();
        }
      }
    },
    clearCodeCheck() {
      this.codeCheck && clearInterval(this.codeCheck);
      this.codeCheck = null;
    },
    async loginCode() {
      const res = await this.$axios.get('/api/pc/login/scan');
      this.keyCode = res.data.key;
      this.qrCode = res.data.qrcode;
      this.codeCheck = setInterval(() => {
        this.$axios.post('/api/pc/login/scan/check', {key: this.keyCode}).then(res => {
          if (res.data.status === 200) {
            this.$auth.setUserToken(res.data.result.token);
            this.$auth.setUser(res.data.result.user);
            this.isShow = false;
            this.closeLogin();
            location.reload()
            this.clearCodeCheck();
          } else if (res.data.status === 400) {
            this.clearCodeCheck();
          }
        })
      }, 2000);
    },
    async loginH5() {
      let that = this;
      if (!that.account) return that.$message.error('请填写账号');
      // if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
      if (!that.password) return that.$message.error("请填写密码");
      if (!that.isAgree) return that.$message.error("请勾选用户隐私协议");
      let userInfo = {
        account: that.account,
        password: that.password,
        user_type: 'pc'
      };
      await that.$auth.loginWith('local1', {data: userInfo}).then(() => {
        that.isShow = false;
        this.closeLogin();
        location.reload()
      }).catch(err => {
        that.$message.error(err);
      })
    },
    async loginMobile() {
      let that = this;
      if (!that.account) return that.$message.error("请填写手机号码");
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
      if (!that.captcha) return that.$message.error("请填写验证码");
      if (!/^[\w\d]+$/i.test(that.captcha)) return that.$message.error("请输入正确的验证码");
      if (!that.isAgree) return that.$message.error("请勾选用户隐私协议");
      let userInfo = {
        phone: that.account,
        sms_code: that.captcha,
        user_type: 'pc'
      };
      await that.$auth.loginWith('local2', {data: userInfo}).then(() => {
        that.isShow = false;
        this.closeLogin();
        location.reload()
      })
        .catch(err => {
          that.$message.error('验证码错误');
        })
    },
    async register() {
      let that = this;
      if (!that.account) return that.$message.error("请输入手机号码");
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
      if (!that.captcha) return that.$message.error("请输入验证码");
      if (!/^[\w\d]+$/i.test(that.captcha)) return that.$message.error("请输入正确的验证码");
      if (!that.password) return that.$message.error("请输入密码");
      if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/i.test(that.password)) return that.$message.error("请输入6-16位字母加数字组合");
      if (!that.isAgree) return that.$message.error("请勾选用户隐私协议");
      await this.$axios.post("/api/auth/register", {
        phone: that.account,
        sms_code: that.captcha,
        pwd: that.password,
        user_type: 'pc'
      }).then(res => {
        that.$message.success('注册成功');
        that.$store.commit("isLogin", false);
        that.current = 2;
      }).catch(err => {
        that.$message.error(err);
      });
    },
    //忘记密码
    async registerReset() {
      let that = this;
      if (!that.account) return that.$message.error("请填写手机号码");
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
      if (!that.password) return that.$message.error("请填写密码");
      if (that.password != that.confirm_pwd) return that.$message.error("两次密码不一致");
      if (!that.captcha) return that.$message.error("请填写验证码");
      if (!/^[\w\d]+$/i.test(that.captcha)) return that.$message.error("请输入正确的验证码");
      await this.$axios.post("/api/user/change_pwd", {
        phone: that.account,
        sms_code: that.captcha,
        pwd: that.password,
        confirm_pwd: that.confirm_pwd
      }).then(res => {
        that.$message.success(res.message);
        that.sendCode();
        that.current = 2;
      }).catch(err => {
        that.$message.error(err);
      });
    },
    again() {
      this.getcaptcha()
    },
    getcaptcha() {
      let that = this
      that.$axios.get("/api/captcha").then(res => {
        that.codeUrl = res.data.captcha;
        that.codeVal = res.data.code; //图片验证码
        that.codeKey = res.data.key //图片验证码key
      })
      that.isShowCode = true;
    },
    async code(type, data) {
      let that = this;
      if (!that.account) return that.$message.error('请填写手机号码');
      // if (!that.isAgree) return that.$message.error("请勾选用户隐私协议");
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error('请输入正确的手机号码');
      await this.$axios.post("/api/auth/verify", {
        phone: that.account,
        type: type,
        // key: that.codeKey,
        // code: that.codeVal,
        captchaType: "clickWord",
        captchaVerification: data.captchaVerification
      }).then(res => {
        that.$message.success(res.message);
        that.sendCode();
      }).catch(err => {
        that.$message.error(err);
        // that.getcaptcha();
      });
    },
    closeLogin() {
      this.$store.commit("isLogin", false);
    },
    getVerify(type) {
      let that = this;
      if (!that.account) return that.$message.error('请填写手机号码');
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error('请输入正确的手机号码');
      if (!that.isAgree) return that.$message.error("请勾选用户隐私协议");
      this.currentType = type;
      this.$refs.verify.show();
    },
    success(params) {
      this.closeModel(params);
    },
    // 关闭模态框
    closeModel(params) {
      this.isShow = false;
      this.code(this.currentType, params);
    },
  }
}
</script>

<style scoped lang="scss">
.login-count {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}
.wrapper-count {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 50px;
  .closeBtn {
    color: #fff;
    position: absolute;
    top: 0;
    right: 0;
    text-align: center;
    font-size: 40px;
    cursor: pointer;
  }
}
.wrapper {
  position: relative;
  width: 450px;
  min-height: 426px;
  background-color: #fff;
  text-align: center;
  padding: 50px 0;
  margin: 0 auto;
  .font_red {
    color: #E93323
  }
  .title {
    font-size: 20px;
    font-weight: 400;
    position: relative;
    .item_title {
      cursor: pointer;
      &:first-child {
        margin-right: 70px;
      }
    }
    .iconfont {
      position: absolute;
      top: -71px;
      right: 0;
      font-size: 60px;
      cursor: pointer;
    }
  }
  .item {
    width: 358px;
    height: 50px;
    border: 1px solid #DBDBDB;
    margin: 0 auto;
    &.phone {
      margin-top: 34px;
      .number {
        width: 65px;
        height: 100%;
        line-height: 50px;
        color: #666666;
        border-right: 1px solid #DBDBDB;
      }
      input {
        width: 291px;
      }
    }
    &.pwd {
      margin-top: 20px;
      input {
        width: 100%;
      }
    }
    &.verificat {
      margin-top: 20px;
      input {
        width: 246px;
      }
      .code {
        width: 110px;
        height: 100%;
        border: 0;
        background-color: #fff;
        border-left: 1px solid #DBDBDB;
        img {
          width: 100%;
          height: 100%;
        }
        &.on {
          color: #CCC !important;
        }
      }
    }
    input {
      padding-left: 15px;
      height: 100%;
      border: 0;
      outline: none;
    }
  }
  .signIn {
    width: 358px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    margin: 24px auto 0 auto;
    color: #fff;
    cursor: pointer;
  }
  .fastLogin {
    margin-top: 14px;
    cursor: pointer;
    color: #CCCCCC;
  }
  .title + .iconfont {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 46px;
    color: #282828;
  }
  &.wxLogin {
    position: relative;
    padding-top: 98px;
    .inner {
      position: absolute;
      top: 34px;
      left: 30px;
      font-size: 20px;
      color: #282828;
    }
    .icon-zhanghaodenglu1 {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 46px;
      color: #282828;
    }
  }
  .wxCode {
    position: relative;
    width: 213px;
    height: 213px;
    padding: 10px;
    margin: 0 auto;
    img {
      display: block;
      width: 100%;
    }
    .iconfont {
      font-size: 22px;
      color: #cbcbcb;
    }
    .iconfont:nth-child(1) {
      position: absolute;
      top: 0;
      left: 0;
    }
    .iconfont:nth-child(2) {
      position: absolute;
      top: 0;
      right: 0;
      transform: rotate(90deg);
    }
    .iconfont:nth-child(3) {
      position: absolute;
      right: 0;
      bottom: 0;
      transform: rotate(180deg);
    }
    .iconfont:nth-child(4) {
      position: absolute;
      bottom: 0;
      left: 0;
      transform: rotate(270deg);
    }
  }
  .tip {
    margin-top: 20px;
    font-size: 16px;
    color: #666;
  }
  .item_protocol {
    margin: 17px auto 0;
    width: 358px;
    text-align: left;
    padding-left: 22px;
    font-size: 12px;
    .icon {
      width: 14px;
      height: 14px;
    }
    .show_protocol {
      color: #E93323;
      cursor: pointer;
    }
    .forget_password {
      float: right;
      color: #999999;
      cursor: pointer;
      .icon-icon_question_2 {
        position: relative;
        top: 1px;
      }
    }
  }
}
</style>
