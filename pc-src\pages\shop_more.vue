<template>
    <div class="goods_cate">
      <category-List></category-List>

      <div class="wrapper_1200">
        <div class="title">
            <nuxt-link class="home" to="/">首页 > </nuxt-link>{{titleName}}
        </div>
        <div class="sort acea-row">
          <div class="name">排序：</div>
          <div class="acea-row">
            <div class="item" :class="iSdefaults === 0?'font-color':''" @click="defaults">默认</div>
            <div class="item" :class="iSdefaults === 1?'font-color':''" @click="salesSort">
              销量
              <span class="iconfont icon-shangjiashijian" :class="news?'font-color':''"></span>
            </div>
            <div class="item" :class="iSdefaults === 2?'font-color':''" @click="rateSort">
              好评
              <span class="iconfont icon-shangjiashijian" :class="news1?'font-color':''"></span>
            </div>
          </div>
        </div>
        <div class="store acea-row row-middle" v-if="storelist.length">
          <div class="item acea-row row-middle" v-for="(item, index) in storelist" :key="index">
            <!-- <div class="item acea-row row-middle"> -->
              <div class="store_main" :class="'street-bg' + index%6">
                  <div class="store_logo">
                      <img :src="item.mer_avatar" alt="">
                  </div>
                  <div class="store_name line1">
                      <span v-if="item.is_trader" class="trader">自营</span>
                      {{ item.mer_name }}
                  </div>
                  <div class="follow">{{ item.care_count < 10000 ? item.care_count : (item.care_count/10000).toFixed(1)+'万' }}人关注</div>
                  <nuxt-link :to="{path:'/store',query:{id:item.mer_id,type: 0}}" class="goStore">进店逛逛</nuxt-link>
              </div>
              <div v-if="item.recommend.length > 0" class="store_product acea-row row-middle">
                <div class="proList" v-for="(itemn, indexn) in item.recommend" :key="indexn" @click="goDetail(itemn)">
                    <span class="image">
                        <img :src="itemn.image" alt="">
                    </span>
                    <div class="name line1">{{ itemn.store_name }}</div>
                    <div class="price"><span>¥</span>{{ itemn.price }}</div>
                </div>
              </div>
            <div v-else class="store_product acea-row row-middle">
              <div class="no-recommend">
                <div class="pictrue">
                  <img src="../assets/images/noGoods.png">
                </div>
                <div class="name">亲，该暂无商品哟~</div>
              </div>
            </div>
          </div>
        </div>
        <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
      </div>
      <div class="noGoods" v-if="!storelist.length">
        <div class="pictrue">
          <img src="../assets/images/noGoods.png">
        </div>
        <div class="name">亲，暂无商户哟~</div>
      </div>

    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import categoryList from "@/components/categoryList";

export default {
      name: "goods_cate",
      auth: false,
      components: {
        categoryList
      },
      data(){
        return {
          categoryList:[],
          categoryCurrent:[],
          current:0,
          moreCurrent:0,
          seen:false,
          titleName:'更多店铺',
          erCurrent:0,
          iSdefaults:0,
          storelist:[],
          pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
          page: 1, //代表页面的初始页数
          limit:10,
          scollY: null,// 离底部距离有多少
          total: 0, //总页数
          title:'',
          cid:0,//一级分类
          sid:0,//二级分类
          order:'',
          news:0,
          news1: 0,
        }
      },
    async asyncData({ app, params, query }) {
        return {
          keyword:  query.title ? query.title : '',
        };
    },
      fetch({ store}) {
        store.commit('isHeader', true);
        store.commit('isFooter', true);
      },
      head() {
        return {
          title: "商品分类-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
        this.getStorelist();
      },
      mounted(){
        // this.pullRefresh();
      },
      beforeDestroy() {
        window.onscroll = null;
      },
      methods:{
        goDetail: function (item) {
          if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
          }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
          }
        },
        getStorelist(num){
          let _this = this,currentPage = {page: _this.page,limit: _this.limit,order: _this.order,keyword: _this.keyword};
          _this.$axios.get('/api/store/merchant/lst',{
              params:currentPage
            }).then(function (res) {
            _this.total = res.data.count;
            _this.storelist = res.data.list;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
       // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getStorelist()
        },
        defaults(){
          this.iSdefaults = 0;
          this.storelist = [];
          this.pullRefreshss = true;
          this.page = 1;
          this.news = 0;
          this.order = '';
          this.getStorelist();
        },
        salesSort(){
          this.iSdefaults = 1;
          this.storelist = [];
          this.page = 1;
          this.order = 'sales';
          this.news = 1;
          this.news1 = 0;
          this.getStorelist();
        },
        rateSort(){
          this.iSdefaults = 2;
          this.storelist = [];
          this.pullRefreshss = true;
          this.order = 'rate';
          this.page = 1;
          this.news = 0
          this.news1 = 1;
          this.getStorelist();
        }
      }
    }
</script>

<style scoped lang="scss">
  .goods_cate{
    .noGoods{
      text-align: center;
      .pictrue{
        width: 274px;
        height: 174px;
        margin: 130px auto 0 auto;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 14px;
        color: #969696;
        margin-top: 20px;
        margin-bottom: 290px;
      }
    }
    .title{
      color: #999999;
      height: 46px;
      line-height: 46px;
      .home{
        color: #282828;
      }
    }
    .store{
        margin-top: 20px;
        .item{
            width: 590px;
            margin-right: 20px;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-bottom: 30px;
            &:nth-child(2n){
                margin-right: 0;
            }
            .store_main{
                width: 180px;
                height: 224px;
                text-align: center;
                color: #fff;
                background-image:url('../assets/images/street-bg0.jpg');
                &.street-bg1{
                  background-image:url('../assets/images/street-bg1.jpg');
                }
                &.street-bg2{
                  background-image:url('../assets/images/street-bg2.jpg');
                }
                &.street-bg3{
                  background-image:url('../assets/images/street-bg3.jpg');
                }
                &.street-bg4{
                  background-image:url('../assets/images/street-bg4.jpg');
                }
                &.street-bg5{
                  background-image:url('../assets/images/street-bg5.jpg');
                }
                .store_logo{
                    display: inline-block;
                    margin-top: 35px;
                    width: 61px;
                    height: 61px;
                    border-radius: 100%;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .store_name{
                    margin: 10px 0;
                    font-weight: bold;
                    padding: 0 10px;
                    .trader{
                        display: inline-block;
                        font-family: 'PingFang SC';
                        font-size: 12px;
                        width: 32px;
                        height: 18px;
                        line-height: 18px;
                        background-color: #E93323;
                        font-weight: normal;
                        border-radius: 2px;
                    }
                }
                .follow{
                    opacity: .7;
                    margin-bottom: 12px;
                }
                .goStore{
                    display: inline-block;
                    font-size: 12px;
                    line-height: 24px;
                    border: 1px solid #fff;
                    padding: 0 9px;
                    color: #fff;
                    border-radius: 2px;
                }
            }
            .store_product{
                width: 410px;
                height: 224px;
                padding: 0 17px;
                background: #fff;
                text-align: center;
                cursor: pointer;
              .no-recommend{
                margin: 0 auto;
              }
                .proList{
                    width: 112px;
                    float: left;
                    margin-right: 20px;
                    &:nth-child(3n){
                        margin-right: 0;
                    }
                    .image{
                        width: 112px;
                        height: 112px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .name{
                        margin-top: 7px;
                        color: #5A5A5A;
                        line-height: 16px;
                    }
                    .price{
                        color: #E93323;
                        font-weight: bold;
                        margin-top: 4px;
                        span{
                            font-size: 10px;
                        }
                    }
                }
            }
        }
    }
  }
  .sort{
        .item{
          margin-right: 30px;
          cursor: pointer;
          &:hover{
            color: #E93323;
          }
          .icon{
            font-size: 15px;
            margin-left: 5px;
          }
          .iconfont{
            font-size: 15px;
            color: #E2E2E2;
            margin-left: 5px;
          }
        }
        .name{
            color: #969696;
            margin-right: 20px;
        }
    }
</style>
