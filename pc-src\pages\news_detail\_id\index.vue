<template>
  <div class="news_detail wrapper_1200">
    <div class="header">
      <span class="home"><nuxt-link class="home" to="/">首页  </nuxt-link> > <nuxt-link class="home" to="/news_list">资讯信息</nuxt-link> > </span>资讯详情
    </div>
    <div class="news_count">
      <div class="title">{{newsDetail.title}}</div>
      <div class="time">
        <div class="time-list"><span class="iconfont icon-shijian1"></span>  {{newsDetail.create_time}}</div>
      </div>
      <div class="content">
        <div
          v-if="newsDetail.content"
          class="detail-html"
          v-html="newsDetail.content.content"
        ></div>
      </div>
    </div>
  </div>

</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

export default {
  auth: false,
  data() {
    return {
      newsDetail: {},
      id: ""
    };
  },
  async asyncData({error,app,params}){
    try {
      let [newsDetail] = await Promise.all([
        app.$axios.get(`/api/article/detail/${params.id}`)
      ]);
      return {
        id: params.id,
        newsDetail: newsDetail.data
      }
    } catch (e) {
      console.log(e)
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },
  head() {
    return {
      title: this.newsDetail.title + '-' + this.$store.state.titleCon
    }
  },
  fetch({store}) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  mounted() {
  },
  methods: {
  }
};
</script>

<style lang="scss" scoped>
.header {
  margin-top: 21px;
  color: #999999;
  .home {
    color: #282828;
  }
}
.news_count{
  text-align: center;
  background: #ffffff;
  padding: 0 72px;
  margin-top: 20px;
  .title{
    color: #333333;
    font-size: 24px;
    padding-top: 44px;
  }
  .time{
    margin-top: 22px;
    color: #999999;
    font-size: 13px;
    .time-list{
      display: inline-block;
    }
  }
  .content{
    margin-top: 50px;
    color: #333333;
    text-align: left;
  }
}
.news_count .content ::v-deep p,
.news_count .content ::v-deep div{
  margin-top: 40px;
  line-height: 30px;
}
.news_count .content ::v-deep img{
  display: block;
  margin: 0 auto;
  max-width: 100%;
}
</style>
