<template>
  <div class="payment wrapper_1200">
    <div class="header">
      <span class="home"
        ><nuxt-link class="home" to="/">首页 </nuxt-link> > 个人中心 ></span
      >确认订单
    </div>
    <div class="orderTip">
      <div>订单提交成功！去付款咯~</div>
      <div class="times acea-row row-middle">
        <div>剩余时间：</div>
        <countDown
          v-if="orderDetail.cancel_unix"
          :is-day="false"
          :tip-text="' '"
          :day-text="' '"
          :hour-text="' 小时 '"
          :minute-text="' 分钟 '"
          :second-text="'秒 '"
          :datatime="orderDetail.cancel_unix"
        ></countDown>
      </div>
    </div>
    <div class="detail">
      <div class="item">
        订单编号：{{
          orderDetail.order_id
            ? orderDetail.order_sn
            : orderDetail.group_order_sn
        }}
      </div>
      <div class="item">
        订单价格：<span class="order_price">{{ orderDetail.pay_price }}元</span>
      </div>
      <div class="item line1">
        收货信息：{{ orderDetail.real_name }} {{ orderDetail.user_phone }}
        {{ orderDetail.user_address }}
      </div>
      <div v-if="type == 2" class="item line1">
        商品名称：{{
          orderDetail.orderProduct && orderDetail.orderProduct.length
            ? orderDetail.orderProduct[0].cart_info.product.store_name
            : ""
        }}
      </div>
      <div v-else class="item line1">
        商品名称：{{
          orderDetail.orderList && orderDetail.orderList.length
            ? orderDetail.orderList[0].orderProduct[0].cart_info.product
                .store_name
            : ""
        }}
      </div>
    </div>
    <div class="payType">
      <div class="title">选择以下支付方式</div>
      <div class="type acea-row row-middle">
        <div
          v-if="yue_status == 1"
          class="item acea-row row-center-wrapper "
          :class="current === 0 ? 'on' : ''"
          @click="currentPay(0)"
        >
          <div class="iconfont icon-yue"></div>
          <div>
            <div class="name">余额支付</div>
            <div class="yue">余额：{{ userInfo.now_money }}</div>
          </div>
          <div
            class="iconfont icon-xuanzhong4 font-color"
            v-if="current === 0"
          ></div>
        </div>
        <div
          v-if="wechat_status == 1"
          class="item acea-row row-center-wrapper"
          :class="current === 1 ? 'on' : ''"
          @click="currentPay(1)"
        >
          <div class="iconfont icon-weixinzhifu1"></div>
          <div>
            <div class="name">微信支付</div>
          </div>
          <div
            class="iconfont icon-xuanzhong4 font-color"
            v-if="current === 1"
          ></div>
        </div>
        <div
          v-if="alipay_status == 1"
          class="item acea-row row-center-wrapper"
          :class="current === 2 ? 'on' : ''"
          @click="currentPay(2)"
        >
          <div class="iconfont icon-zhifubao"></div>
          <div>
            <div class="name">支付宝支付</div>
          </div>
          <div
            class="iconfont icon-xuanzhong4 font-color"
            v-if="current === 2"
          ></div>
        </div>
        <div
          v-if="offline_switch && offline_switch != 0"
          class="item acea-row row-center-wrapper"
          :class="current === 3 ? 'on' : ''"
          @click="currentPay(3)"
        >
          <div class="iconfont-h5 icon-a-ic_offlinepay"></div>
          <div>
            <div class="name">线下支付</div>
          </div>
          <div
            class="iconfont icon-xuanzhong4 font-color"
            v-if="current === 3"
          ></div>
        </div>
      </div>
      <div class="goPay acea-row row-right">
        <button class="button" @click="goPay" :disabled="disabeld">
          去支付
        </button>
      </div>
    </div>
    <!--微信支付宝支付弹窗-->
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :center="true"
    >
      <div class="wxPay">
        <div class="wrapper">
          <div class="title acea-row row-between-wrapper">
            <div>{{ current == 1 ? "微信支付" : "支付宝支付" }}</div>
            <div class="amount">
              应付金额：<span class="num font-color">{{
                orderDetail.pay_price
              }}</span
              >元
            </div>
          </div>
          <div class="acea-row row-center-wrapper">
            <div class="wx">
              <div class="pictrue">
                <client-only>
                  <vue-qr class="bicode" :text="codeUrl" :size="310"></vue-qr>
                </client-only>
                <div v-if="failCode" class="fail-code" @click="goPay">
                  <div class="fail-code-info">
                    <img src="../assets/images/fail-code.png" />
                    <div>二维码失效，点击重新获取</div>
                  </div>
                </div>
              </div>
              <div class="text acea-row row-center-wrapper">
                <div class="iconfont icon-saoyisao"></div>
                <div>
                  <div v-if="current == 1">请使用微信扫一扫</div>
                  <div v-if="current == 2">请使用支付宝扫一扫</div>
                  <div class="tip">扫描二维码支付</div>
                </div>
              </div>
            </div>
            <div class="phone">
              <img v-if="current == 1" src="../assets/images/phone.png" />
              <img v-if="current == 2" src="../assets/images/aliqr.png" />
            </div>
          </div>
          <div class="selectPay" @click="handleClose">
            <span class="iconfont icon-fanhui"></span>选择其他支付方式
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
export default {
  name: "payment",
  auth: "guest",
  components: {
    countDown
  },
  data() {
    return {
      orderId: 0,
      orderDetail: {},
      current: 0,
      userInfo: {},
      payArr: ["balance", "weixinQr", "alipayQr", "offline"],
      payType: "balance",
      dialogVisible: false,
      totalPrice: 0,
      codeUrl: "",
      invalid: 0,
      endTime: "",
      timeName: null,
      failCode: false,
      failCodeStatus: null,
      yue_status: 0,
      alipay_status: 0,
      wechat_status: 0,
      offline_switch: 0,
    };
  },
  async asyncData({ app, query }) {
    let [userInfoMsg] = await Promise.all([app.$axios.get("/api/user")]);
    return {
      orderId: query.result,
      type: query.type,
      userInfo: userInfoMsg.data,
      disabeld: false
    };
  },
  watch: {
    endTime(n) {
      if (n === 1) {
        this.clearStatus();
      }
    }
  },
  fetch({ store }) {
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "支付-" + this.$store.state.titleCon
    };
  },
  beforeMount() {},
  mounted() {
    this.getOrderInfo();
    this.getPayType();
  },
  beforeDestroy() {
    this.clearStatus();
    this.clearFailCodeStatus();
  },
  methods: {
    //获取支付方式状态
    getPayType() {
      let that = this;
      that.$axios
        .get("/api/config")
        .then(res => {
          that.alipay_status = res.data.alipay_open;
          that.yue_status = res.data.yue_pay_status;
          that.wechat_status = res.data.pay_weixin_open;
          that.offline_switch = res.data.offline_switch;
          if (res.data.yue_pay_status == 0) that.currentPay(1);
        })
        .catch(err => {
          that.$message.error(err);
        });
    },
    clearStatus() {
      if (this.timeName) {
        clearInterval(this.timeName);
      }
      this.timeName = null;
    },
    clearFailCodeStatus() {
      if (this.failCodeStatus) {
        clearTimeout(this.failCodeStatus);
        this.failCodeStatus = null;
      }
    },
    currentPay(index) {
      let that = this;
      that.current = index;
      that.payType = that.payArr[index];
    },
    getOrderInfo() {
      let that = this;
      if (that.type == 2) {
        that.$axios
          .get("/api/order/detail/" + that.orderId)
          .then(res => {
            that.orderDetail = res.data;
          })
          .catch(err => {
            that.$message.error(err);
          });
      } else {
        that.$axios
          .get("/api/order/group_order_detail/" + that.orderId)
          .then(res => {
            that.orderDetail = res.data;
          })
          .catch(err => {
            that.$message.error(err);
          });
      }
    },
    goPay() {
      let that = this;
      this.disabeld = true;
      this.clearFailCodeStatus();
      this.clearStatus();
      if (
        this.current === 0 &&
        parseFloat(that.userInfo.now_money) <
          parseFloat(that.orderDetail.pay_price)
      )
        return that.$message.error("余额不足");
      if (this.current === 0) {
        that.$axios
          .post("/api/order/pay/" + that.orderId, {
            type: that.payType,
            return_url:
              window.location.protocol +
              "//" +
              window.location.host +
              "/user/order_list?type=1"
          })
          .then(res => {
            if (res.data.status === "error") {
              that.$message.error(res.message);
              return;
            }
            that.$message.success(res.message);
            that.gainCount();
            setTimeout(function() {
              that.$router.replace({ path: "/user/order_list?type=1" });
            }, 1000);
          })
          .catch(err => {
            that.$message.error(err);
            setTimeout(function() {
              that.$router.push({ path: "/" });
            }, 1000);
          });
      } else {
        that.$axios
          .post("/api/order/pay/" + that.orderId, {
            type: that.payType,
            return_url:
              window.location.protocol +
              "//" +
              window.location.host +
              "/user/order_list?type=1"
          })
          .then(res => {
            if (res.data.status === "success" && that.payType === "offline") {
              that.$message.success("订单创建成功，支付失败，线下支付请告知收银员！");
              setTimeout(() => {
                that.$router.replace({ path: "/user/order_list?type=0" });
              }, 500);
              return;
            }
            if (res.data.status === "error") {
              that.$message.error(res.message);
              return;
            }
            this.failCode = false;
            this.dialogVisible = true;
            this.codeUrl = res.data.result.config;
            this.failCodeStatus = setTimeout(() => {
              this.failCode = true;
            }, 1000 * 60 * 3);

            if (!this.timeName) {
              this.timeName = setInterval(() => {
                this.wxSuccess();
              }, 2000);
            }
          })
          .catch(err => {
            that.$message.error(err);
            setTimeout(function() {
              that.$router.push({ path: "/" });
            }, 1000);
          });
      }
    },
    gainCount: function() {
      let that = this;
      that.$axios.get("/api/user/cart/count").then(res => {
        that.$store.commit("cartNum", res.data[0].count || 0);
      });
    },
    wxSuccess() {
      let that = this;
      that.$axios
        .get("/api/order/status/" + that.orderId)
        .then(res => {
          that.endTime = res.data.paid;
          if (res.data.paid) {
            this.clearStatus();
            this.clearFailCodeStatus();
            that.$message.success("支付成功");
            that.gainCount();
            setTimeout(function() {
              that.$router.replace({ path: "/user/order_list?type=1" });
            }, 1000);
          }
        })
        .catch(err => {
          that.$message.error(err);
          setTimeout(function() {
            that.$router.replace({ path: "/" });
          }, 1000);
        });
    },
    handleClose() {
      this.dialogVisible = false;
      this.disabeld = false;
      this.clearFailCodeStatus();
      this.clearStatus();
    }
  }
};
</script>
<style scoped lang="scss">
.payment {
  .header {
    margin-top: 21px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .orderTip {
    padding: 55px 30px 31px 30px;
    font-size: 32px;
    color: #fff;
    width: 1200px;
    height: 159px;
    background: url("../assets/images/orderBg.png") no-repeat;
    background-size: 100% 100%;
    .times {
      font-size: 14px;
      margin-top: 12px;
    }
  }
  .detail {
    width: 100%;
    height: 182px;
    background-color: #fff;
    padding: 31px 30px;
    .item {
      font-size: 16px;
      .order_price {
        color: #e93323;
      }
      & ~ .item {
        margin-top: 12px;
      }
    }
  }
  .payType {
    width: 100%;
    height: 387px;
    background-color: #fff;
    margin-top: 17px;
    .title {
      height: 57px;
      padding: 0 30px;
      border-bottom: 1px dotted #cecece;
      line-height: 57px;
      font-size: 18px;
    }
    .type {
      padding: 22px 50px 55px 30px;
      .item {
        width: 210px;
        height: 86px;
        border: 1px solid #d4d4d4;
        position: relative;
        cursor: pointer;
        overflow: hidden;
        & ~ .item {
          margin-left: 20px;
        }
        &.on {
          border-color: #e93323;
        }
        .iconfont,.iconfont-h5 {
          color: #09bb07;
          font-size: 32px;
          margin-right: 11px;
          &.icon-yue {
            color: #fe9c01;
          }
          &.icon-zhifubao {
            color: #00aaea;
          }
          &.icon-a-ic_offlinepay {
            color: #FF7B00;
          }
        }
        .icon-xuanzhong4 {
          position: absolute;
          right: -4px;
          bottom: -3px;
          margin: 0 !important;
        }
        .name {
          font-size: 16px;
          color: #4e4e4e;
        }
        .yue {
          font-size: 14px;
          color: #969696;
          margin-top: 6px;
        }
      }
    }
    .goPay {
      margin: 80px 50px 0 0;
      .bnt,
      .button {
        width: 180px;
        height: 46px;
        font-size: 16px;
        color: #fff;
        border-radius: 4px;
        text-align: center;
        line-height: 46px;
        cursor: pointer;
      }
      .button {
        outline: none;
        border: none;
        background-color: #e93323;
        &:disabled {
          border-color: #fab6b6;
          background-color: #fab6b6;
        }
      }
    }
  }
}
::v-deep .el-dialog {
  width: 1000px;
}
.wrapper {
  background-color: #fff;
  .title {
    margin-bottom: 51px;
    font-size: 20px;
    .amount {
      font-size: 14px;
      color: #969696;
      .num {
        font-size: 22px;
        font-weight: bold;
      }
    }
  }
  .wx {
    width: 310px;
    .pictrue {
      width: 100%;
      height: 310px;
      border: 1px solid #ccc;
      position: relative;
      & > img {
        width: 100%;
        height: 100%;
      }
      .fail-code {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        cursor: pointer;
        .fail-code-info {
          text-align: center;
          width: 100%;
          color: #fff;
          & > img {
            display: inline-block;
            width: 50px;
            height: 50px;
          }
          & > div {
            margin-top: 10px;
          }
        }
      }
    }
    .text {
      width: 100%;
      height: 72px;
      background-color: #eb5e4a;
      color: #fff;
      font-size: 14px;
      margin-top: 16px;
      .iconfont {
        font-size: 50px;
        margin-right: 30px;
      }
      .tip {
        margin-top: 4px;
      }
    }
  }
  .phone {
    margin-left: 144px;
    width: 262px;
    height: 399px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .selectPay {
    font-size: 16px;
    color: #236fe9;
    margin-top: 68px;
    cursor: pointer;
    .iconfont {
      margin-right: 16px;
      font-size: 15px;
    }
  }
}
</style>
