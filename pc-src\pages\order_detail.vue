<template>
  <div class="order-detail wrapper_1200">
    <div class="header">
      <nuxt-link to="/">首页></nuxt-link>
      <nuxt-link :to="{path:'/user',query:{type:0}}">个人中心></nuxt-link>
      <span>订单详情</span>
    </div>
    <div v-if="orderData.activity_type == 2 && (orderData.status == 10 || orderData.status == 11)">
      <div class="section process">
        <div class="section-hd presell-hd">
         订单状态： {{ orderData.status == 11 ? '交易已关闭' : '待付尾款' }}
          <div>请在{{orderData.orderProduct[0].cart_info.productPresell.final_end_time}}前完成支付,超时订单将自动取消！</div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="section process" v-if="orderData.status >= 0">
        <!--order_type等于1为自提商品-->
      <div class="section-hd" v-if="orderData.status === 0 && orderData.order_type!=1">
        订单状态：
        {{orderData.is_virtual == 4 ? '待服务' : '待发货'}}
      </div>
      <div class="section-hd" v-if="orderData.status === 0 && orderData.order_type==1">
        订单状态：
        {{orderData.is_virtual == 4 ? '待核销' : '待发货'}}
      </div>
      <div class="section-hd" v-if="orderData.status === 1">
        订单状态：{{orderData.is_virtual == 1 ? '服务商品已虚拟发货' : orderData.is_virtual == 4 ? '待服务' : '待收货'}}
      </div>
      <div class="section-hd" v-if="orderData.status === 20 && orderData.is_virtual == 4">
        订单状态：{{orderData.order_type == 1 ? '待核销' : '已打卡'}}
      </div>
      <div class="section-hd" v-if="orderData.status === 2">
        订单状态：待评价
      </div>
      <div class="section-hd" v-if="orderData.status === 3">
        订单状态：已完成
      </div>
      <div class="section-bd">
        <ul class="acea-row row-middle row-center-wrapper">
          <li class="past">
            <div class="line"></div>
            <div class="iconfont icon-webicon318"></div>
            <div class="iconfont icon-fukuan">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待付款</div>
              <!-- <div>{{ orderData.create_time }}</div> -->
            </div>
          </li>
          <li
           v-if="orderData.order_type != 1 || (orderData.order_type == 1 && orderData.is_virtual == 4)"
          :class="{
              past:
                (orderData.status > 0 || orderData.status == -1) && (orderData.is_virtual == 4 && orderData.status != 1),
              now:
                orderData.status == 0 || orderData.status === 9 || (orderData.is_virtual == 4 && orderData.status === 1)
            }"
            >
            <div class="line"></div>
            <div :class="[
                'iconfont',
                 orderData.status == 0 || orderData.status == 9 || orderData.status >= 1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"></div>
            <div class="iconfont icon-peihuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>{{orderData.is_virtual == 4 ? '待服务' : '待发货'}}</div>
            </div>
          </li>
          <li
           v-if="orderData.order_type == 1 && orderData.is_virtual != 4"
          :class="{
              past:
                orderData.status > 0 || orderData.status == -1,
              now:
                orderData.status == 0 || orderData.status === 9
            }"
            >
            <div class="line"></div>
            <div :class="[
                'iconfont',
                 orderData.status == 0 || orderData.status == 9 || orderData.status >= 1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"></div>
            <div class="iconfont icon-peihuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>{{orderData.order_type == 1 ? '待核销' : '待发货'}}</div>
            </div>
          </li>
          <li
          v-if="orderData.is_virtual == 4"
          :class="{
              past:
                orderData.status > 1 || orderData.status == -1,
              now:
                orderData.status == 20
            }"
          >
            <div class="line"></div>
            <div
              :class="[
                'iconfont',
                 orderData.status > 1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-fahuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待核销</div>
            </div>
          </li>
          <li
          v-if="orderData.order_type != 1 && orderData.is_virtual!=4"
          :class="{
              past:
                orderData.status > 1 || orderData.status == -1,
              now:
                orderData.status == 1
            }"
          >
            <div class="line"></div>
            <div
              :class="[
                'iconfont',
                 orderData.status > 1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-fahuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待收货</div>
            </div>
          </li>
          <li
          :class="{
              past:
                orderData.status > 2 || orderData.status == -1,
              now:
                orderData.status == 2
            }"
          >
            <div class="line"></div>
            <div
              :class="[
                'iconfont',
                 orderData.status >= 2
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-pingjia1">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待评价</div>
            </div>
          </li>
          <li
          :class="{
              past:
                orderData.status > 3 || orderData.status == -1,
              now:
                orderData.status == 3
            }"
          >
            <div
              :class="[
                'iconfont',
                orderData.status == 3
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-wancheng"></div>
            <div class="info">
              <div>已完成</div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="orderData.remark" class="section-bd">
      <ul>
        <li class="acea-row">
          <div>发货备注：</div>
          <div>
            {{ orderData.remark }}
          </div>
        </li>
      </ul>
    </div>
  </div>

    <div v-if="orderData.refund_reason" class="section reason">
      <div class="section-hd">
        <span class="iconfont icon-tuikuantishi"></span>商家拒绝退款
      </div>
      <div class="section-bd">
        <ul>
          <li class="acea-row">
            <div>拒绝原因：</div>
            <div>
              {{ orderData.refund_reason }}
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!--卡密发货-->
    <div v-if="orderData.delivery_id && orderData.is_virtual == 3" class="section">
      <div class="section-bd">
        <ul>
          <li class="acea-row row-middle">
            <i class="iconfont icon-tuikuantishi" style="font-size: 20px;margin-right: 5px;"></i>
            <div>卡密发货：</div>
            <div>{{orderData.delivery_id}}</div>
          </li>
        </ul>
      </div>
    </div>
    <!--预约商品-->
    <template v-if="orderData.is_virtual == 4">
      <!--门店信息---到店-->
      <div v-if="orderData.order_type == 1" class="section">
        <div class="section-hd">门店信息</div>
        <div class="section-bd">
          <ul>
            <li class="acea-row row-middle">
              <div>商家姓名：</div>
              <div>{{orderData.real_name}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>联系电话：</div>
              <div>{{orderData.phone}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>门店名称：</div>
              <div>{{orderData.merchant.mer_name}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>门店地址：</div>
              <div>{{orderData.merchant.mer_address}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>营业时间：</div>
              <div>{{orderData.merchant.mer_take_time[0]}}-{{orderData.merchant.mer_take_time[1]}}</div>
            </li>
          </ul>
        </div>
      </div>
      <!--上门信息-->
      <div v-else class="section">
        <div class="section-hd">上门信息</div>
        <div class="section-bd">
          <ul>
            <li class="acea-row row-middle">
              <div>联系人：</div>
              <div>{{orderData.real_name}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>联系电话：</div>
              <div>{{orderData.user_phone}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>上门地址：</div>
              <div>{{orderData.user_address}}</div>
            </li>
            <li v-if="orderData.staffs" class="acea-row row-middle">
              <div>服务人员：</div>
              <div>{{orderData.staffs.name}}</div>
            </li>
            <li v-if="orderData.staffs" class="acea-row row-middle">
              <div>服务电话：</div>
              <div>{{orderData.staffs.phone}}</div>
            </li>
          </ul>
        </div>
      </div>
      <!--预约信息-->
      <div v-if="orderData&&orderData.orderProduct" class="section">
        <div class="section-hd">预约信息</div>
        <div class="section-bd">
          <ul>
            <li class="acea-row row-middle">
              <div>预约日期：</div>
              <div>{{orderData.orderProduct[0].reservation_date}} {{orderData.orderProduct[0].reservation_time_part}}</div>
            </li>
            <li class="acea-row row-middle">
              <div>预约数量：</div>
              <div>{{orderData.orderProduct[0].product_num}}</div>
            </li>
          </ul>
        </div>
      </div>
      
    </template>
    
    <div class="section">
      <div class="section-hd">订单信息</div>
      <div class="section-bd">
        <ul>
          <li class="acea-row row-middle">
            <div>订单编号：</div>
            <div>{{ orderData.order_sn }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>订单日期：</div>
            <div>{{ orderData.create_time }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>支付状态：</div>
            <div>已支付</div>
          </li>
          <li class="acea-row row-middle">
            <div>支付方式：</div>
            <div v-if="orderData.pay_type==0">余额支付</div>
            <div v-if="orderData.pay_type==7">线下支付</div>
            <div v-if="orderData.pay_type==4 || orderData.pay_type==5">支付宝支付</div>
            <div v-if="orderData.pay_type==1 || orderData.pay_type==2 || orderData.pay_type==3 || orderData.pay_type==6">微信支付</div>
          </li>
         
          <li class="acea-row row-middle">
            <div>订单金额：</div>
            <div class="money">￥{{ orderData.pay_price }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section" v-if="orderData.order_type == 0 && (![1,3,4].includes(orderData.is_virtual))">
      <div class="section-hd">收货信息</div>
      <div class="section-bd">
        <ul>
          <li class="acea-row row-middle">
            <div>收货人：</div>
            <div>{{ orderData.real_name }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>联系电话：</div>
            <div>{{ orderData.user_phone }}</div>
          </li>
          <li class="acea-row">
            <div>收货地址：</div>
            <div>{{ orderData.user_address }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section" v-if="orderData.order_extend&&orderData.order_extend.length>0">
      <div v-if="orderData.is_virtual !=4" class="section-hd"></div>
      <div class="section-bd">
        <ul v-for="(item,index) in orderData.order_extend" :key="index" class="form-ul">
          <li v-for="(itm,idx) in item" :key="idx"  class='acea-row row-middle'>
            <div>{{idx}}：</div>
            <div v-if="!Array.isArray(itm)" class='conter'>{{itm}}</div>
            <div v-else class='conter virtual_image'>
              <img v-for="(pic,i) in itm" :key="i" class="picture" :src="pic"/>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section" v-if="orderData.mark">
      <div class="section-bd">
        <ul>
          <li class="acea-row">
            <div>买家留言：</div>
            <div>{{ orderData.mark }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="orderData.delivery_type == 5" class="section">
      <div class="section-hd delivery-hd">
        <span>配送信息</span>
        <nuxt-link :to="{path:'/logistics_delivery',query:{orderId:orderData.order_id}}" class="morebtn">查看信息<span class="iconfont icon-you"></span></nuxt-link>
      </div>
      <div class="section-bd">
        <div v-if="orderData.delivery_id" class="delivery-info">
          <span><img src="../assets/images/delivery_man.png" /></span>
          <div class="delivery-detail">
            <span>{{orderData.delivery_name}}</span>
            <span>{{orderData.delivery_id}}</span>
          </div>
        </div>
        <div v-else class="delivery-info">
          <span><img src="../assets/images/dispatch.png" /></span>
          <div class="delivery-detail">
            <span>系统派单中...</span> 
          </div>
        </div>
      </div>
    </div>
    <div class="section order-number">
      <div class="section-bd" v-if="orderData.delivery_type === '2'">
        <ul>
          <li class="acea-row row-middle">
            <div>配送方式：</div>
            <div>送货</div>
          </li>
          <li class="acea-row row-middle">
            <div>配送员：</div>
            <div>{{ orderData.delivery_name || "" }}</div>
          </li>
          <li class="acea-row">
            <div>联系电话：</div>
            <div>{{ orderData.delivery_id || "" }}</div>
          </li>
        </ul>
      </div>
      <div
        class="section-bd"
        v-else-if="orderData.delivery_type === 'fictitious'"
      >
        <ul>
          <li class="acea-row row-middle">
            <div>虚拟发货：</div>
            <div>已发货，请注意查收</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section">
      <div v-if="orderData.merchant && orderData.is_virtual != 1" class="section-hd">
        <span class="mer_name">{{orderData.merchant.mer_name}}</span>
        <div class="orderBnt" v-if="orderData.mer_id&&(mer_service.services_type == 1)" @click="chatShow">联系客服 <span class="iconfont icon-lianxikefu"></span></div>
        <div class="orderBnt" v-else-if="mer_service.services_type == 2 && mer_service.service_phone">
          <el-tooltip popper-class="tps" effect="dark" :content="'客服电话：'+mer_service.service_phone" placement="right">       
            <div>联系客服 <span class="iconfont icon-lianxikefu"></span></div>
          </el-tooltip> 
        </div>
        <a class="orderBnt" v-else-if="mer_service.services_type== 4" :href="mer_service.mer_customer_link" target="blank">
          联系客服<span class="iconfont icon-lianxikefu"></span>
        </a>
      </div>
      <div class="section-bd">
        <ul class="goods" :class="'goods'+orderData.is_virtual">
          <div v-if="orderData.is_virtual == 1" class="title acea-row row-between-wrapper">
            <div class="item-status" :class="'status'+orderData.status">{{orderData.status == 0 ? '待核销' : '已核销'}}</div>
            <div v-if="orderData.status != 0 && orderData.verify_time" class="item-date">{{orderData.verify_time}}</div>
          </div>
          <li v-for="(item,index) in orderData.orderProduct" :key="index">
            <div v-if="orderData.activity_type == 2">
              <div class="acea-row row-middle">
                <div class="img-box" @click.stop="goDetail(item)">
                  <img :src="(item.cart_info.productAttr && item.cart_info.productAttr.image) || item.cart_info.product.image" />
                  <span>预售</span>
                </div>
                <div class="store-box" @click.stop="goDetail(item)">
                  <div class="name line2">{{ item.cart_info.product.store_name }}</div>
                  <div class="info" v-if="item.cart_info.productAttr.sku">{{item.cart_info.productAttr.sku}}</div>
                  <div class="money_count">
                    <span class="money">￥{{item.cart_info.productPresellAttr.presell_price}}</span>
                  </div>
                  <div v-if="orderData.status === 0 || orderData.status === 10 || orderData.status === 11" class="develity_date">
                    <!--全款预售-->
										<span v-if="item.cart_info.productPresell.presell_type === 1">
											发货时间：{{ item.cart_info.productPresell.delivery_type === 1 ? '支付成功后' : '预售结束后' }}{{ item.cart_info.productPresell.delivery_day }}天内
										</span>
										<!--定金预售-->
										<span v-if="item.cart_info.productPresell.presell_type === 2">
                    发货时间：{{ item.cart_info.productPresell.delivery_type === 1 ? '支付尾款后' : '预售结束后' }}{{ item.cart_info.productPresell.delivery_day }}天内
                    </span>
                  </div>
                </div>
               <div class="presell_num"><span>x{{ item.product_num }}</span></div>
                <div class="operate_btn">
                  <div class="refund_btn" v-if="(item.is_refund ==0  && (orderData.status != 10 && orderData.status != 11) && orderData.refund_status|| item.refund_num > 0) && orderData.is_virtual!=4"  style="margin-right: 10px;">
                    <el-button size="mini" @click.stop="refund(item)">申请退款</el-button>
                  </div>
                  <div class="refund_btn" v-if="item.is_refund == 1">
                    <span>退款中 </span>
                  </div>
                  <div class="refund_btn" v-if="item.is_refund > 1">
                    <span>已退款 x {{item.product_num - item.refund_num}}</span>
                  </div>
                  <div class="evaluate_btn" v-if='item.is_reply == 0 && orderData.status == 2 && item.is_refund==0'>
                    <el-button type="primary" size="mini" @click.stop="goEvaluate(item)">评价</el-button>
                  </div>
                </div>

              </div>
              <div v-if="orderData.status >= 10"  class="presell_process">
                <div class="process_count">
                  <div class="acea-row row-between">
                    
                    <div> 阶段1：买家已付款</div>
                      <div class="font-color">￥{{ orderData.pay_price }}</div>
                    </div>
                    <div class="acea-row row-between mt10">
                      <div>阶段2：
                      <span v-if="orderData.status == 10 && orderData.presellOrder.activeStatus == 0">未开始</span>
                      <span v-if="orderData.status == 10 && orderData.presellOrder.activeStatus == 1">等待买家付尾款</span>
                      <span v-if="orderData.status == 11 || orderData.presellOrder.activeStatus == 2">交易已关闭</span>
                      </div>
                      <div class="font-color" style="margin-top: 0;"> ￥{{ orderData.presellOrder.pay_price }}</div>
                    </div>
                  </div>
              </div>
            </div>
            <div v-else>
              <div class="acea-row row-middle">
                <div class="img-box" @click.stop="goDetail(item)">
                  <img :src="(item.cart_info.productAttr && item.cart_info.productAttr.image) || item.cart_info.product.image" />
                </div>
                <div class="store-box" @click.stop="goDetail(item)">
                  <div class="name line2">{{ item.cart_info.product.store_name }}</div>
                  <div class="info" v-if="item.cart_info.productAttr.sku">
                    {{item.cart_info.productAttr.sku}}
                  </div>
                  <div class="money_count">
                    <span class="money">￥{{item.cart_info.productAttr.price}}</span>
                    <span>x{{ item.product_num }}</span>
                  </div>
                </div>
                <div class="operate_btn">
                  <div class="refund_btn" v-if="item.cart_info.product.refund_switch==1 && (item.is_refund ==0  && (orderData.status != 10 && orderData.status != 11) && orderData.refund_status|| item.refund_num > 0) && orderData.is_virtual !=4" style="margin-right: 10px;">
                    <el-button size="mini" @click="refund(item)">申请退款</el-button>
                  </div>
                  
                  <div class="refund_btn" v-if="item.is_refund == 1">
                    <span>退款中 x{{ item.product_num - item.refund_num }}</span>
                </div>
                <div class="refund_btn" v-if="item.is_refund > 1">
                  <span>已退款 x {{item.product_num - item.refund_num}}</span>
              </div>
              <div class="evaluate_btn" v-if='item.is_reply == 0 && orderData.status == 2 && item.is_refund==0'>
                <el-button type="primary" size="mini" @click="goEvaluate(item)">评价</el-button>
              </div>
              </div>
              </div>
            </div>
          </li>
        </ul>
        <div v-if="orderData.order_type == 1 && orderData.takeOrderList" class="goods" >
          <div v-for="(item,index) in orderData.takeOrderList" :key="index">
            <ul class="goods1" v-for="(itm,idx) in item.orderProduct" :key="idx" style="padding-top: 0;">
              <li>  
                <div>
                  <div class="title acea-row row-between-wrapper">
                    <div class="item-status" :class="'status'+item.status">{{item.status == 0 ? '待核销' : '已核销'}}</div>
                    <div v-if="item.status != 0 && item.verify_time" class="item-date">{{item.verify_time}}</div>
                  </div>
                  <div class="acea-row row-middle">
                    <div class="img-box">
                      <img :src="(itm.cart_info.productAttr && itm.cart_info.productAttr.image) || itm.cart_info.product.image" />
                    </div>
                  <div class="store-box">
                    <div class="name line2">{{ itm.cart_info.product.store_name }}</div>
                    <div class="info" v-if="itm.cart_info.productAttr.sku">
                      {{itm.cart_info.productAttr.sku}}
                    </div>
                    <div class="money_count">
                      <span class="money">￥{{itm.cart_info.productAttr.price}}</span>
                      <span>x{{ itm.product_num }}</span>
                    </div>
                  </div>
                  <div class="operate_btn">
                    <div class="refund_btn" v-if="itm.cart_info.product.refund_switch==1 && (itm.is_refund ==0  && (item.status != 10 && item.status != 11) && item.refund_status|| itm.refund_num > 0)" style="margin-right: 10px;">
                      <el-button size="mini" @click="refund(itm)">申请退款</el-button>
                    </div>
               
                    <div class="refund_btn" v-if="itm.is_refund == 1">
                      <span>退款中 x{{ itm.product_num - itm.refund_num }}</span>
                    </div>
                    <div class="refund_btn" v-if="itm.is_refund > 1">
                      <span>已退款 x {{itm.product_num - itm.refund_num}}</span>
                    </div>
                    <div class="evaluate_btn" v-if='itm.is_reply == 0 && item.status == 2'>
                      <el-button type="primary" size="mini" @click="goEvaluate(itm)">评价</el-button>
                    </div>
                  </div>
                  </div>
                </div> 
              </li>
            </ul>
          </div>
        </div>
        <!---->
        <ul v-if="orderData.is_virtual == 0 || orderData.coupon_price > 0 || orderData.integral_price > 0" :class="'border'+orderData.order_type">
          <li v-if="orderData.is_virtual == 0" class="acea-row row-middle">
            <div>运费：</div>
            <div>
              {{ orderData.pay_postage <= 0 ? "包邮" : orderData.pay_postage }}
            </div>
          </li>
          <li v-if="orderData.coupon_price > 0" class="acea-row row-middle coupon">
            <div>优惠金额：</div>
            <div>
              <span>-￥{{ orderData.coupon_price }}</span>
            </div>
          </li>
          <li class="acea-row row-middle coupon" v-if="orderData.integral_price > 0">
            <div>积分抵扣： </div>
            <div>
             <span>-￥{{orderData.integral_price}}</span>
            </div>
          </li>
        </ul>
        <ul class="total">
          <li class="acea-row row-middle row-between">
            <div>
              共{{ orderData.total_num }}件商品，订单总金额为：<span class="money"
                >￥<b>{{ orderData.pay_price }}</b></span
              >
            </div>
            <div class="footerState acea-row row-middle">
              <nuxt-link
              class="orderBnt"
              :to="{path: '/logistics',query: { orderId: orderData.order_id }}"
              v-if="(orderData.status == 1 || orderData.status == 2) && orderData.delivery_type == 1 && orderData.activity_type != 2 && orderData.is_virtual != 4">查看物流</nuxt-link>
              <div v-if="refundNum.length != cartInfo.length && orderData.refund_status && orderData.refund_switch == 1 && !orderData.cancel_status"
              class="orderBnt" @click="allRefund">批量退款</div>
              <div class="orderBnt" v-if="orderData.is_virtual ==4 && orderData.cancel_status" @click="cancelReservation">
                取消预约
              </div>
              <div v-if="!orderData.receipt && orderData.status != -1 && orderData.open_receipt == 1" class="orderBnt" @click="showInvoicePupon">申请开票</div>
              <div
                class="orderBnt"
                :class="{
                  on: orderData.status === 1
                }"
                v-if="orderData.status === 1 && orderData.is_virtual != 4"
                @click="confirmOrder"
              >
                确认收货
              </div>
              <div
                class="orderBnt"
                v-if="orderData.status === 3"
                @click="delOrder"
              >
                删除订单
              </div>
              <div class="orderBnt"
                 v-if="orderData.activity_type==0 && orderData.is_virtual != 4 && (orderData.status === 3 || orderData.status === 2)" @click="goOrderConfirm">
                再次购买
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <add-invoicing ref="addInvoicing" @applyInvoice="applyInvoice"></add-invoicing>
    <chat-room
      v-if="chatPopShow"
      :chatId="orderData.mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
// +----------------------------------------------------------------------
import {Message, MessageBox} from "element-ui";
import ChatRoom from "@/components/ChatRoom";
import addInvoicing from "@/components/addInvoicing";
export default {
  auth: "guest",
  components: { ChatRoom, addInvoicing },
  data() {
    return {
      chatPopShow: false,
      orderData: {},
      goodsNum: 0,
      refundNum: [],
      cartInfo: [],
    };
  },
  async asyncData({ app, query }) {
    let [ orderData ] = await Promise.all([
            app.$axios.get(`/api/order/detail/${query.orderId}`)
        ]);
    return {
      orderData: orderData.data,
      mer_service: orderData.data.merchant.services_type,
      cartInfo: orderData.data.orderProduct
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "订单详情-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    let that = this,
    num = 0;
    that.cartInfo.map(el => {
		if (el.refund_num == 0) {
			that.refundNum.push(el)
		}
	})
    that.goodsNum = num;
  },
  methods: {
    chatShow(mer_id) {
      if(this.$auth.loggedIn){
        this.mer_id = mer_id;
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    goDetail(item) {
      if(item.product_type == 1){
        this.$router.push({ path: '/goods_seckill_detail/'+item.product_id, query: { time: item.stop_time,status: item.seckill_status } });
      }else if(item.product_type == 2){
        this.$router.push({ path: '/goods_presell_detail/'+item.activity_id });
      }else{
        this.$router.push({ path: '/goods_detail/'+item.product_id });
      }
    },
    getOrderInfo() {
      this.$axios.get(`/api/order/detail/${this.orderData.order_id}`).then(res => {
        this.orderData = res.data;
      });
    },
    // 申请开票
    showInvoicePupon(){ 
      this.$refs.addInvoicing.showInvoicePupon();
    },
    // 开票
    applyInvoice(data) {
      let that = this;
      that.$axios
        .post("/api/order/receipt/"+that.orderData.order_id, data).then(res => {
          this.getOrderInfo()
          return Message.success(res.message)
        }).catch(err => {
          return Message.error(err.message)
        })
    },
    // 取消订单
    cancelOrder() {
      let that = this;
      MessageBox.confirm("确定取消该订单吗？", "提示").then(res => {
        that.$axios
          .post("/api/order/cancel", {
            id: that.orderData.order_id
          })
          .then(data => {
            Message.success(data.message);
            that.$router.replace({path:'/user/order_list',query:{type:1}});
          });
      });
    },
    // 取消预约
    cancelReservation() {
      let that = this;
      MessageBox.confirm("确定取消预约吗？", "提示").then(res => {
        that.$axios
          .post("/api/order/self/cancel/"+that.orderData.order_id)
          .then(data => {
            Message.success(data.message);
            that.getOrderInfo()
          });
      });
    },
    //去支付
    goPay() {
      this.$router.push({
        path: "/payment",
        query: { result: this.orderData.order_id }
      });
    },
    //确认收货
    confirmOrder() {
      let that = this;
      MessageBox.confirm(
        "为保障权益，请收到货确认无误后，再确认收货",
        "提示"
      ).then(res => {
        that.$axios
          .post("/api/order/take/"+ that.orderData.order_id
          )
          .then(data => {
            Message.success("操作成功");
            that.getOrderInfo();
          });
      });
    },
    //删除订单
    delOrder() {
      let that = this;
      MessageBox.confirm("确定删除该订单吗？", "提示").then(res => {
        that.$axios
          .post("/api/order/del/"+that.orderData.order_id)
          .then(data => {
            Message.success("删除成功");
            that.$router.replace({path:'/user/order_list',query:{type:1}});
          });
      });
    },
    //  再次购买
    goOrderConfirm() {
      let that = this;
      let data = []
      this.cartInfo.map((item, index) => {
        let obj = {}
        obj.product_id = item.product_id
        obj.product_attr_unique = item.product_sku
        obj.cart_num = item.product_num
        data.push(obj)
      })
      that.$axios
        .post("/api/user/cart/again", {data: data})
        .then(res => {
          let cart_id = res.data.cart_id.join(',')
          that.$router.replace({
            path: "/order_confirm",
            query: { new: 1, cartId: cart_id }
          });
        }).catch(err=>{
          that.$message.error(err);
      })
    },
    //  去评价
    goEvaluate(item) {
      this.$router.push({
        path: "/evaluation",
        query: {
          unique: item.order_product_id,
          order_id: this.orderData.order_id
        }
      });
    },
     //批量退款
    allRefund() {
      if (this.orderData.status == 0 || this.orderData.is_virtual == 4) {
        this.$router.push({
          path: "/refund",
          query: {
            orderId: this.orderData.order_id,
            refund_type: 1,
            type: 2
          }
        });
      } else {
        this.$router.push({
          path: "/user/refund_select",
          query: {
            orderId: this.orderData.order_id,
            type: 2
          }
        });
      }
    },
    //退款
    refund(item){
      if(this.orderData.status == 0 || this.orderData.status == 9 || this.orderData.is_virtual != 0 || this.orderData.is_virtual ==4){
        this.$router.push({
          path: "/refund_confirm",
          query: {
            orderId: item.order_id,
            type: 1,
            ids: item.order_product_id,
            refund_type: 1,
          }
        });
      }else{
        this.$router.push({
          path: "/user/refund_select",
          query: {
            orderId: item.order_id,
            type: 1,
            ids: item.order_product_id
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.order-detail {
  >div {
    background: #fff;
  }
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    background-color: unset;
    .home {
      color: #282828;
    }
  }
  > div {
    .process {
      margin-top: 0;
      div {
        border-top: none;
        &.section-hd {
          padding: 26px 22px 0;
        }

        ul {
          padding: 27px 0 94px;
          &::after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
          }
        }
        li {
          position: relative;
          float: left;
          margin-top: 0;
          margin-left: 222px;
          &:first-child {
            margin-left: 0;
          }
          .line {
            position: absolute;
            top: 50%;
            left: 16px;
            width: 226px;
            height: 4px;
            background: #c7c7c7;
            transform: translateY(-50%);
          }
          .iconfont {
            position: relative;
            width: auto;
            font-size: 18px;
            line-height: 1;
            color: #c7c7c7;
            + .iconfont {
              position: absolute;
              top: 50%;
              left: 50%;
              display: none;
              width: 40px;
              height: 40px;
              border: 4px solid #e93323;
              border-radius: 50%;
              background: #ffffff;
              transform: translate(-50%, -50%);
              font-size: 20px;
              line-height: 32px;
              text-align: center;
              color: #e93323;
            }
          }
          .arrow {
            position: absolute;
            top: 50%;
            left: 100%;
            display: none;
            width: 80px;
            height: 16px;
            background: #e93323;
            transform: translateY(-50%);
            &::after {
              content: "";
              position: absolute;
              top: 0;
              left: 100%;
              border-width: 8px;
              border-style: solid;
              border-color: transparent transparent transparent #e93323;
            }
          }
          .info {
            position: absolute;
            top: 42px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            text-align: center;
            color: #9a9a9a;
            width: 100px;
            div {
              &:first-child {
                margin-bottom: 4px;
                font-size: 16px;
                color: #282828;
              }
            }
          }
          &.past {
            .line {
              background: rgba(233, 51, 35, 0.6);
            }
            .iconfont {
              color: #e93323;
              font-weight: bold;
              font-size: 22px;
              left: -3px;
            }
          }
          &.now {
            .info {
              div {
                &:first-child {
                  color: #e93323;
                }
              }
            }
            .iconfont {
              + .iconfont {
                display: block;
              }
            }
            .arrow {
              display: block;
            }
          }
        }
      }
    }
    &.order-number {
      li {
        div {
          &:nth-child(2) {
            flex: none;
          }
        }
        a {
          margin-left: 30px;
          font-size: 16px;
          color: #236fe9;

          .iconfont {
            font-size: 12px;
          }
        }
      }
    }
    ~ div {
      margin-top: 14px;
    }
    > div {
      ~ div {
        border-top: 1px dashed #cecece;
      }
    }
     .presell-hd {
        div{
          margin-top: 20px;
        }
      }

    &.reject {
      position: relative;
      padding: 30px 22px;
      background: #666666;
      overflow: hidden;
      margin-top: 0;
      .iconfont {
        position: absolute;
        top: -20px;
        right: 28px;
        font-size: 112px;
        color: #818181;
      }
      div {
        border-top: none;
        &.section-hd {
          padding: 0;
          font-weight: bold;
          color: #ffffff;
        }
        ul {
          padding: 0;
          margin-top: 8px;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
    &.reason {
      padding: 26px 22px;
      div {
        border-top: none;
        &.section-hd {
          padding: 0;
          .iconfont {
            margin-right: 8px;
          }
        }
        ul {
          padding: 0;
          margin-top: 15px;
          color: #7e7e7e;
        }
      }
    }
  }
  .virtual_image{
    display: flex;
    align-items: center;
    img{
      width: 70px;
      height: 70px;
      border-radius: 4px;
      margin-right: 5px;
    }
  }
  .delivery-hd{
    display: flex;
    justify-content: space-between;
    .morebtn{
      color: #236FE9;
      font-size: 16px;
    }
  }
  .delivery-info{
    display: flex;
    align-items: center;
    padding: 20px 22px;
    img{
      width: 40px;
      height: 40px;
    }
    .delivery-detail{
      margin-left: 20px;
      span{
        margin-right: 10px;
      }
    }
  }
  .section-hd {
    padding: 18px 22px;
    font-size: 18px;
    color: #282828;
    position: relative;
    .qrcode{
        box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
        background: #fff;
        position: absolute;
        right: 0;
        top: 30px;
        z-index: 10;
        display: none;
        padding: 6px;
        img{
          width: 100%;
          display: block !important;
        }
    }
    .orderBnt{
        color: #E93323;
        font-size: 18px;
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        &:hover{
            .qrcode{
                display: block;
            }
        }
        .iconfont{
            font-size: 20px;
        }
    }
  }
  .section-bd {
    ul {
      padding: 22px;
      font-size: 16px;
      color: #282828;
    
      &.goods1{
        border: none;
      }
    }
    li {
      .time {
        margin-left: 10px;
      }
      ~ li {
        margin-top: 20px;
      }
      &.coupon {
        span {
          ~ span {
            margin-left: 18px;
          }
        }
      }
    }
    .money {
      color: #e93323;
      b {
        font-weight: normal;
        font-size: 22px;
      }
    }
    .goods {
      &.goods1{
        padding-top: 0;
        &::after{
          content: "";
          display: block;
          width: 1200px;
          height: 14px;
          background:#F9F9F9;
          position: relative;
          top: 20px;
          left: -22px;
        }
      }
      .info {
        font-size: 12px;
        color: #aaa;
        margin-top: 4px;
      }
      li {
        position: relative;
        cursor: pointer;
        ~ li {
          margin-top: 22px;
        }
        > div {
          .img-box {
            width: 86px;
            height: 86px;
            overflow: hidden;
            margin-right: 26px;
            position: relative;
            span {
              display: block;
              width: 100%;
              text-align: center;
              font-size: 12px;
              line-height: 18px;
              background: rgba(0,0,0,.5);
              position: absolute;
              left: 0;
              bottom: 0;
              color: #fff;
            }
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
          }
          del {
            margin-left: 12px;
            font-size: 14px;
            color: #919191;
           }
        }
        .money_count{
          margin-top: 4px;
          color: #b1b1b1;
        }
        .store-box{
          width: 700px;
          .name{
            max-width: 500px;
          }
        }
        .presell_num{
          color: #b1b1b1;
        }
      }
      .develity_date{
        color: #FD6523;
        font-size: 12px;
      }
      .title{
        height: 60px;
        position: relative;
        margin-bottom: 15px;
        &::after{
          content: "";
          width: 1200px;
          border-bottom: 1px dashed #CECECE; 
          position: absolute;
          bottom: 0;
          left: -22px;
        }
        .item-status{
          color: #999999;
          font-size: 14px;
          &.status0{
            color: #2291F8;
          }
        }
        .item-date{
          color: #666666;
          font-size: 13px;
        }
		  }
    }
    .total {
      padding: 28px 22px;
      .footerState {
        cursor: pointer;
      }
      .service {
        width: 114px;
        height: 40px;
        margin-left: 18px;
        background: #e93323;
        color: #fff;
        font-size: 16px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
      }
      div {
        &:first-child {
          width: auto;
        }
        &:last-child {
          flex: none;
          div {
            padding-right: 30px;
            padding-left: 30px;
            ~ div {
              border-left: 1px solid #cecece;
            }
          }
          .orderBnt {
            width: 114px;
            height: 40px;
            padding: 0;
            border: 1px solid #999999;
            border-radius: 2px;
            background: none;
            outline: none;
            font-size: 16px;
            line-height: 40px;
            text-align: center;
            color: #282828;
            cursor: pointer;
            ~ .orderBnt {
              margin-left: 18px;
            }
            &.on {
              border-color:  #e93323;
              background: #e93323;
              color: #ffffff;

            }
          }
        }
      }
    }
  }
}
.form-ul {
  border-bottom: 14px solid #f5f5f5;
  &:last-child {
    border: none;
  }
}
.operate_btn{
  display: flex;
  position: absolute;
  right: 0;
  color:  #B1B1B1;
  align-items: center;

}
.presell_process{
  margin-top: 20px;
  .process_count{
    background: #FFF8F7;
    padding: 21px 20px;
    >div{
      color: #282828;
      width: 60%;
    }
  }
  .mt10{
    margin-top: 10px;
  }
}
.evaluate_btn {
 margin-left: 15px;
}
.border1{
  border-top: none!important;;
}
</style>
