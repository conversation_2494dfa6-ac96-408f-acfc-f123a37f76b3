<template>
  <div class="qualify-wrapper wrapper_1200">
    <div class="qualify-main">

      <div class="qualifyMain">
        <!--店铺资质图片-->
        <div class="qualify-count" v-if="urlList.length">
          <div class="title">{{storeName}}</div>
          <div class="desc">注：以下信息，由商家依据《电子商务法》规定发布公示。如需进一步核实，可联系商家客服咨询</div>
          <div class="content-main" v-html="content"></div>
        </div>
        <!--输入验证码-->
        <div v-else>
          <div class="com-title">
            商家营业执照信息
          </div>
          <div class="desc">根据国家工商总局《网络交易管理办法》要求对网店营业执照信息公示如下：</div>
          <el-form ref="ruleForm" label-width="80px" class="demo-ruleForm" @submit.native.prevent>
            <el-form-item label="验证码：">
              <el-input v-model="verCodeValue" placeholder="请输入图片中的验证码" class="verifiCode"></el-input>
              <span class="imageCode" @click="getVerCodeImage"><img :src="captchaData.captcha" alt=""></span>
            </el-form-item>
            <el-form-item >
              <el-button type="primary" @click="onSubmit">提交</el-button>
            </el-form-item>
          </el-form>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
import sendVerifyCode from "@/mixins/SendVerifyCode";
export default {
  name: "qualifications",
  auth: "guest",
  mixins: [sendVerifyCode],
  data() {
    return {
      captchaData: {},
      verCodeValue: '',
      urlList: [],
      content: '',
    };
  },
  async asyncData({app, params, query}) {
    return {
      id: query.id,
      storeName: query.storeName
    };
  },
  fetch({store}) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "用户协议与隐私政策-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getVerCodeImage();
  },
  mounted() {
  },
  methods: {
    getVerCodeImage(){
      let that = this;
      that.$axios.get("/api/captcha").then(res => {
        that.captchaData = res.data;
      }).catch(err => {
        that.$message.error(err);
      });
    },
    onSubmit(){
      let that = this;
      if(that.verCodeValue) {
        that.$axios.post("/api/store/certificate/"+that.id,{ key: that.captchaData.key, code: that.verCodeValue}).then(res => {
          if(res.status == 200 && res.message == 'success') {

            that.urlList = res.data;
            that.content = '';
            let imgTap = '';
            that.urlList.forEach(item => {
              imgTap+=that.setImgTap(item);
            })
            that.content = `<p>${imgTap}</p>`;
          }
        }).catch(err => {
          that.$message.error(err);
          that.getVerCodeImage();
          that.verCodeValue = '';
        })
      } else {
        that.$message.error('请输入验证码');
      }
    },
    setImgTap(url) {
      return `<img style="max-width:100%;margin:0 auto 15px;display: block;" src="${url}"></img>`
    },

  }
}
</script>

<style lang="scss" scoped>

.qualify-wrapper {
  margin-top: 20px;
  .qualify-main{
    background: #ffffff;
    padding: 0 34px 60px;
    min-height: 670px;
  }
  .com-title {
    font-size: 20px;
    color: #282828;
    border: none;
    line-height: 74px;
    border-bottom: 1px solid #ECECEC;
  }
  .qualifyMain{

    .desc{
      display: block;
      margin-top: 40px;
      color: #969696;
      font-size: 16px;
    }
  }
  .el-form{
    margin-top: 60px;
    .verifiCode{
      width: 248px;
    }
    .imageCode,.verifiCode{
      float: left;
    }
    .imageCode{
      display: inline-block;
      margin-left: 12px;
      width: 124px;
      height: 44px;
      cursor: pointer;
      img{
        width: 124px;
        height: 44px;
      }
    }
  }
}
.qualify-count{
  text-align: center;
  .content-main{
    margin-top: 40px;
  }
  .title{
    padding-top: 50px;
    color: #282828;
    font-size: 20px;
  }
}
</style>
