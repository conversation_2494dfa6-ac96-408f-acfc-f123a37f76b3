<template>
  <div class="ivu-global-footer i-copyright" v-if="openVersion == 1 && !headMenuNoShow">
       <div v-if="version.status == -1" class="ivu-global-footer-copyright">{{ `Copyright ${version.year} ` }}<span>{{ version.version }}</span></div>
       <div v-else class="ivu-global-footer-copyright">{{ version.Copyright }}</div>
    </div>
</template>
<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { getVersion } from '@/api/accounts'
export default {
  name: 'i-copyright',
  data () {
    return {
      copyright: leaveuKeyTerms['Copyright © 2022 西安众邦网络科技有限公司'],
      openVersion: '0',
      copyright_status: '0',
      version: {},
      headMenuNoShow: false
    }
  },
  watch: {
    $route(newRoute) {
      this.headMenuNoShow = this.$route.meta.fullScreen;
    }
  },
  mounted () {
    this.headMenuNoShow = this.$route.meta.fullScreen;
    this.getVersion();
  },
  methods: {
    getVersion () {
      getVersion().then((res) => {
        let version = res.data.version
        this.version = res.data;
        this.copyright = res.data.copyright
        this.openVersion = res.data.sys_open_version;
      }).catch((res) => {
        this.$message.error(res.message);
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.ivu-global-footer {
  /* margin: 48px 0 24px 0; */
  /* padding: 0 16px; */
  margin: 15px 0px;
  text-align: center;
  box-sizing: border-box;
  // margin-left: 210px;
}
.i-copyright {
  flex: 0 0 auto;
}
.ivu-global-footer-links {
  margin-bottom: 8px;
}
.ivu-global-footer-links a:not(:last-child) {
  margin-right: 40px;
}
.ivu-global-footer-links a {
  font-size: 14px;
  color: #808695;
  transition: all .2s ease-in-out;
}
.ivu-global-footer-copyright {
  color: #808695;
  font-size: 14px;
}
</style>
