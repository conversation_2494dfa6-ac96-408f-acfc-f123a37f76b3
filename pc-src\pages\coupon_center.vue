<template>
  <div>
      <div class="goods_cate">
      <category-List ref="cateGory"></category-List>
      <div class="coupon-center">
        <div class="title-section">
          <img src="@/assets/images/coupon-title.png" />
        </div>
        <div class="title-selection">
          <div v-for="(item, index) in typeList" :class="item.val == couponType ? 'on' : ''" class="title-item" @click="changeType(item.val)">
            <span>{{item.label}}</span>
          </div>
        </div>
      </div>
      <div class="main-bd">
        <div v-if="couponList.length > 0">
          <div class="list acea-row">
            <div v-for="item in couponList" :key="item.id" class="item acea-row" :class="item.ProductLst.length > 0 ? '' : 'disabled'">
              <div class="text">
                <div class="product">
                  <img :src="item.ProductLst[0]['image']" v-if="item.ProductLst && item.ProductLst[0]"/>
                  <img src="~assets/images/no_product.png" alt="" v-else>
                </div>
                <div class="coupon-desc">
                  <div class="price">
                    <p>¥<span>{{item.coupon_price}}</span></p>
                    <span v-if="item.use_min_price == 0" class="used">无门槛使用</span>
                    <span v-else class="used">满{{parsePrice(item.use_min_price)}}可用</span>
                  </div>
                  <div class="coupon-title">
                      <span class="coupon-label">{{item.type == 0 ? '店铺券' : item.type == 1 ? '商品券' : item.type == 11 ? '品类券' : item.type == 12 ? '跨店券' : '通用券' }}</span>
                      <span class="title line1">{{item.title}}</span> 
                  </div>
                </div>
                <div class="text-cont acea-row row-middle">
                 
                </div>
              </div>
              <div class="btn acea-row row-middle" v-if="!item.issue" :class="item.ProductLst.length > 0 ? '' : 'disabled'" @click="getReceive(item)">立即领取</div>
              <div class="btn acea-row row-middle" v-else>
                <nuxt-link v-if="item.ProductLst.length > 0 " :to="{path:'/goods_coupon',query:{id:item.coupon_id}}">去使用</nuxt-link>
                <div v-else>去使用</div>
              </div>
            </div>   
          </div>
           <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
        </div>
          <div class="empty-box" v-if="couponList.length == 0">
            <img src="~/assets/images/noCoupou.png" alt="">
            <p>亲，暂无可使用优惠券哟~</p>
          </div>
        </div>
    </div>
     <!--优惠券弹窗-->
    <div v-if="isReceive" class="coupon-poupon">
      <div class="poupon-count">
        <span class="closeBtn iconfont icon-guanbi" @click="closeCoupon"></span>
        <div class="coupon-image">
          <img src="~assets/images/coupon-receive.png" alt="">
        </div>
        <div class="coupon-title">
          <div class="text">恭喜您，领取成功</div>
          <div class="message">感谢您的参与，祝您购物愉快</div>
        </div>
        <nuxt-link :to="{path:'/goods_coupon',query:{id:coupon_id}}" class="useBtn">
            立即使用
        </nuxt-link>
      </div>
    </div>
  </div>   
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import categoryList from "@/components/categoryList";
export default {
      name: "goods_cate",
      auth: false,
      components: {
        categoryList
      },
      data(){
        return {
          titleName:'',
          couponList:[],
          coupon_id: '',
          pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
          page: 1, //代表页面的初始页数
          limit:15,
          scollY: null,// 离底部距离有多少
          total: 0, //总页数
          title:'下拉加载更多',
          cid:0,//一级分类
          sid:'',//二级分类
          order: '',
          priceOrder: '',
          price_on: '',
          price_off: '',
          news:0,
          is_multiple: false,
          checkedBrands: [],
          is_showMore: true,
          search: '',
          couponType: -1,
          typeList: [
            {label: '全部', val: '-1'},
            {label: '通用券', val: '10'},
            {label: '品类券', val: '11'},
            {label: '跨店券', val: '12'},
            {label: '店铺券', val: '0'},
          ],
          isReceive: false,
        }
      },
      async asyncData({app,query}){
        return {
          keyword: query.title ? query.title : '',
          sid: query.sid ? query.sid:'',
          titleName: query.name ? decodeURI(query.name).split(',') : '',
        }
      },
      watch:{
        $route: {
            handler: function(newVal, oldVal){
                this.keyword = newVal.query.title ? newVal.query.title : '';
                this.sid = newVal.query.sid ? newVal.query.sid : '';
                this.titleName = newVal.query.name ? decodeURI(newVal.query.name).split(',') : '';
                this.page = 1;
                this.getCouponList();
            },
            // 深度观察监听
            deep: true
        },
        
      },
      fetch({ store}) {
        store.commit("isBanner", false);
        store.commit('isHeader', true);
        store.commit('isFooter', true);
      },
      head() {
        return {
          title: "领券中心-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
        this.searchVal = this.$route.query.title;
        this.sid = this.$route.query.sid || '';
        this.getCouponList('');
      },
      mounted(){},
      beforeDestroy() {},
      methods:{
        getCouponList(num){
          let _this = this;
          _this.page = num ? num : _this.page;
          let type = _this.couponType == -1 ? '' : _this.couponType
          let  currentPage = {page: _this.page,limit:_this.limit,type: type,product:1};
          _this.$axios.get('/api/coupon/getlst', {
            params: currentPage
          }).then(function (res) {
            _this.total = res.data.count;
            _this.couponList = res.data.list;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getCouponList('');
        },
        changeType(type) {
          this.couponType = type
          this.getCouponList(1)
        },
        parsePrice(price){
          return parseInt(price)
        },        
        //立即领取
        getReceive(item) {
          let _this = this
          _this.$axios
            .post("/api/coupon/receive/"+item.coupon_id)
            .then(function (res) {
              _this.isReceive = true
              item.issue = true
              _this.coupon_id = item.coupon_id
            })
            .catch(function (err) {
              _this.$message.error(err);
            });
        },
        closeCoupon() {
          this.isReceive = false;
        }      
      }
    }
</script>

<style scoped lang="scss">
  .trader{
    color: #fff;
    background-color: #e93323;
    display: inline-block;
    width: 32px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 2px;
    margin-right: 5px;
    font-size: 12px;
  }
  .cate_name{
    cursor: pointer;
    &.on{
      color: #E93323
    }
  }
  .coupon-center{
    background-image: url("../assets/images/coupon-center.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .title-section {
    padding-top: 40px;
    padding-bottom: 40px;
    text-align: center;
    img {
      display: inline-block;
      width: 200px;
      height: 48px;
      vertical-align: middle;
    }
  }
  .title-selection{
    background: #ffffff;
    width: 1200px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 0 30px;
    .title-item{
      margin-right: 20px;
      padding: 3px 18px;
      border-radius: 21px;
      border: 1px solid #BBBBBB;
      color: #666666;
      cursor: pointer;
      &.on{
        color: #fff;
        background-color: #E93323;
        border-color: #E93323;
      }
    }
  }
  .main-bd{
    width: 1200px;
    margin: 30px auto 0;

    .item {
      width: 380px;
      height: 160px;
      margin-right: 30px;
      margin-bottom: 20px;
      background: url("~assets/images/coupon-back3.png") center/cover no-repeat;
      box-shadow: 0 3px 20px rgba(0, 0, 0, 0.08);
      &.disabled {
        background: url("~assets/images/coupon-back2.png") center/cover no-repeat;
        pointer-events:none;
        cursor:not-allowed;
      }
      &:nth-child(3n){
        margin-right: 0;
      }
      .text {
        flex: 1;
        padding-left: 14px;
        display: flex;
        align-items: center;
      }
      .product {
        width: 100px;
        height: 100px;
        img{
          width: 100px;
          height: 100px;
        }
      }
      .coupon-desc {
        margin-left: 20px;
        .price{
          display: flex;
          align-items: baseline;
          color: #969696;
          font-size: 12px;
          p{
             font-size: 16px;
            color: #E93323;
             span{
               font-size: 28px;
               font-weight: bold;
             }
          }
         
        }
      }
      .coupon-title{
         color: #282828;
         font-size: 14px;
         line-height: 22px;
         margin-top: 12px;
         display: flex;
         align-items: center;
        .coupon-label{
          color: #E93323;
          font-size: 12px;
          background: rgba(233,51,35,.1);
          padding: 0 7px;
          line-height: 20px;
          border-radius: 10px;
          text-align: center;
        }
        .title{
          display: inline-block;
          max-width: 120px;
          margin-left: 5px;
        }
      }

      .btn {
        width: 52px;
        padding-right: 18px;
        padding-left: 18px;
        font-size: 16px;
        color: #ffffff;
        cursor: pointer;
        &.disabled{
          pointer-events:none;
          cursor:not-allowed;
        }
        a{
          color: #fff;
        }
      }
    }
  }
  .coupon-poupon{
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}
.poupon-count{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 54px 0 60px;
  background: #fff;
  width: 370px;
  height: 400px;
  text-align: center;
  .closeBtn {
    color: #939393;
    position: absolute;
    top: 20px;
    right: 20px;
    text-align: center;
    font-size: 20px;
    cursor: pointer;
  }
  .coupon-image{
    text-align: center;
    img{
      display: inline-block;
      width: 120px;
      height: 120px;
    }
  }
  .coupon-title {
    margin-top: 24px;
    .text{
      color: #E93323;
      font-size: 20px;
    }
    .message{
      margin-top: 10px;
      color: #939393;

    }
  }
  .useBtn {
    margin: 46px auto 0;
    width: 150px;
    height: 42px;
    line-height: 42px;
    border-radius: 4px;
    background: #E93323;
    display: block;
    color: #fff;
  }
}
</style>
