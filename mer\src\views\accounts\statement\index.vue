<template>
  <div class="divBox">
    <div class="selCard mb14">
      <el-form size="small" label-width="85px" :inline="true">
        <el-form-item :label="$t('时间选择：')" >
          <el-date-picker
            v-model="timeVal"
            type="daterange"
            align="right"
            unlink-panels
            format="yyyy/MM/dd"
            value-format="yyyy/MM/dd"
            style="width: 280px;"
            @change="onchangeTime"
          />
        </el-form-item>
      </el-form>
    </div>
    <cards-data v-if="cardLists.length>0" :more="true" :card-lists="cardLists" />
    <el-card class="box-card">
      <div class="mb20">
        <el-tabs v-if="headeNum.length > 0" v-model="tableForm.type" @tab-click="getList(1)">
          <el-tab-pane v-for="(item,index) in headeNum" :key="index" :name="item.type.toString()" :label="item.title" />
        </el-tabs>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
        class="table"
        highlight-current-row
      >
        <el-table-column :label="$t('序号')" min-width="100">
          <template scope="scope">
            <span>{{ scope.$index+(tableForm.page - 1) * tableForm.limit + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="time"
          :label="$t('日期')"
          min-width="150"
        />
        <el-table-column
          prop="income"
          :label="$t('账期内收入')"
          min-width="150"
        />
        <el-table-column
          prop="expend"
          :label="$t('账期内支出')"
          min-width="150"
        />

        <el-table-column
          prop="charge"
          :label="$t('商户应入账金额')"
          min-width="150"
        />
        <el-table-column :label="$t('操作')" min-width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="onDetails(scope.row.time)">{{ $t('详情') }}</el-button>
            <el-button type="text" size="small" @click="exports(scope.row.time)">{{ $t('下载账单') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block mb20">
        <el-pagination
          background
          :page-size="tableForm.limit"
          :current-page="tableForm.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <el-dialog
      :title="tableForm.type == 1 ? '日账单详情' : '月账单详情'"
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
      center
    >
      <el-row align="middle" class="ivu-mt mt20">
        <el-col :span="4">
          <el-menu
            default-active="0"
            class="el-menu-vertical-demo"
          >
            <el-menu-item :name="accountDetails.date">
              <span>{{ accountDetails.date }}</span>
            </el-menu-item>
          </el-menu>
        </el-col>
        <el-col :span="20">
          <el-col :span="8">
            <div class="grid-content">
              <span class="title">{{ accountDetails.income && accountDetails.income.title }}</span>
              <span class="color_red">{{ accountDetails.income && accountDetails.income.number }}元</span>
              <span class="count">{{ accountDetails.income && accountDetails.income.count }}</span>
              <div v-if="accountDetails.income.data" class="list">
                <el-row v-for="(item,index) in accountDetails.income.data" :key="index" class="item">
                  <el-col :span="12" class="name">{{ item['0'] }}</el-col>
                  <el-col :span="12" class="cost">
                    <span class="cost_num">{{ item['1'] }}</span>
                    <span class="cost_count">{{ item['2'] }}</span>
                  </el-col>
                </el-row>
              </div>
            </div>
            <el-divider direction="vertical" />
          </el-col>
          <el-col :span="8">
            <div class="grid-content">
              <span class="title">{{ accountDetails.expend && accountDetails.expend.title }}</span>
              <span class="color_gray">{{ accountDetails.expend && accountDetails.expend.number }}元</span>
              <span class="count">{{ accountDetails.expend && accountDetails.expend.count }}</span>
              <div v-if="accountDetails.expend.data" class="list">
                <el-row v-for="(item,index) in accountDetails.expend.data" :key="index" class="item">
                  <el-col :span="12" class="name">{{ item['0'] }}</el-col>
                  <el-col :span="12" class="cost">
                    <span class="cost_num">{{ item['1'] }}</span>
                    <span class="cost_count">{{ item['2'] }}</span>
                  </el-col>
                </el-row>
              </div>
            </div>
            <el-divider direction="vertical" />
          </el-col>
          <el-col :span="8">
            <div class="grid-content">
              <span class="title">{{ accountDetails.charge && accountDetails.charge.title }}</span>
              <span class="color_gray">{{ accountDetails.charge && accountDetails.charge.number }}元</span>
              <span class="count">{{ accountDetails.charge && accountDetails.charge.count }}</span>
              <div v-if="accountDetails.charge.data" class="list">
                <el-row v-for="(item,index) in accountDetails.charge.data" :key="index" class="item">
                  <el-col :span="12" class="name">{{ item['0'] }}</el-col>
                  <el-col :span="12" class="cost">
                    <span class="cost_num">{{ item['1'] }}</span>
                    <span class="cost_count">{{ item['2'] }}</span>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-col>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">{{ $t('我知道了') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { financialLstApi, financialDetailApi, finaHeaderDataApi, downloadFinancialApi } from '@/api/accounts'
import createWorkBook from '@/utils/newToExcel.js'
import { roterPre } from '@/settings'
import cardsData from '@/components/cards/index'
export default {
  name: 'Record',
  components: { cardsData },
  data() {
    return {
      loading: false,
      roterPre: roterPre,
      timeVal: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      tableForm: {
        page: 1,
        limit: 10,
        date: '',
        type: '1'
      },
      ruleForm: {
        status: '0'
      },
      headeNum: [
        { type: 1, title: leaveuKeyTerms['日账单'] },
        { type: 2, title: leaveuKeyTerms['月账单'] }
      ],
      dialogVisible: false,
      rules: {
        status: [
          { required: true, message: leaveuKeyTerms['请选择对账状态'], trigger: 'change' }
        ]
      },
      reconciliationId: 0,
      cardLists: [],
      accountDetails: {
        date: '',
        charge: {},
        expend: {},
        income: {}
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getList('')
    this.getHeaderData()
  },
  methods: {
    onDetails(date) {
      financialDetailApi(this.tableForm.type, { date: date }).then(res => {
        this.dialogVisible = true
        this.accountDetails = res.data
      }).catch(res => {
        this.$message.error(res.message)
      })
    },
    getHeaderData() {
      finaHeaderDataApi({ date: this.tableForm.date }).then(res => {
        this.cardLists = res.data.stat
      }).catch(res => {
        this.$message.error(res.message)
      })
    },
    async exports(time) {
      let excelData = JSON.parse(JSON.stringify(this.tableForm)), data = []
      excelData.page = 1
      let pageCount = 1
      let lebData = {};
      for (let i = 0; i < pageCount; i++) {
        lebData = await this.downloadAccounts(excelData,time)
        pageCount = Math.ceil(lebData.count/excelData.limit)
        if (lebData.export.length) {
          data = data.concat(lebData.export)
          excelData.page++
        }  
      }
      createWorkBook(lebData.header, lebData.title, data, lebData.foot,lebData.filename);
      return
    },
    /**账单 */
    downloadAccounts(excelData,time) {
      excelData.date = time
      return new Promise((resolve, reject) => {
        downloadFinancialApi(this.tableForm.type,excelData).then((res) => {
          return resolve(res.data)
        })
      })
    },
    // 下载账单
    // downloadAccounts(time) {
    //   downloadFinancialApi(this.tableForm.type, { date: time })
    //     .then((res) => {
    //       const h = this.$createElement
    //       this.$msgbox({
    //         title: '提示',
    //         message: h('p', null, [
    //           h('span', null, '文件正在生成中，请稍后点击"'),
    //           h('span', { style: 'color: teal' }, '导出记录'),
    //           h('span', null, '"查看~ ')
    //         ]),
    //         confirmButtonText: '我知道了'
    //       }).then(action => {
    //         this.$router.push(`${roterPre}/export/list`)
    //       })
    //     }).catch((res) => {
    //       this.$message.error(res.message)
    //     })
    // },
    handleClose() {
      this.dialogVisible = false
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e
      this.tableForm.date = this.timeVal ? this.timeVal.join('-') : ''
      this.getList('')
      this.getHeaderData()
    },
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableForm.page = num || this.tableForm.page
      financialLstApi(this.tableForm).then(res => {
        this.tableData.data = res.data.list
        this.tableData.total = res.data.count
        this.listLoading = false
      }).catch(res => {
        this.listLoading = false
        this.$message.error(res.message)
      })
    },
    pageChange(page) {
      this.tableForm.page = page
      this.getList('')
    },
    handleSizeChange(val) {
      this.tableForm.limit = val
      this.chkName = ''
      this.getList('')
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .tabBox_tit {
    width: 60%;
    font-size: 12px !important;
    margin: 0 2px 0 10px;
    letter-spacing: 1px;
    padding: 5px 0;
    box-sizing: border-box;
  }
  .el-menu-item{
    font-weight: bold;
    color: #333;
  }
  ::v-deep.el-dialog__header{
    text-align: left;
  }
  .el-col{
    position: relative;
    .el-divider--vertical{
      position: absolute;
      height: 100%;
      right: 0;
      top: 0;
      margin: 0;
    }
  }
  .grid-content{
      padding: 0 15px;
      display: block;
      .title,.color_red,.color_gray{
        display: block;
        line-height: 20px;
      }
      .color_red{
        color: red;
        font-weight: bold;
      }
      .color_gray{
        color: #333;
        font-weight: bold;
      }
     .count{
         font-size: 12px;
     }
     .list{
         margin-top: 20px;
         .item{
             overflow: hidden;
             margin-bottom: 10px;
         }
         .name,.cost{
             line-height: 20px;
         }
         .cost{
             text-align: right;
             span{
                 display: block;
             }
         }
         .name,.cost_count{
             font-size: 12px;
         }
         .cost_count{
             margin-top: 10px;
         }
         .cost_num{
             font-weight: bold;
             color: #333;
         }
     }

  }

</style>
