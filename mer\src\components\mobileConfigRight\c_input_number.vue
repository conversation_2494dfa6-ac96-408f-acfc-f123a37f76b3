<template>
    <div class="numbox" v-if="configData">
        <div class="c_row-item">
        <el-col class="label" :class="configData.type=='form'?'on':''" :span="4">
            <span>{{ $t(configData.title) ||'商品数量' }}</span>
        </el-col>
        <el-col :span="19" class="slider-box">
            <!--<Input v-model="configData.val" type="number" placeholder="请输入数量" @on-change="bindChange" style="text-align: right;"/>-->
            <el-input-number v-model="configData.val" :placeholder="$t('请输入数量')" :step="1" :max="100" :min="1" @change="bindChange" style="text-align: right;"></el-input-number>
        </el-col>
        </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
    name: 'c_input_number',
    props: {
        configObj: {
            type: Object
        },
        configNme: {
            type: String
        }
    },
    data () {
        return {
            defaults: {},
            sliderWidth: 0,
            configData: {}
        }
    },
    created () {
        this.defaults = this.configObj
        this.configData = this.configObj[this.configNme]
    },
    watch: {
        configObj: {
            handler (nVal, oVal) {
                this.defaults = nVal
                this.configData = nVal[this.configNme]
            },
            immediate: true,
            deep: true
        }
    },
    methods:{
        bindChange(){
            this.$emit('getConfig', { name: 'number', numVal: this.configData.val })
        }
    }
}
</script>

<style scoped lang="scss">
    ::v-deep .el-input-number{
        width: 100%;
    }       
    .numbox{
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        span{
            width: 80px;
            color: #999;
        }
    }      
    .c_row-item{
        width: 100%;
        .label{
            &.on{
            text-align: right;
            span{
                color: #666;
            }
            }
        }
    }
        
</style>
