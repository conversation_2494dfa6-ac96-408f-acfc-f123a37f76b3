<template>
  <div>
    <div class="index wrapper_1200">
    <!--商品分类-->
    <div class="categoryMain">
      <div class="wrapper_1200">
        <div class="list wrapper_1200 acea-row row-middle">
          <div class="categoryBtn" @click="abc">
            商品分类
          </div>
          <a @click="goUrl(item.link)" class="item" :class="{ 'font-color': item.link === $route.path }"
                     v-for="(item, index) in headerList" :key="index">{{ item.label }}
          </a>
        </div>
      </div>
    </div>
    <!--轮播分类-->
    <div class="wrapper">
      <div class="slider-banner banner">
        <client-only>
          <div v-swiper:mySwiper="swiperOption">
            <div class="swiper-wrapper">
              <div
                @click="goUrl(item.link)"
                class="swiper-slide"
                v-for="(item, index) in swiperData"
                :key="index"
                v-if="index<10"
              >
                <img :src="item.image"/>
              </div>
            </div>
            <div
              class="swiper-pagination paginationBanner"
              slot="pagination"
            ></div>
            <div class="swiper-button-prev" slot="pagination"></div>
            <div class="swiper-button-next" slot="pagination"></div>
          </div>
        </client-only>
      </div>
      <div class="category acea-row row-middle" @mouseleave="leave()">
        <div class="sort" :class="categoryList.length > 10 ? 'sort-scroll' : ''">
          <div
            class="item acea-row row-between-wrapper"
            :class="index === current ? 'bg-color' : '' "
            v-for="(item, index) in categoryList"
            :key="index"
            @mouseenter="enter(index)"
          >
            <div class="name line1" @click="goCate('','',item)">{{ item.cate_name }}</div>
            <div class="iconfont icon-you"></div>
          </div>
        </div>
        <div class="sortCon" v-if="seen && categoryCurrent">
          <div class="erSort">
            <div
              class="item acea-row"
              v-for="(item, index) in categoryCurrent.children"
              v-show="categoryCurrent.children.length>0"
              :key="index" >
              <div class="name line1" @click="goCate('',categoryCurrent,item)">{{ item.cate_name }}</div>
              <div class="cateList">
                <span class="cateItem"
                      @click="goCate(categoryCurrent,item,itemn)"
                      v-for="(itemn, indexn) in item.children" :key="indexn">
                    {{ itemn.cate_name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--秒杀-->
    <div class="seckill acea-row row-middle" v-if="seckillList.length" id="page1">
      <div class="header">
        <div class="title">限时秒杀</div>
        <div class="timeCurrent">
          <span>{{ currentTime }} </span
          >点场
        </div>
        <div class="lines"></div>
        <div class="tip">距离结束还剩</div>
        <countDown
          :is-day="false"
          :tip-text="' '"
          :day-text="' '"
          :hour-text="' : '"
          :minute-text="' : '"
          :second-text="' '"
          :datatime="datatime"
        ></countDown>
      </div>
      <div class="seckillList">
        <div v-swiper:myScroll="swiperScroll">
          <div class="swiper-wrapper">
            <div
              class="swiper-slide item"
              v-for="(item, index) in seckillList"
              :key="index"
            >
              <div class="picTxt" @click="goSeckillDetail(item.product_id, item.stop)">
                <div class="pictrue"><img :src="item.image"/></div>
                <div class="name line1">{{ item.store_name }}</div>
                <div class="money">
                  <span class="font-color">¥{{ item.price }}</span>
                  <span class="ot_price">¥{{ item.ot_price }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-button-prev" slot="pagination"></div>
          <div class="swiper-button-next" slot="pagination"></div>
        </div>
      </div>
    </div>
    <!--精品推荐-->
    <div class="boutique" v-if="boutiqueTwo.length || boutiqueOne" id="page2">
      <div class="publicTitle acea-row row-between-wrapper">
        <div class="title acea-row row-middle">
          <div class="pictrue">
            <img src="../assets/images/boutiqueTitle.png"/>
          </div>
          <div>诚意推荐 品质商品</div>
        </div>
        <nuxt-link :to="{path:'/goods_list',query:{type:'best'}}" class="more acea-row row-center-wrapper">
          更多<span class="iconfont icon-you"></span>
        </nuxt-link>
      </div>
      <div class="list acea-row row-middle">
        <div
          class="oneItem acea-row row-between"
          @click="goDetail(boutiqueOne)"
        >
          <div class="text">
            <div class="name line2">{{ boutiqueOne.store_name }}</div>
            <div class="info line1">{{ boutiqueOne.store_info }}</div>
            <div class="money">
              <span class="font-color">¥{{ boutiqueOne.price }}</span>
            </div>
          </div>
          <div class="pictrue">
            <img :src="boutiqueOne.image" v-if="boutiqueOne.image"/>
            <img src="~assets/images/no_goods.jpg" alt="" v-else>
          </div>
        </div>
        <div
          class="item"
          v-for="(item, index) in boutiqueTwo"
          :key="index"
          v-if="index < 2"
          @click="goDetail(item)"
        >
          <div class="name line1">{{ item.store_name }}</div>
          <div class="font-color">¥{{ item.price }}</div>
          <div class="pictrue">
            <img :src="item.image" v-if="item.image"/>
            <img src="~assets/images/no_goods.jpg" alt="" v-else>
          </div>
        </div>
      </div>
    </div>
    <!--新品首发-->
    <div class="newGoodsList" v-if="newGoods.length > 0 && couponGoods.length == 0" id="page3">
      <div class="publicTitle acea-row row-between-wrapper">
        <div class="title acea-row row-middle">
          <div class="pictrue">
            <img src="../assets/images/newsTitle.png"/>
          </div>
          <div>永远好奇 永远年轻</div>
        </div>
        <nuxt-link :to="{path:'/goods_list',query:{type:'new'}}" class="more acea-row row-center-wrapper">
          更多<span class="iconfont icon-you"></span>
        </nuxt-link>
      </div>
      <div class="list acea-row row-middle">
        <div
          class="item"
          v-for="(item, index) in newGoods"
          v-if="index < 5"
          @click="goDetail(item)"
        >
          <div class="pictrue">
            <img :src="item.image" v-if="item.image"/>
            <img src="~assets/images/no_goods.jpg" alt="" v-else>
          </div>
          <div class="name line1">{{ item.store_name }}</div>
          <div class="money line1">
            <span class="label">优惠价</span>
            <span class="font-color">¥{{ item.price }}</span>
          </div>
          <div class="stock">已抢 {{ item.sales }}{{ item.unit_name }}</div>
        </div>
      </div>
    </div>
    <!--领券中心-->
    <div class="newGoods" v-if="couponGoods.length>0 && newGoods.length>0" id="page3">
      <div class="count">
        <div class="publicTitle acea-row row-between-wrapper">
          <div class="title acea-row row-middle">
             <div class="title-text">
              领券<span class="color-red">中心</span>
            </div>
            <div>精选爆款  天天低价</div>
          </div>
          <nuxt-link :to="{path:'/coupon_center'}" class="moreBtn acea-row row-center-wrapper">
            <span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="list acea-row row-middle">
          <div
            class="item"
            v-for="(item, index) in couponGoods"
          >
            <div class="pictrue">
              <img :src="item.ProductLst[0]['image']" v-if="item.ProductLst && item.ProductLst[0]"/>
              <img src="~assets/images/no_goods.jpg" alt="" v-else>
            </div>
            <nuxt-link :to="{path:'/coupon_center'}" class="item-coupon">
              <div class="coupon-text line1">满{{item.use_min_price}}元减{{item.coupon_price}}元</div>
              <div  class="coupon-btn" >立即领取</div>
            </nuxt-link>
          </div>
        </div>
      </div>
      <div class="count">
        <div class="publicTitle acea-row row-between-wrapper">
          <div class="title acea-row row-middle">
             <div class="title-text">
              首发新品
            </div>
            <div>永远好奇 永远年轻</div>
          </div>
          <nuxt-link :to="{path:'/goods_list',query:{type:'new'}}" class="moreBtn acea-row row-center-wrapper">
            <span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="list acea-row row-middle">
          <div
            class="item"
            v-for="(item, index) in newGoods"
            v-if="index < 3"
            @click="goDetail(item)"
          >
            <div class="pictrue">
              <img :src="item.image" v-if="item.image"/>
              <img src="~assets/images/no_goods.jpg" alt="" v-else>
            </div>
            <div class="name line1">{{ item.store_name }}</div>
            <div class="money line1">
              <span class="label">优惠价</span>
              <span class="font-color">¥{{ item.price }}</span>
            </div>
            <div class="stock">已抢 {{ item.sales }}{{ item.unit_name }}</div>
          </div>
        </div>
      </div>   
    </div>
    <!--推荐单品-->
    <div class="Recommended acea-row row-middle" v-if="singleRecommend.length" id="page5">
      <div class="list">
        <div class="publicTitle acea-row row-between-wrapper">
          <div class="title acea-row row-middle">
            <div class="title-text">
              推荐<span class="color-red">单品</span>
            </div>
            <div class="title-intr">精选爆款 天天低价</div>
          </div>
          <nuxt-link :to="{path:'/goods_list',query:{type:'good'}}" class="moreBtn">
            <span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="recommended-items">
          <div v-swiper:mySwiper2="swiperOption2" class="swiper-container">
            <div class="swiper-wrapper">
              <div
                class="swiper-slide"
                v-for="(item, index) in singleRecommend"
                :key="index"
                v-if="index<10"
                @click="goDetail(item)"
              >
                <img class="recommend-img" :src="item.image"/>
                <div class="recommend-text">
                  <div class="name line1">{{ item.store_name }}</div>
                  <div class="price">¥<span>{{ item.price }}</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="list">
        <div class="publicTitle acea-row row-between-wrapper">
          <div class="title acea-row row-middle">
            <div class="title-text">
              品牌<span class="color-red">好店</span>
            </div>
            <div class="title-intr">优质好店等你来选</div>
          </div>
          <nuxt-link :to="{path:'/shop_street'}" class="moreBtn">
            <span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="list_count">
          <nuxt-link :to="{path: '/store',query:{id:item.mer_id}}" v-for="(item, index) in brandStoreList" :key="index"
                     class="item acea-row row-between-wrapper">
            <div class="list-product">
              <div class="name line2">{{ item.mer_name }}</div>
              <span v-if="item.is_trader" class="trader">自营</span>
              <div class="attention-count">{{ item.care_count }}人关注</div>
            </div>
            <div class="picture">
              <img :src="(item.recommend && item.recommend.length) ? item.recommend[0].image : item.mer_avatar" alt="">
            </div>
          </nuxt-link>
        </div>
      </div>
      <div class="list">
        <div class="publicTitle acea-row row-between-wrapper">
          <div class="title acea-row row-middle">
            <div class="title-text">
              热门<span class="color-red">榜单</span>
            </div>
            <div class="title-intr">大家都帮你选好了</div>
          </div>
          <nuxt-link :to="{path:'/goods_list',query:{type:'hot'}}" class="moreBtn">
            <span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="list_count">
          <div v-for="(item, index) in popularList" :key="index" @click="goDetail(item)"
               class="item hot-list acea-row row-between-wrapper">
            <div class="top-num"><img :src='"../assets/images/top-list"+index+".png"' alt=""></div>
            <div class="picture">
              <img :src="item.image" alt="">
            </div>
            <div class="name line2">{{ item.store_name }}</div>
          </div>
        </div>
      </div>
    </div>
    <!--分类-->
    <div v-if="classifyList.length" class="classify" id="page4">
      <div class="classify-title">
        <span>分类广场</span>
      </div>
      <div class="list" v-for="(item, index) in classifyList" :key="index">
        <div class="list-title">{{ item.title }}
          <nuxt-link :to="{path:'/goods_cate',query:{sid:item.cid,name:item.title}}" class="more acea-row row-center-wrapper" style="float: right">
            更多<span class="iconfont icon-you"></span>
          </nuxt-link>
        </div>
        <div class="list-count acea-row">
          <a class="classify-banner" @click="goUrl(item.left_link)">
            <img :src="item.left_image" alt="">
          </a>
          <div v-if="item.list.length>0" class="classify-items acea-row">
            <div class="classify-item" v-for="(itemn, indexn) in item.list" :key="indexn"
                 @click="goDetail(itemn)">
              <div class="picture">
                <img :src="itemn.image" alt="">
              </div>
              <div class="item-text">
                <div class="item-name line2">
                  <span v-if="itemn.merchant.is_trader && itemn.product_type == 0" class="trader">自营</span><span v-else-if="itemn.product_type == 1" class="trader">秒杀</span>{{ itemn.store_name }}
                </div>
                <div class="item-price acea-row">
                  <div class="price"><span>¥</span>{{ itemn.price }}</div>
                  <div v-if="itemn.issetCoupon" class="coupon">券</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a class="list-banner"  @click="goUrl(item.bottom_link)">
          <img :src="item.bottom_image" alt="">
        </a>
      </div>
    </div>
    <div
      class="loadingicon acea-row row-center-wrapper"
      v-if="classifyList.length && classifyList.length >= limit"
    >
      <span class="loading iconfont icon-jiazai" v-if="!pullRefreshss"></span
      >{{ title }}
    </div>
    <div class="floatWindow">
      <div class="list">
        <div class="item" v-if="seckillList.length" @click="goSpecify(1)">
          <div>限时<br/>秒杀</div>
        </div>
        <div class="item" v-if="boutiqueTwo.length" @click="goSpecify(2)">
          <div>精品<br/>推荐</div>
        </div>
        <div class="item" v-if="newGoods.length" @click="goSpecify(3)">
          <div>火爆<br/>新品</div>
        </div>
        <div class="item" v-if="classifyList.length" @click="goSpecify(4)">
          <div>分类<br/>广场</div>
        </div>
        <div v-if="sys_service.sys_service_switch == 1 || sys_service.sys_service_switch == 2 || sys_service.sys_service_switch == 4" class="item" @mouseleave="wxCodeHide">
          <div @mouseenter="wxCode">
            <div class="iconfont icon-lianxikefu"></div>
            <div>客服</div>
          </div>
          <div class="itemCon" v-if="isService">
            <div class="ewm acea-row row-center-wrapper" v-if="sys_service.sys_service_switch == 2">
              <div class="pictrue">
                <div class="arrow"></div>
                <div class="iconfont icon-dadianhua01"></div>
              </div>
              <div class="tip">客服电话<br><span class="tel">{{sys_service.sys_phone}}</span></div>
            </div>
            <div class="ewm acea-row row-center-wrapper" v-if="sys_service.sys_service_switch == 1">
              <div class="pictrue">
                <div class="arrow"></div>
                <div class="iconfont icon-kefu2"></div>
              </div>
              <div class="tip">在线客服<br><span @click="chatShow" class="chatShowBtn">联系客服</span></div>
            </div>
            <div class="ewm acea-row row-center-wrapper" v-if="sys_service.sys_service_switch == 4">
              <div class="pictrue">
                <div class="arrow"></div>
                <div class="iconfont icon-kefu2"></div>
              </div>
              <div class="tip">点击跳转客服<br><a :href="sys_service.customer_link" target="blank" class="chatShowBtn">联系客服</a></div>
            </div>
          </div>
        </div>
        <div class="item" @click="goTop">
          <div class="iconfont icon-huidaodingbu1"></div>
          <div>回到顶部</div>
        </div>
      </div>
    </div>
  </div>
  <chat-room
      v-if="chatPopShow"
      :chatId="0"
      @close="chatPopShow = false"
    ></chat-room>
    <!--优惠券弹窗-->
    <div v-if="isReceive" class="coupon-poupon">
      <div class="poupon-count">
        <span class="closeBtn iconfont icon-guanbi" @click="closeCoupon"></span>
        <div class="coupon-image">
          <img src="~assets/images/coupon-receive.png" alt="">
        </div>
        <div class="coupon-title">
          <div class="text">恭喜您，领取成功</div>
          <div class="message">感谢您的参与，祝您购物愉快</div>
        </div>
        <nuxt-link :to="{path:'/coupon_center'}" class="useBtn">
            立即使用
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
import ChatRoom from "@/components/ChatRoom";
import {mapGetters} from "vuex";

export default {
  name: "index",
  auth: false,
  components: {
    countDown,
    ChatRoom
  },
  data() {
    return {
      chatPopShow: false,
      seen: false,
      current: -1,
      swiperData: [],
      headerList: [],
      categoryList: [],
      categoryCurrent: null,
      datatime: 0,
      currentTime: "00:00",
      seckillList: [],
      recommendList: [],
      boutiqueOne: null,
      boutiqueTwo: [],
      newGoods: [],
      classifyList: [], //初始数据代码
      brandStoreList: [], //品牌好店
      popularList: [],
      pullRefreshss: true,
      page: 1, //代表页面的初始页数
      limit: 3,
      scollY: null, // 离底部距离有多少
      pageTotal: 0, //总页数
      title: "下拉加载更多",
      swiperOption: {
        pagination: {
          el: ".paginationBanner",
          clickable: true
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        },
        autoplay: {
          disableOnInteraction: false,
          delay: 5000
        },
        loop: true,
        speed: 1000,
        observer: true,
        observeParents: true
      },
      swiperScroll: {
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        },
        freeMode: true,
        freeModeMomentum: false,
        slidesPerView: "auto",
        observer: true,
        observeParents: true
      },
      swiperOption2: {
        autoplay: {
          disableOnInteraction: false,
          delay: 3000
        },
        loop: true,
        speed: 2000,
        observer: true,
        slidesPerView: 'auto',
        observeParents: true,
        centeredSlides: true
      },
      isService: false,
      isReceive: false,
      sys_service: {}
    };
  },
  computed: mapGetters(['openService']),
  async asyncData({app, error}) {
    try {
      let [categoryMsg, seckillMsg, boutiqueTwo, newGoods, couponGoods, singleRecommend, brandStoreList, popularList, homeList, seckillList, classifyList] = await Promise.all([
        app.$axios.get("/api/store/product/category/lst"),
        app.$axios.get("/api/store/product/seckill/select"),
        app.$axios.get("/api/product/spu/hot/best", {
          params: {
            page: 1,
            limit: 4,
            common: 1
          }
        }),
        app.$axios.get("/api/product/spu/hot/new", {
          params: {
            page: 1,
            limit: 5,
            common: 1
          }
        }),
        app.$axios.get("/api/coupon/getlst", {
          params: {
            product: 1,
            is_pc:1,
          }
        }),
        app.$axios.get("/api/product/spu/hot/good", {
          params: {
            page: 1,
            limit: 5,
            common: 1
          }
        }),
        app.$axios.get("/api/store/merchant/lst", {
          params: {
            page: 1,
            limit: 2,
            is_best: 1
          }
        }),
        app.$axios.get("/api/product/spu/hot/hot", {
          params: {
            page: 1,
            limit: 3,
            common: 1
          }
        }),
        app.$axios.get("/api/pc/home"),
        app.$axios.get("/api/store/product/seckill/lst", {
          params: {
            page: 1,
            limit: 10
          }
        }),
        app.$axios.get("/api/pc/rec_list", {
          params: {
            page: 1, limit: 3
          }
        }),
      ]);
      return {
        categoryList: categoryMsg.data.list,
        seckillTime: seckillMsg.data,
        boutiqueOne: boutiqueTwo.data.list.length ? boutiqueTwo.data.list.shift() : null,
        boutiqueTwo: boutiqueTwo.data.list,
        couponGoods: couponGoods.data.list,
        newGoods: newGoods.data.list,
        singleRecommend: singleRecommend.data.list,
        brandStoreList: brandStoreList.data.list,
        popularList: popularList.data.list,
        swiperData: homeList.data.pc_home_banner,
        headerList: homeList.data.pc_home_tab,
        seckillList: seckillList.data.list,
        classifyList: classifyList.data.list,
        pageTotal: classifyList.data.count,
      };
    } catch (e) {
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },
  fetch({store}) {
    store.commit("isBanner", true);
    store.commit("isHeader", true);
    store.commit("isFooter", true);

  },
  head() {
    return {
      title: this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.classifyList = this.parseLst(this.classifyList);
  },
  mounted() {
    this.getServiceType();
    if(this.seckillTime && this.seckillList.length>0)this.getSeckillIndexTime();
    this.pullRefresh();
  },
  beforeDestroy() {
    window.onscroll = null;
  },
  methods: {
    getServiceType() {
      let that = this;
      that.$axios.get("/api/config").then(res => {
        that.sys_service = res.data.service_type;
        that.$store.commit('sysService', res.data.service_type);
      }).catch(err => {
        that.$message.error(err);
      });
    },
    chatShow() {
      if(this.$auth.loggedIn){
        this.$axios
        .get(`/api/has_service/0`)
        .then((res) => {
          this.chatPopShow = true;
        })
        .catch((err) => {
          this.$message.error(err);
        });   
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    parseLst(lst) {
      return lst.map(item => {
        if (item.left_image) {
          item.list = item.list.splice(0, 8);
        }
        return item;
      });
    },
    goSpecify(key) {
      var PageId = document.querySelector('#page' + key)
      window.scrollTo({
        'top': PageId.offsetTop,
        'behavior': 'smooth'
      })
    },
    // 商品分类
    goCate(categoryCurrent,item,itemn) {
      let name = '';
      if(categoryCurrent && item){
          name = categoryCurrent.cate_name+'_'+categoryCurrent.store_category_id+','+item.cate_name+'_'+item.store_category_id+','+itemn.cate_name+'_'+itemn.store_category_id
      }else{
        if(item){
          name = item.cate_name+'_'+item.store_category_id+','+itemn.cate_name+'_'+itemn.store_category_id
        }else{
          name = itemn.cate_name+'_'+itemn.store_category_id
        }
      }
      this.$router.push({
        path: '/goods_cate',
        query: {
          sid: itemn.store_category_id,
          name: encodeURI(name)
        }
      });
    },
    abc(){
      console.log(this.headerList);
    },
    /*点击轮播图跳转*/
    goUrl(url){
      if(!url)return;
      let isHttp = url.substring(0,4) == 'http' ? true : false;
      if(isHttp){
        location.href = url;
      }else{
        this.$router.push({ path: url });
      }
    },
    getSeckillIndexTime() {
      let seckillTime = this.seckillTime;
      this.datatime = seckillTime.seckillEndTime;
      this.currentTime =
        seckillTime.seckillTime[seckillTime.seckillTimeIndex].start_time;
      let id = seckillTime.seckillTime[seckillTime.seckillTimeIndex].seckill_time_id;
    },
    enter(index) {
      this.seen = true;
      this.current = index;
      this.categoryCurrent = this.categoryList[index];
    },
    leave() {
      this.seen = false;
      this.current = -1;
    },
    goTop() {
      (function n() {
        var t = document.documentElement.scrollTop || document.body.scrollTop;
        if (t > 0) {
          window.requestAnimationFrame(n);
          window.scrollTo(0, t - t / 5)
        }
      })()
    },
    wxCode() {
      this.isService = true
    },
    wxCodeHide() {
      this.isService = false
    },
    getClassifyList() {
      let _this = this,
        currentPage = {page: this.page, limit: this.limit};
      _this.$axios
        .get("/api/pc/rec_list", {
          params: currentPage
        })
        .then(function (res) {
          _this.pageTotal = res.data.count;
          // 请求完成后，把得到的数据拼接到当前dom里面
          _this.classifyList = _this.classifyList.concat(_this.parseLst(res.data.list));
        })
        .catch(function (err) {
          _this.$message.error(err);
        });
    },
    // 下拉加载ajax
    pullRefresh: function () {
      let _this = this;
      window.onscroll = function () {
        _this.scrollChange();
      };
    },
    scrollChange: function () {
      var _this = this;
      this.scollY =
        this.comsys.getScrollTop() +
        this.comsys.getWindowHeight() -
        this.comsys.getScrollHeight();
      // 把下拉刷新置为false，防止多次请求
      if (this.scollY >= -50) {
        if (this.page > Math.ceil(this.pageTotal / this.limit)) {
          this.title = "已没有更多数据";
          this.pullRefreshss = true;
          return false;
        }
        if (!this.pullRefreshss) {
          return false;
        }
        _this.pullRefreshss = false;
        this.title = "正在加载中....";
        // 加页码数
        this.page++;
        _this.getClassifyList();
      } else {
        // 其他时候把下拉刷新置为true
        _this.pullRefreshss = true;
        this.title = "下拉加载更多";
      }
    },
    goDetail: function (item) {
      if(item.product_type == 1){
        this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time } });
      }else{
        this.$router.push({ path: '/goods_detail/'+item.product_id });
       }
    },
    goSeckillDetail: function (productId, time) {
      this.$router.push({path: '/goods_seckill_detail/'+productId, query: { time: time, time_id: '' }});
    },
    //立即领取
    getReceive(id) {
      let _this = this
      _this.$axios
        .post("/api/coupon/receive/"+id)
        .then(function (res) {
          _this.isReceive = true
        })
        .catch(function (err) {
          _this.$message.error(err);
        });
    },
    closeCoupon() {
      this.isReceive = false;
    }
  }
};
</script>

<style scoped lang="scss">

.sort-scroll::-webkit-scrollbar{
  width: 0;
}
.sort-scroll::-webkit-scrollbar-track{
  background: transparent;
}
.sort-scroll::-webkit-scrollbar-thumb{
  background: transparent;
}
.sort-scroll::-webkit-scrollbar-corner{
  background: transparent;
}
.coupon-poupon{
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}
.poupon-count{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 54px 0 60px;
  background: #fff;
  width: 370px;
  height: 400px;
  text-align: center;
  .closeBtn {
    color: #939393;
    position: absolute;
    top: 20px;
    right: 20px;
    text-align: center;
    font-size: 20px;
    cursor: pointer;
  }
  .coupon-image{
    text-align: center;
    img{
      display: inline-block;
      width: 120px;
      height: 120px;
    }
  }
  .coupon-title {
    margin-top: 24px;
    .text{
      color: #E93323;
      font-size: 20px;
    }
    .message{
      margin-top: 10px;
      color: #939393;

    }
  }
  .useBtn {
    margin: 46px auto 0;
    width: 150px;
    height: 42px;
    line-height: 42px;
    border-radius: 4px;
    background: #E93323;
    display: block;
    color: #fff;
  }
}
.floatWindow {
  position: fixed;
  right: 0;
  bottom: 15%;
  width: 70px;
  z-index: 999;
  cursor: pointer;
  background-color: #fff;
  box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
  .list {
    .item {
      position: relative;
      width: 100%;
      height: 74px;
      text-align: center;
      font-size: 12px;
      color: #5C5C5C;
      padding: 23px 0;
      line-height: 18px;
      &:hover {
        color: #E93323;
      }
      .iconfont {
        margin-bottom: 5px;
        font-size: 25px;
      }
      & ~ .item {
        &:before {
          position: absolute;
          content: ' ';
          width: 48px;
          height: 1px;
          background-color: #F0F0F0;
          top: 0;
          left: 50%;
          margin-left: -24px;
        }
      }
      .itemCon {
        right: 100%;
        position: absolute;
        top: 0;
        padding-right: 20px;
        .ewm {
          width: 195px;
          height: 60px;
          border: 1px solid #eeeeee;
          background-color: #fff;
          padding: 10px 15px;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          align-items: center;
          color: #282828;
          .tip {
            font-size: 14px;
            width: 120px;
            text-align: left;
            line-height: 20px;
            color: rgb(189, 189, 189);
            .tel{
              color: #282828;
            }
            .chatShowBtn{
              display: inline-block;
              width: 60px;
              height: 20px;
              line-height: 20px;
              background-color: #ff7b00;
              border-radius: 100px;
              color: #fff;
              font-size: 12px;
              text-align: center;
            }
          }
          .pictrue {
            vertical-align: middle;

            .iconfont {
              margin-bottom: 0;
              color: #282828;
            }
            .arrow {
              position: absolute;
              right: 5px;
              top: 23px;
              width: 0px;
              height: 0px;
              border: 8px solid transparent;
              border-left-color: #eee;
              &:before {
                position: absolute;
                left: -8px;
                top: -7px;
                content: "";
                width: 0px;
                height: 0px;
                border: 7px solid transparent;
                border-left-color: #fff;
              }
            }
          }
        }
      }
    }
  }
}
.line2 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: pre-wrap;
}
.categoryMain {
  background: #fff;
  position: relative;
  .categoryBtn {
    width: 208px;
    height: 44px;
    line-height: 44px;
    color: #fff;
    background-color: #E93323;
    text-align: center;
    cursor: pointer;
  }
  .item {
    padding: 11.5px 10px;
    margin-right: 13px;
    color: #282828;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    min-width: 90px;
    display: inline-block;
    text-align: center;
    &:hover {
      color: #E93323;
    }
  }
}
.index {
  .publicTitle {
    margin-top: 30px;
    .title {
      color: #8b8b8b;
      .pictrue {
        width: 110px;
        height: 30px;
        margin-right: 13px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .list-title .more, .publicTitle .more{
    width: 58px;
    height: 24px;
    border: 1px solid #c6c6c6;
    color: #818181;
    font-size: 12px;
    cursor: pointer;
    .iconfont {
      font-size: 10px;
    }
  }
  .wrapper {
    height: 420px;
    position: relative;
    cursor: pointer;
    .banner {
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .category {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
      height: 100%;
    }
    .sort {
      width: 208px;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      color: #fff;
      padding: 14px 0;
      .item {
        height: 39px;
        padding: 0 21px;
        .name {
          width: 150px;
        }
        .iconfont {
          font-size: 10px;
        }
      }
      &.sort-scroll{
        overflow-y: scroll;
        .name {
          width: 125px;
        }
      }
    }
    .sortCon {
      width: 664px;
      height: 100%;
      background-color: #fff;
      box-shadow: 5px 1px 10px rgba(0, 0, 0, 0.2);
      border: 1px solid #f2f2f2;
      border-left: 0;
      border-right: 0;
      padding: 20px 0;
      .title {
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f1f1;
        .font-color {
          padding-bottom: 8px;
          font-size: 16px;
          border-bottom: 2px solid #e93323;
        }
      }
      .erSort {
        overflow-x: hidden;
        overflow-y: auto;
        max-height: 400px;
        .item {
          -webkit-justify-content: space-between;
          justify-content: space-between;
          margin-bottom: 20px;
          .name {
            color: #282828;
            margin-left: 14px;
            text-align: right;
            width: 88px;
            font-weight: bold;
            &:hover{
              color: #E93323;
            }
          }
          .pictrue {
            width: 50px;
            height: 50px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        .cateList {
          width: 525px;
          .cateItem {
            text-align: center;
            padding: 0 20px;
            display: inline-block;
            color: #888888;
            font-size: 12px;
            margin-bottom: 12px;
            &:hover {
              color: #e93323;
            }
          }
        }
      }
    }
  }
  .seckill {
    margin-top: 30px;
    .header {
      background: url("../assets/images/skillBg.jpg") no-repeat;
      background-size: 100% 100%;
      width: 208px;
      height: 266px;
      color: #fff;
      text-align: center;
      padding: 44px 0;
      font-size: 16px;
      .title {
        font-size: 28px;
        font-weight: bold;
      }
      .timeCurrent {
        margin-top: 20px;
        span {
          font-weight: bold;
        }
      }
      .lines {
        width: 17px;
        height: 2px;
        background: #ffffff;
        margin: 10px auto 0 auto;
      }
      .tip {
        margin-top: 10px;
      }
      .time {
        margin-top: 20px;
      }
    }
    .seckillList {
      width: 992px;
      height: 266px;
      background-color: #fff;
      position: relative;
      .item {
        cursor: pointer;
        display: inline-block;
        width: 248px;
        position: relative;
        padding: 21px 0;
        &:hover {
          .name {
            color: #E93323;
          }
        }
        .picTxt {
          width: 164px;
          margin: 0 auto;
        }
        & ~ .item:before {
          content: " ";
          position: absolute;
          width: 1px;
          height: 98px;
          background-color: #f4f4f4;
          top: 50%;
          margin-top: -49px;
        }
        .pictrue {
          width: 164px;
          height: 164px;
          margin: 0 auto;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .name {
          color: #282828;
          text-align: center;
          width: 164px;
          margin: 10px auto 0 auto;
        }
        .money {
          text-align: center;
          width: 164px;
          margin: 12px auto 0 auto;
          .font-color {
            font-weight: bold;
            font-size: 16px;
          }
          .y_money {
            color: #a3a3a3;
            text-decoration: line-through;
            margin-left: 6px;
          }
        }
        .ot_price{
          text-decoration: line-through;
          color: #A3A3A3;
          display: inline-block;
          margin-left: 2px;
        }
      }
    }
  }
  .boutique {
    .list {
      margin-top: 14px;
      .oneItem {
        padding: 28px;
        width: 586px;
        height: 250px;
        background: url("../assets/images/boutique1.jpg") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        &:hover {
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
        .text {
          width: 310px;
          .name {
            font-size: 18px;
            color: #282828;
          }
          .info {
            font-size: 16px;
            color: #a3a3a3;
            margin-top: 8px;
          }
          .money {
            margin-top: 26px;
            .font-color {
              font-size: 20px;
              font-weight: bold;
            }
            .y_money {
              color: #a3a3a3;
              margin-left: 12px;
              text-decoration: line-through;
            }
          }
        }
        .pictrue {
          width: 194px;
          height: 194px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .item {
        width: 287px;
        height: 250px;
        background: url("../assets/images/boutique2.jpg") no-repeat;
        background-size: 100% 100%;
        padding: 28px;
        position: relative;
        margin-left: 20px;
        cursor: pointer;
        &:hover {
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
        .name {
          font-size: 18px;
        }
        .font-color {
          font-size: 20px;
          font-weight: bold;
          margin-top: 12px;
        }
        .y_money {
          color: #a3a3a3;
          text-decoration: line-through;
          margin-top: 4px;
        }
        .pictrue {
          width: 130px;
          height: 130px;
          position: absolute;
          right: 28px;
          bottom: 28px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .newGoodsList {
    .list {
      margin-top: 14px;

      .item {
        width: 224px;
        height: 320px;
        background-color: #fff;
        padding: 16px 16px 22px 16px;
        cursor: pointer;

        &:hover {
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }

        .pictrue {
          width: 192px;
          height: 192px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .name {
          margin-top: 12px;
        }

        .money {
          margin-top: 10px;
          display: flex;
          align-items: center;

          .label {
            background: url("../assets/images/label.jpg") no-repeat;
            background-size: 100% 100%;
            width: 45px;
            height: 20px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            line-height: 20px;
            display: inline-block;
          }

          .font-color {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
          }

          .y_money {
            font-size: 12px;
            color: #a3a3a3;
            margin-left: 10px;
            text-decoration: line-through;
          }
        }

        .stock {
          color: #969696;
          font-size: 12px;
          margin-top: 14px;
        }

        & ~ .item {
          margin-left: 20px;
        }
      }
    }
  }
  .newGoods {
    display:flex;
    align-items:center;
    justify-content: space-between;
    margin-top: 30px;
    .count{
      width: 586px;
      height: 345px;
      padding: 20px;
      background: #fff;
      &:nth-child(2n){
        width: 594px;
        .item{
          & ~ .item {
          margin-left: 16px;
        }
        }
      }
      .publicTitle{
        margin-top: 0;
      }
    }
    .moreBtn {
      float: right;
      width: 19px;
      height: 19px;
      text-align: center;
      border: 1.5px solid #E93323;
      border-radius: 100%;
      line-height: 19px;
      span {
        color: #E93323;
        font-size: 10px;
        font-weight: bold;
      }
    }
    .title-text {
      font-weight: bold;
      color: #333333;
      font-size: 24px;
      margin-right: 10px;
      .color-red {
        color: #E93323;
      }
    }
    .list {
      margin-top: 17px;
      .item {
        width: 174px;
        background-color: #fff;
        cursor: pointer;
        .pictrue {
          width: 174px;
          height: 174px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .name {
          margin-top: 10px;
        }
        .money {
          margin-top: 10px;
          display: flex;
          align-items: center;
          .label {
            background: url("../assets/images/label.jpg") no-repeat;
            background-size: 100% 100%;
            width: 45px;
            height: 20px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            line-height: 20px;
            display: inline-block;
          }
          .font-color {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
          }
          .y_money {
            font-size: 12px;
            color: #a3a3a3;
            margin-left: 10px;
            text-decoration: line-through;
          }
        }
        .stock {
          color: #969696;
          font-size: 12px;
          margin-top: 10px;
        }
        & ~ .item {
          margin-left: 12px;
        }
      }
      .item-coupon{
        width: 174px;
        height: 66px;
        background-image: url(../assets/images/index-coupon.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin-top: 13px;
        text-align: center;
        padding: 10px;
        display: block;
        box-sizing: border-box;
        .coupon-text{
          font-size: 16px;
          font-weight: bold;
          color: #E93323;
        }
        .coupon-btn{
          margin-top: 7px;
          font-size: 12px;
          color: #ffffff;
          width: 70px;
          height: 20px;
          line-height: 20px;
          background: #E93323;
          border-radius: 10px;
          display: inline-block;
        }
      }
    }
  }
  .Recommended {
    margin-top: 30px;
    .publicTitle {
      margin-top: 0;
      padding: 0 27px;
      width: 100%;
      .moreBtn {
        float: right;
        width: 19px;
        height: 19px;
        text-align: center;
        border: 1.5px solid #E93323;
        border-radius: 100%;
        line-height: 19px;
        span {
          color: #E93323;
          font-size: 10px;
          font-weight: bold;
        }
      }
    }
    .title-text {
      font-weight: bold;
      color: #333333;
      font-size: 24px;
      margin-right: 10px;
      .color-red {
        color: #E93323;
      }
    }
    .title-intr {
      position: relative;
      top: 3px;
    }
    .list {
      width: 390px;
      height: 400px;
      padding: 26px 0;
      background-color: #fff;
      .list_count {
        padding: 0 27px;
        margin-top: 30px;
        cursor: pointer;
        .item {
          width: 100%;
          height: 136px;
          box-sizing: border-box;
          padding: 0 15px 0 20px;
          background: url("../assets/images/storeBg.png") no-repeat;
          align-items: center; /*定义body的元素垂直居中*/
          -webkit-box-pack: justify;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          &:first-child {
            margin-bottom: 14px;
          }
        }
        .hot-list {
          height: 84px;
          margin-bottom: 17px;
          background-image: none;
          &:last-child {
            margin-bottom: 0;
          }
          .top-num {
            width: 36px;
            height: 44px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .name {
            width: 136px;
          }
          .picture {
            width: 84px;
            height: 84px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .list-product {
        width: 183px;
        .name {
          color: #282828;
          font-size: 14px;
          line-height: 20px;
        }
        .trader {
          display: block;
          color: #E93323;
          font-size: 12px;
          border: 1px solid #E93323;
          width: 32px;
          margin-top: 7px;
          text-align: center;
          border-radius: 2px;
        }
        .attention-count {
          color: #8B8B8B;
          font-size: 13px;
          margin-top: 20px;
        }
      }
      .picture {
        width: 106px;
        height: 106px;
        img {
          width: 100%;
          height: 100%;
          border-radius: 3px;
        }
      }
      & ~ .list {
        margin-left: 15px;
      }
    }
    .recommended-items {
      margin-top: 32px;
      .swiper-container {
        .recommend-text {
          opacity: 0;
        }
        .swiper-slide {
          display: block;
          width: 180px;
          height: 280px;
          margin-right: 15px;
          position: relative;
          top: 20px;
          transition: all .3s ease;
          img {
            width: 100%;
            height: 160px;
          }
        }
        .swiper-slide-active {
          top: 0;
          left: 0;
          .recommend-text {
            opacity: 1;
            position: absolute;
            width: 100%;
            text-align: center;
            bottom: 0;
            .name {
              color: #282828;
              font-size: 18px;
              line-height: 30px;
              margin-bottom: 10px;
            }
            .price {
              color: #E93323;
              font-size: 14px;
              font-weight: bold;
              span {
                font-size: 20px;
              }
            }
          }
          img {
            height: 180px !important;
          }
        }
      }
    }
  }
  .classify {
    width: 1200px;
    padding: 54px 0 10px 0;
    .list-title {
      font-size: 20px;
      color: #282828;
      margin-top: 30px;
    }
    .classify-title {
      width: 100%;
      height: 30px;
      background-image: url("../assets/images/classified-title.png");
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
      text-align: center;
      span {
        display: inline-block;
        font-size: 22px;
        color: #333333;
        font-weight: bold;
      }
    }
    .list-count {
      width: 1200px;
      margin-top: 15px;
      -webkit-justify-content: space-between;
      justify-content: space-between;
    }
    .classify-banner {
      width: 224px;
      height: 640px;
      margin-bottom: 20px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .classify-items {
      width: 956px;
      .classify-item {
        width: 224px;
        height: 310px;
        padding: 16px;
        background: #fff;
        margin-right: 20px;
        margin-bottom: 20px;
        cursor: pointer;
        .picture {
          width: 192px;
          height: 192px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        &:nth-child(4n) {
          margin-right: 0;
        }
        &:hover {
          box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
      }
      .item-text {
        margin-top: 10px;
        .item-name {
          color: #666666;
          font-size: 14px;
          line-height: 22px;
          .trader {
            color: #fff;
            background-color: #E93323;
            display: inline-block;
            width: 32px;
            height: 18px;
            line-height: 18px;
            text-align: center;
            border-radius: 2px;
            margin-right: 5px;
            font-size: 12px;
          }
        }
        .item-price {
          margin-top: 12px;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          .price {
            color: #E93323;
            font-size: 18px;
            font-weight: bold;
            span {
              font-size: 14px;
            }
          }
          .coupon {
            position: relative;
            width: 20px;
            height: 20px;
            color: #E93323;
            text-align: center;
            border-radius: 2px;
            &:before {
              content: "";
              display: block;
              width: 20px;
              height: 20px;
              background: linear-gradient(330deg, #E75543 0%, #F46753 100%);
              border-radius: 2px;
              opacity: 0.14;
              position: absolute;
              top: 0;
              left: 0;
            }
          }
        }
      }
    }
    .list-banner {
      margin-top: 30px;
      width: 1200px;
      height: 120px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
