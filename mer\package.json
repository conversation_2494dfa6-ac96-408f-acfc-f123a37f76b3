{"name": "vue-element-mer", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "postinstall": "patch-package"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@form-create/element-ui": "^2.5.35", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/resource": "^6.1.17", "@fullcalendar/resource-timeline": "^6.1.17", "@fullcalendar/vue": "^6.1.17", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "cos-js-sdk-v5": "^1.4.5", "crypto-js": "^4.1.1", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.15.14", "exceljs": "^4.2.0", "file-saver": "2.0.1", "fuse.js": "3.4.4", "html2canvas": "^1.1.0", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jsonp": "^0.2.1", "jszip": "3.2.1", "less": "^2.7.3", "less-loader": "^4.1.0", "moment": "^2.29.1", "monaco-editor": "^0.19.3", "node-sass": "^4.13.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "print-js": "^1.6.0", "printjs": "^1.1.0", "qiniu-js": "^2.5.5", "qrcodejs2": "0.0.2", "screenfull": "4.2.0", "showdown": "1.9.0", "sortablejs": "^1.15.0", "v-viewer": "^1.5.1", "view-design": "^4.7.0", "vue": "2.6.10", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "1.0.13", "vue-jsonp": "^2.0.0", "vue-qr": "^2.5.0", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-ueditor-wrap": "^2.4.1", "vuedraggable": "2.20.0", "vuex": "3.1.0", "wangeditor": "^4.7.12", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-import": "^1.13.8", "cache-loader": "^2.0.1", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "patch-package": "^6.2.2", "plop": "2.3.0", "runjs": "^4.3.2", "sass-loader": "^7.3.1", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-lazyload": "^1.3.3", "vue-template-compiler": "2.6.10", "vuex-persist": "^3.1.3"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "homepage": "https://github.com/PanJiaChen/vue-element-admin#readme", "main": ".eslintrc.js", "directories": {"test": "tests"}}