<template>
  <el-dialog v-if="dialogVisible" :title="$t('商品信息')" :visible.sync="dialogVisible" width="900px">
    <div class="divBox">
      <div class="container">
        <el-form size="small" inline label-width="85px">
          <el-form-item :label="$t('商品分类：')">
            <el-select
              v-model="tableFrom.mer_cate_id"
              :placeholder="$t('请选择')"
              class="dialogWidth"
              clearable
              @change="getList()"
            >
              <el-option
                v-for="item in merCateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('商品搜索：')">
            <el-input
              v-model="tableFrom.keyword"
              :placeholder="$t('请输入商品名称，关键字，产品编号')"
              class="dialogWidth"
              clearable
              @keyup.enter.native="getList"
            >
            </el-input>
          </el-form-item>
          <el-form-item>
              <el-button type="primary" size="small" @click="getList">{{ $t('查询') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="resellShow" class="mb10">
        <el-alert
          :title="$t('注：添加为预售商品后，原普通商品会下架；如该商品已开启其它营销活动，请勿选择！')"
          type="warning"
          v-if="resellShow"
          show-icon>
        </el-alert>
      </div>
      <el-table v-loading="listLoading" :data="tableData.data" size="small">
        <el-table-column width="55">
          <template slot-scope="scope">
            <el-radio
              v-model="templateRadio"
              :label="scope.row.product_id"
              @change.native="getTemplateRow(scope.row)"
            >&nbsp</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="product_id" label="ID" min-width="50" />
        <el-table-column :label="$t('商品图')" min-width="70">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.image"
                :preview-src-list="[scope.row.image]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="store_name" :label="$t('商品名称')" min-width="230" />
        <el-table-column prop="stock" :label="$t('库存')" min-width="70" />
      </el-table>
      <div class="block">
        <el-pagination
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { goodLstApi, categorySelectApi } from '@/api/product'
import { roterPre } from '@/settings'
export default {
  name: 'GoodsList',
  props:{
    resellShow:{
      type:Boolean,
      default:false
    },
    isSeckill: {
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      dialogVisible: false,
      templateRadio: 0,
      merCateList: [],
      roterPre: roterPre,
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 6,
        cate_id: '',
        store_name: '',
        keyword: '',
        is_gift_bag: 0,
        status: 1,
        is_ficti: this.isSeckill ? '' : 0,
        is_action: this.isSeckill ? '' : 1
      },
      multipleSelection: {},
      checked: []
    }
  },
  mounted() {
    this.getList()
    this.getCategorySelect()
    window.addEventListener('unload', (e) => this.unloadHandler(e))
  },
  methods: {
    getTemplateRow(row) {
      this.multipleSelection = { src: row.image, id: row.product_id }
      this.dialogVisible = false
      this.$emit('getProduct', this.multipleSelection)
    },
    // 商户分类；
    getCategorySelect() {
      categorySelectApi()
        .then((res) => {
          this.merCateList = res.data
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    // 列表
    getList() {
      this.listLoading = true
      goodLstApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.seachTiele {
  line-height: 35px;
}
.fr {
  float: right;
}
</style>
