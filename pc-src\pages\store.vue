<template>
  <div>
    <div v-if="$store.state.top_banner" class="store-banner">
      <img :src="$store.state.top_banner" alt="">
    </div>
    <div class="menu-count">
      <div class="user-menu user-wrapper acea-row row-middle" >
        <div class="menu-main acea-row row-middle">
          <div @click="goPage(menu,index)" @mouseenter="showCategory(index)" class="menu-item"
            v-for="(menu,index) in userMenu" :key="index" :class="{active:menuCur == menu.key}">{{ menu.title }}
          </div>
        </div>
        <div class="menu-search acea-row row-middle">
          <div class="text"><input type="text" placeholder="店内商品搜索" v-model="search"></div>
          <div class="bnt" @click="submit"><span class="iconfont icon-xiazai5"></span></div>
        </div>
        <div v-if="seen" class="category acea-row row-middle" @mouseleave="leave()">
          <div class="sort">
            <div
              class="item acea-row row-between-wrapper"
              v-for="(item, index) in category"
              :key="index"
            >
              <div class="name line1" @click="goCategoryGoods(item.store_category_id,null)">{{ item.cate_name }}<span class="iconfont icon-you"></span></div>
              <div class="sortCon">
                <div class="erSort acea-row">
                  <div
                    class="sub-item line1"
                    v-for="(itemn, indexn) in item.children"
                    :key="indexn"
                    @click="goCategoryGoods(itemn.store_category_id,null)"
                  >
                    {{ itemn.cate_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <nuxt-link class="moreBtn" :to="{path:'/store/category',query:{id:id}}">查看全部商品</nuxt-link>
        </div>
      </div>
    </div>
    <div v-if="showStore" class="wrapper_1200">
      <div class="user-wrapper">
        <div class="content clearfix">
          <div class="left-box">
            <div class="user-info">
              <div class="store-basis">
                <div class="store-logo">
                  <img :src="storeDetail.mer_avatar" alt="">
                </div>
                <div class="store-name line1">
                  <span v-if="storeDetail.type_name" class="trader">{{storeDetail.type_name}}</span>
                  <span v-else-if="storeDetail.is_trader" class="trader">自营</span>
                  {{ storeDetail.mer_name }}
                </div>
              </div>
              <div class="store-info">
                <div class="item acea-row row-middle">
                  <span class="title">店铺评分</span>
                  <div class="cont">
                    <span
                      v-for="(v, i) in 5"
                      :key="i"
                      :class="{ on: i + 1 <= score.number.toFixed(1) }"
                      class="iconfont icon-pingjia star"
                    ></span>
                  </div>
                </div>
                <div class="item acea-row row-middle">
                  <span class="title">关注人数</span>
                  <span class="desc">{{storeDetail.care_count < 10000 ? (storeDetail.care_count || '') : ((storeDetail.care_count || 0)/10000).toFixed(1)+'万'}}人</span>
                </div>
                <div v-if="storeDetail.mer_id && mer_service && mer_service.services_type == 1 || mer_service.services_type == 2" class="item acea-row row-middle">
                  <span class="title">联系客服</span>
                  <span class="iconfont icon-kefu" v-if="storeDetail.mer_id && storeDetail.services_type && mer_service.services_type== 1" @click="chatShow"></span>
                  <el-tooltip v-else-if="storeDetail.mer_id && mer_service.services_type== 2" popper-class="tps" class="iconfont" effect="dark" :content="'客服电话：'+mer_service.service_phone" placement="right">
                    <span class="iconfont icon-kefu"></span>
                  </el-tooltip>
                  <a class="iconfont icon-kefu" v-else-if="storeDetail.mer_id && storeDetail.services_type && mer_service.services_type== 4" :href="mer_service.mer_customer_link" target="blank"></a>
                </div>
                <div v-if="storeDetail.isset_certificate" class="item acea-row row-middle">
                  <span class="title">店铺资质</span>
                  <nuxt-link class="desc" :to="{path:'/qualifications',query:{id:id,storeName:storeDetail.mer_name}}">
                    <el-tooltip popper-class="tps" class="iconfont" effect="dark" content="企业营业执照" placement="right">
                      <img class="store_qualify" src="~/assets/images/store_qualify.png" alt="">
                    </el-tooltip>
                  </nuxt-link>
                </div>
              </div>
              <div class="store-favorites">
                <button class="collection" :class="storeDetail.care ? 'care' : ''" @click="followToggle">
                  {{ storeDetail.care ? '已收藏' : '收藏店铺' }}
                </button>
              </div>
            </div>
            <div class="menu-collapse">
              <div class="store-category" v-for="(item,index) in category" :key="index">
                <div class="cate-title">
                  <span v-if="item.children" class="cate-btn"
                    @click="toggle(index)">{{ index === current ? '-' : '+' }}</span>
                  <span class="cate-name" :class="(index== current&&cateId) ? 'font-bold' : ''" @click="goCategoryGoods(item.store_category_id,index)">{{ item.cate_name }}</span>
                </div>
                <div class="cate-count" v-if="item.children && index === current"> 
                  <div :class="itemn.store_category_id == cateId ? 'cate-item font-red' : 'cate-item'"
                    v-for="(itemn,indexn) in item.children" :key="indexn"
                    @click="goCategoryGoods(itemn.store_category_id,null)">{{ itemn.cate_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-box">
            <NuxtChild keep-alive/>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="wrapper_1200">
      <div class="noGoods">
        <div class="pictrue">
          <img src="../assets/images/store_closed.png">
        </div>
      </div>
    </div>
    <chat-room
      v-if="chatPopShow"
      :chatId="storeDetail.mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import ChatRoom from "@/components/ChatRoom";
export default {
  name: "store",
  auth: false,
  components: {ChatRoom},
  data() {
    return {
      chatPopShow: false,
      reply_star: 3,
      search: '',
      activeName: '1',
      current: 0,
      userMenu: [
        {
          key: 0,
          link: '/store',
          title: '店铺首页',
        },
        {
          key: 4,
          title: '全部分类',
          link: '/store/category'
        },
        {
          key: 1,
          title: '领优惠券',
          link: '/store/storeCoupon'
        }
      ],
      menuCur: 0,
      userInfo: {},
      storeDetail: {},
      mer_service: {},
      showStore: true,
      indexCur: 0,
      seen: false,
      categoryList: [],
      category: [], //店铺分类
      isCode: false,
    }
  },
  async asyncData({app, query}) {
    let [category] = await Promise.all([
      // app.$axios.get(`/api/store/merchant/detail/${query.id}`),
      app.$axios.get(`/api/store/merchant/category/lst/${query.id}`),
    ]);
    return {
      indexCur: query.type,
      id: query.id,
      // storeDetail: storeDetail.data,
      category: category.data,
      cateId: query.cateId,
      search: query.search
    };
  },
  computed: {
    score: function () {
      let store = this.storeDetail,
        score = {
          star: 0,
          number: 0
        };
      if ('postage_score' in store) {
        score.number = (parseFloat(store.postage_score) + parseFloat(store.product_score) + parseFloat(store.service_score)) / 3;
        score.star = score.number / 5 * 100;
      }
      return score;
    },
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        this.userMenu.forEach((el, index) => {
          if (el.link == val.path) {
            this.menuCur = el.key;
            this.indexCur = index;
          }
        })
        this.cateId = val.query.cateId ? val.query.cateId : '';
        this.search = val.query.search ? val.query.search : '';
      },
      // 深度观察监听
      deep: true
    }
  },
  fetch({store}) {
    store.commit('isHeader', true);
    store.commit('isFooter', true);
  },
  head() {
    return {
      title: "店铺-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.userMenu.forEach(el => {
      if (el.link == this.$route.path) {
        this.menuCur = el.key
      }
    });
    this.getMerConfig();
    this.getStoreDetail();
  },
  methods: {
    chatShow() {
      if(this.$auth.loggedIn){
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    goCategoryGoods(cateId,index) {
      if(index!=null)this.current = index == this.current ? 0 : index;
      this.seen = false;
      this.$router.replace({path: '/store/category', query: {id: this.id, cateId: cateId}});
    },
    //获取商户基础数据
    getMerConfig: function () {
      this.$axios.get('/api/pc/mer_config/' + this.id).then(res => {
        this.$store.commit('merBanner', res.data.banner);
        this.$store.commit('topBanner', res.data.top_banner);
      })
    },
    /*获取店铺详情*/
    getStoreDetail(){
      let that = this;
      that.$axios.get("/api/store/merchant/detail/" + that.id).then(res => {
        that.storeDetail = res.data;
        that.mer_service = res.data.services_type;
        that.showStore = true;
      }).catch(err => {
        that.$message.error(err);
        that.showStore = false;
      });
    },
    goPage(menu, index) {
      this.menuCur = menu.key
      this.$router.push({
        path: `${menu.link}`,
        query: {id: this.id}
      });
    },
    followToggle() {
      this.storeDetail.care ? this.unfollow() : this.follow();
    },
    unfollow() {
      let that = this;
      that.$axios.post('/api/user/relation/delete', {
        type: 10,
        type_id: that.id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = false;
          this.$message.success(res.message);
        }
      })
    },
    follow() {
      let that = this;
      that.$axios.post('/api/user/relation/create', {
        type: 10,
        type_id: that.id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = true;
          this.$message.success(res.message);
        }
      })
    },
    showCategory(index) {
      if (index == 1) {
        this.seen = true
      } else {
        this.seen = false
      }
    },
    leave() {
      this.seen = false;
    },
    toggle(index) {
      this.current = index == this.current ? -1 : index;
    },
    getList(item) {

    },
    submit() {
        this.$router.push({path: '/store/category', query: {search: this.search ? this.search.trim() : '', id: this.id}});
        this.search = '';
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  margin-top: 25px;
}
.store-banner {
  width: 100%;
  height: 130px;
  overflow: hidden;
  min-width: 1200px;
  img {
    object-fit: none;
    width: 100%;
    height: 100%;
  }
}
.wrapper_1200 {
  margin: 0 auto;
}
.menu-count {
  height: 40px;
  background: #DFDFDF;
  min-width: 1200px;
  width: 100%;
}
.user-menu {
  position: relative;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  padding: 0 10px;
  .category {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: rgba(254, 248, 248, .96);
    width: 100%;
    padding: 40px 20px 20px;
    z-index: 10;
    .name {
      width: 130px;
      position: relative;
      padding-right: 20px;
      margin-right: 30px;
      cursor: pointer;
      .iconfont {
        font-size: 10px;
        position: absolute;
        right: 0;
        top: 3px;
        color: #282828;
      }
    }
    .sortCon {
      width: 1000px;
      .sub-item {
        margin: 0 15px 15px;
        color: #666666;
        cursor: pointer;
      }
    }
    .erSort {
      align-items: center;
    }
    .item {
      margin-bottom: 20px;
      align-items: baseline;
    }
    .moreBtn {
      color: #282828;
      font-size: 12px;
      width: 100px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      border-radius: 13px;
      border: 1px solid #666666;
    }
  }
  .menu-main {
    width: 300px;
    height: 40px;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    .menu-item {
      display: inline-block;
      height: 26px;
      line-height: 26px;
      color: #282828;
      padding: 0 10px;
      cursor: pointer;
      &.active {
        color: #fff;
        background: #282828;
        border-radius: 15px;
      }
    }
  }
  .menu-search {
    width: 220px;
    height: 24px;
    background-color: #fff;
    border-radius: 17px;
    .text {
      width: 175px;
    }
    input {
      border: none;
      height: 24px;
      line-height: 24px;
      color: #999999;
      padding: 0 15px;
      border-radius: 17px;
      &:focus {
        border: none;
        outline: none;;
      }
    }
    .bnt {
      width: 44px;
      background-color: #282828;
      color: #fff;
      border-radius: 0 17px 17px 0;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
    }
  }
}
.user-wrapper {
  width: 1200px;
  margin: 0 auto;
  .left-box {
    width: 224px;
    float: left;
    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 370px;
      background: #fff;
      color: #282828;
      font-size: 14px;
      padding: 0 25px;
      .store-basis {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-bottom: 1px dashed #ECECEC;
        height: 130px;
        .trader {
          display: inline-block;
          width: auto;
          padding: 0 4px;
          height: 18px;
          line-height: 18px;
          text-align: center;
          color: #fff;
          background: #E93323;
          border-radius: 2px;
          margin-right: 3px;
          font-size: 12px;
          font-family: 'PingFang SC';
        }
      }
      .store-logo {
        width: 61px;
        height: 61px;
        margin-bottom: 15px;
        img {
          width: 61px;
          height: 61px;
          border-radius: 50%;
        }
      }
      .name {
        margin-top: 10px;
        padding: 0 15px;
      }
    }
    .store-info {
      padding: 15px 0 0;
      position: relative;
      border-bottom: 1px dashed #ECECEC;
      .service {
        right: 210px;
        position: absolute;
        top: 0;
        .ewm {
          width: 140px;
          border: 1px solid #eeeeee;
          background-color: #fff;
          padding: 10px 15px;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          align-items: center;
          color: #282828;
          .tip {
            font-size: 14px;
            color: #666666;
            margin-top: 10px;
            text-align: center;
          }
          .pictrue {
            vertical-align: middle;
            width: 116px;
            height: 116px;
            display: inline;
            img {
              width: 100%;
              height: 100%;
            }
            .iconfont {
              margin-bottom: 0;
              color: #282828;
            }
            .arrow {
              position: absolute;
              right: -15px;
              top: 23px;
              width: 0px;
              height: 0px;
              border: 8px solid transparent;
              border-left-color: #eee;
              &:before {
                position: absolute;
                left: -8px;
                top: -7px;
                content: "";
                width: 0px;
                height: 0px;
                border: 7px solid transparent;
                border-left-color: #fff;
              }
            }
          }
        }
      }
      .item {
        font-size: 12px;
        color: #7e7e7e;
        margin-bottom: 15px;
        .iconfont {
          cursor: pointer;
        }
        .icon-kefu {
          color: #E93323;
        }
        .cont {
          margin-left: 8px;
        }
        .star {
          font-size: 12px;
          color: #e6e6e6;
          ~ .star {
            margin-left: 5px;
          }
          &.on {
            color: #e93323;
          }
        }
        .title {
          color: #999999;
          font-size: 12px;
          margin-right: 15px;
        }
        .desc {
          color: #333333;
          position: relative;
        }
        .store_qualify{
          width: 16px;
          height: 16px;
        }
      }
    }
   
    .store-favorites {
      margin-top: 14px;
      .collection {
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #333333;
        border: 1px solid #C8C8C8;
        border-radius: 2px;
        background: #fff;
        &.care {
          color: #fff;
          background-color: #e93323;
          border-color: #e93323;
        }
      }
    }
    .menu-collapse {
      background: #fff;
      margin-top: 15px;
      .store-category {
        line-height: 34px;
        color: #666;
        border-bottom: 1px solid #F2F2F2;
        &:last-child {
          border-bottom: none;
        }
        .cate-title {
          padding: 0 12px;
          .cate-name {
            cursor: pointer;
          }
        }
        .cate-btn {
          cursor: pointer;
          display: inline-block;
          margin-right: 2px;
          width: 13px;
          height: 13px;
          background-color: #D2D2D2;
          border-radius: 2px;
          color: #fff;
          text-align: center;
          line-height: 12px;
        }
        .cate-count {
          padding: 15px 40px;
          background-color: #F2F2F2;
          div {
            cursor: pointer;
          }
          .font-red {
            color: #e93323;
          }
        }
        .font-bold{
          font-weight: bold;
        }
      }

    }
  }
  .right-box {
    width: 956px;
    // min-height: 730px;
    margin-left: 8px;
    padding-bottom: 30px;
    float: right;
  }
}
.router-tips {
  height: 60px;
  line-height: 60px;
  font-size: 14px;
  a {
    color: #333;
  }
  span {
    color: #999999;
  }
}
.noGoods{
  text-align: center;
  .pictrue{
    width: 274px;
    height: 213px;
    margin: 130px auto 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }
}
</style>
