<template>
    <div class="hot_imgs">
        <div class="title">
            最多可添加4个板块片建议尺寸140 * 140px；鼠标拖拽左侧圆点可
            调整板块顺序
        </div>
        <div class="list-box">
            <draggable
                class="dragArea list-group"
                :list="defaults.menu"
                group="people"
                handle=".move-icon"
            >
            <div class="item" v-for="(item,index) in defaults.menu" :key="index">
                <div class="move-icon">
                    <Icon type="ios-keypad-outline" size="22" />
                </div>
                <div class="img-box" @click="modalPicTap('单选',index)">
                    <img :src="item.img" alt="" v-if="item.img">
                    <div class="upload-box" v-else><Icon type="ios-camera-outline" size="36" /></div>
                    <div>
                        <el-dialog :visible.sync="modalPic" width="950px" :title="$t('上传图片')">
                            <uploadPictures :isChoice="isChoice" @getPic="getPic" :gridBtn="gridBtn" :gridPic="gridPic" v-if="modalPic"></uploadPictures>
                        </el-dialog>
                    </div>
                </div>
                <div class="info">
                    <div class="info-item" v-for="(infos,key) in item.info" :key="key">
                        <span>{{ $t(infos.title) }}</span>
                        <div class="input-box">
                            <el-input v-model="infos.value" :placeholder="infos.tips" :maxlength="infos.max" />
                        </div>
                    </div>
                </div>
            </div>
            </draggable>
        </div>
        <div class="add-btn" v-if="defaults.menu.length < 4">
            <Button style="width: 100%; height: 40px;" @click="addBox">{{ $t('添加板块') }}</Button>
        </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import vuedraggable from 'vuedraggable'
import UeditorWrap from 'vue-ueditor-wrap';
import uploadPictures from '@/components/uploadPicture';
export default {
    name: 'c_hot_imgs',
    props: {
        configObj: {
            type: Object
        }
    },
    components: {
        draggable: vuedraggable,
        UeditorWrap,
        uploadPictures
    },
    data () {
        return {
            defaults: {},
            menus: [],
            list: [
                {
                    title: 'aa',
                    val: ''
                }
            ],
            modalPic: false,
            isChoice: '单选',
            gridBtn: {
                xl: 4,
                lg: 8,
                md: 8,
                sm: 8,
                xs: 8
            },
            gridPic: {
                xl: 6,
                lg: 8,
                md: 12,
                sm: 12,
                xs: 12
            },
            activeIndex: 0
        }
    },
    created () {
        this.defaults = this.configObj
    },
    watch: {
        configObj: {
            handler (nVal, oVal) {
                this.defaults = nVal
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        addBox () {
            let obj = {
                img: 'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1594458238721&di=d9978a807dcbf5d8a01400875bc51162&imgtype=0&src=http%3A%2F%2Fattachments.gfan.com%2Fforum%2F201604%2F23%2F002205xqdkj84gnw4oi85v.jpg',
                info: [
                    {
                        title: this.$t(this.$t('标题')),
                        value: '',
                        tips: this.$t(this.$t('选填，不超过4个字')),
                        max: 4
                    },
                    {
                        title: this.$t(this.$t('简介')),
                        value: '',
                        tips: this.$t(this.$t('选填，不超过20个字')),
                        max: 20
                    }
                ],
                link: {
                    title: this.$t(this.$t('链接')),
                    optiops: [
                        {
                            type: 0,
                            value: '',
                            label: this.$t(this.$t('一级>二级分类'))
                        },
                        {
                            type: 1,
                            value: '',
                            label: this.$t(this.$t('自定义链接'))
                        }
                    ]
                }
            }
            this.defaults.menu.push(obj)
        },
        // 点击图文封面
        modalPicTap (title, index) {
            this.activeIndex = index
            this.modalPic = true;
        },
        // 添加自定义弹窗
        addCustomDialog (editorId) {
            window.UE.registerUI('test-dialog', function (editor, uiName) {
                let dialog = new window.UE.ui.Dialog({
                    iframeUrl: '/admin/widget.images/index.html?fodder=dialog',
                    editor: editor,
                    name: uiName,
                    title: this.$t(this.$t('上传图片')),
                    cssRules: 'width:1200px;height:500px;padding:20px;'
                });
                this.dialog = dialog;
                // 参考上面的自定义按钮
                var btn = new window.UE.ui.Button({
                    name: 'dialog-button',
                    title: this.$t(this.$t('上传图片')),
                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,
                    onclick: function () {
                        // 渲染dialog
                        dialog.render();
                        dialog.open();
                    }
                });

                return btn;
            }, 37);
        },
        // 获取图片信息
        getPic (pc) {
            this.defaults.menu[this.activeIndex].img = pc.att_dir;
            this.modalPic = false;
        }
    }
}
</script>

<style scoped lang="scss">
.hot_imgs{
    border-top: 1px solid rgba(0,0,0,0.05);
    .title{
        padding: 13px 0;
        color: #999;
        font-size: 12px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
        
    .list-box{
        .item{
            display: flex;
            margin-top: 20px;
            .move-icon{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 80px;
                cursor: move;
            }
                
            .img-box{
                width: 80px;
                height: 80px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .info{
                flex: 1;
                margin-left: 22px;
                .info-item{
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    span{
                        width: 40px;
                        font-size: 13px;
                    }
                    .input-box{
                        flex: 1;
                    }
                }  
            }      
        }            
    }     
}              
    .add-btn{
         margin-top: 10px;
    }
       
    .upload-box{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 80px;
        height: 80px;
        background: #f7f7f7;
        font-size: 12px;
        color: #cccccc;
    }
        
</style>
