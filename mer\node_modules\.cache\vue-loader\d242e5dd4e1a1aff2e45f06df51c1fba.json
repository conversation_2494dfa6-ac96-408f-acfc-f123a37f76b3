{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue", "mtime": 1750488046426}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nexport default {\r\n  name: \"MarketingSettings\",\r\n  props: {\r\n    formValidate: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    OneattrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    ManyAttrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    goodList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    manyTabDate: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    specValue: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    open_svip: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    price: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    svip_rate: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    manyTabTit: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n\r\n    formThead: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n\r\n    extensionStatus: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    deductionStatus: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    deduction_set: {\r\n      type: Number,\r\n      default: -1\r\n    },\r\n    extension_one_rate: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    extension_two_rate: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    deduction_ratio_rate: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n\r\n    baseUrl: {\r\n      type: String,\r\n      required: \"\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      manyBrokerageTwo: 0,\r\n      manyBrokerage: 0,\r\n      keyNum: 0,\r\n      manyVipPrice: \"\"\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    // 选择店铺推荐商品\r\n    openRecommend() {\r\n      this.$emit(\"openRecommend\");\r\n    },\r\n\r\n    // 删除店铺推荐商品\r\n    deleteRecommend(index) {\r\n      this.goodList.splice(index, 1);\r\n    },\r\n\r\n    addCoupon() {\r\n      const _this = this;\r\n      _this.formValidate.give_coupon_ids = [];\r\n      _this.formValidate.couponData = [];\r\n\r\n      this.$modalCoupon(\r\n        this.formValidate.couponData,\r\n        \"wu\",\r\n        _this.formValidate.give_coupon_ids,\r\n        (this.keyNum += 1),\r\n        function(row) {\r\n          _this.$set(_this.formValidate, \"couponData\", row);\r\n\r\n          row.map(item => {\r\n            _this.formValidate.give_coupon_ids.push(item.coupon_id);\r\n          });\r\n          _this.$forceUpdate();\r\n        }\r\n      );\r\n    },\r\n\r\n    handleCloseCoupon(tag) {\r\n      this.formValidate.couponData.splice(\r\n        this.formValidate.couponData.indexOf(tag),\r\n        1\r\n      );\r\n      this.formValidate.give_coupon_ids = [];\r\n      this.formValidate.couponData.map(item => {\r\n        this.formValidate.give_coupon_ids.push(item.coupon_id);\r\n      });\r\n      this.$forceUpdate();\r\n    },\r\n    //设置会员价\r\n    onChangeSpecs(item) {\r\n      if (item == 1 || item == 2 && this.open_svip) {\r\n        this.OneattrValue[0]['svip_price'] = this.OneattrValue[0]['price']\r\n          ? this.accMul(this.OneattrValue[0]['price'], this.svip_rate)\r\n          : 0\r\n        let price = 0\r\n        for (const val of this.ManyAttrValue) {\r\n          price = val.price ? this.accMul(val.price, this.svip_rate) : 0\r\n          this.$set(val, 'svip_price', price)\r\n        }\r\n      }\r\n    },\r\n    // 乘法\r\n    accMul(arg1, arg2) {\r\n      var max = 0\r\n      var s1 = arg1.toString()\r\n      var s2 = arg2.toString()\r\n      try {\r\n        max += s1.split('.')[1].length\r\n      } catch (e) {}\r\n      try {\r\n        max += s2.split('.')[1].length\r\n      } catch (e) {}\r\n      return (\r\n        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /\r\n        Math.pow(10, max)\r\n      )\r\n    },\r\n    // 批量设置\r\n    batchSet() {\r\n      for (let val of this.ManyAttrValue) {\r\n        this.manyBrokerage != undefined &&\r\n          this.$set(val, \"extension_one\", this.manyBrokerage);\r\n        this.manyBrokerageTwo != undefined &&\r\n          this.$set(val, \"extension_two\", this.manyBrokerageTwo);\r\n        if (this.manyVipPrice != undefined) {\r\n          this.$set(val, \"svip_price\", this.manyVipPrice);\r\n        } else {\r\n          this.$set(\r\n            val,\r\n            \"svip_price\",\r\n            (val.price * (this.manyVipDiscount / 100)).toFixed(2)\r\n          );\r\n        }\r\n      }\r\n    },\r\n\r\n    // 切换积分抵扣\r\n    changeIntergral(e) {\r\n      if (e == -1) {\r\n        this.formValidate.integral_rate = -1;\r\n      } else {\r\n        this.formValidate.integral_rate = this.formValidate.integral_rate;\r\n      }\r\n    }\r\n  }\r\n};\r\n", null]}