// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const productRouter =
  {
    path: `${roterPre}/product`,
    name: 'product',
    component: Layout,
    meta: {
      icon: 'dashboard',
      title: leaveuKeyTerms['商品管理']
    },
    alwaysShow: true,
    redirect: 'noRedirect',
    children: [
      {
        path: 'classify',
        name: 'ProductClassify',
        meta: {
          title: leaveuKeyTerms['商品分类'],
          noCache: true
        },
        component: () => import('@/views/product/productClassify')
      },
      {
        path: 'attr',
        name: `ProductAttr`,
        meta: {
          title: leaveuKeyTerms['商品规格'],
          noCache: true
        },
        component: () => import('@/views/product/productAttr')
      },
      {
        path: 'label',
        name: `ProductLabel`,
        meta: {
          title: leaveuKeyTerms['商品标签'],
          noCache: true
        },
        component: () => import('@/views/product/productLabel')
      },
      {
        path: 'list',
        name: `ProductList`,
        meta: {
          title: leaveuKeyTerms['商品列表'],
          noCache: true
        },
        component: () => import('@/views/product/productList')
      },
      {
        path: 'list/addProduct/:id?/:edit?',
        component: () => import('@/views/product/addProduct/index'),
        name: 'AddProduct',
        meta: { title: leaveuKeyTerms['商品添加'], noCache: true, activeMenu: `${roterPre}/product/list` },
        hidden: true
      },
      {
        path: 'cdKey',
        name: `ProductCdKey`,
        meta: {
          title: leaveuKeyTerms['卡密管理'],
          noCache: true
        },
        component: () => import('@/views/product/cdkey/index')
      },
      {
        path: 'cdkey/creatCdkey',
        component: () => import('@/views/product/cdkey/creatCdkey'),
        name: 'creatCdkey',
        meta: { title: leaveuKeyTerms['卡密添加'], 
        noCache: true, 
        activeMenu: `${roterPre}/product/cdkey` },
      },
      {
        path: 'reviews',
        name: 'ProductReviews',
        meta: {
          title: leaveuKeyTerms['商品评论'], noCache: true, activeMenu: `${roterPre}/product/reviews` 
        },
        component: () => import('@/views/product/Reviews/index')
      },
      {
        path: 'reservation',
        name: 'ProductReservation',
        meta: {
          title: leaveuKeyTerms['预约设置'], noCache: true, activeMenu: `${roterPre}/product/reservation` 
        },
        component: () => import('@/views/product/reservationSetting/index')
      },
      {
        path: 'specs',
        name: 'ProductSpecs',
        meta: {
          title: leaveuKeyTerms['商品参数'],
          noCache: true,
         
        },
        component: () => import('@/views/product/specs/list.vue')
      },
      {
        path: 'specs/create/:id?',
        name: 'ProductSpecsCreate',
        meta: {
          title: leaveuKeyTerms['添加参数模板'],
          noCache: true,
          activeMenu: `${roterPre}/product/specs`
        },
        component: () => import('@/views/product/specs/create.vue')
      },
      {
        path: 'unit',
        name: `ProductUnit`,
        meta: {
          title: leaveuKeyTerms['商品单位'],
          noCache: true,
          activeMenu: `${roterPre}/product/unit`
        },
        component: () => import('@/views/product/productUnit')
      },
    ]
  }

export default productRouter
