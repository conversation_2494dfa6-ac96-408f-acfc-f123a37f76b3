<template>
  <div class="order_confirm wrapper_1200" >
    <!-- <div class="header">
      <span class="home">首页 > <span v-if="!news">购物车 > </span> </span>提交订单
    </div> -->
    <template >
      <div class="address">
        <div class="title">{{service_type == 2 ? '上门服务' : '到店服务'}}</div>
        <div class="lines">
          <img src="../assets/images/line.png">
        </div>
        <!--上门服务-->

        <div v-if="allow_address&&service_type == 2" class="list acea-row row-middle" :class="isShow?'on':''">
          <div class="item" :class="current===index?'on':''" v-for="(item, index) in addressList" :key="index"
              @click="tapCurrent(index,item)">
            <div class="default bg-color" v-if="item.is_default">默认</div>
            <div class="name line1">{{ item.real_name }}</div>
            <div class="phone">{{ item.phone }}</div>
            <div class="details line4">{{ item.province }}{{ item.city }}{{ item.district }}{{ item.detail }}</div>
            <div class="iconfont icon-xuanzhong4 font-color" v-if="current===index"></div>
          </div>
          <div class="item add" @click="addAddress">
            <div class="iconfont icon-dizhi-tianjia"></div>
            <div class="tip">添加新地址</div>
          </div>
        </div>
      </div>
      <template v-if="allow_address&&service_type == 2">
        <div class="isShow" @click="open" v-if="!isShow && addressList.length>3">显示更多收货地址<span
          class="iconfont icon-xiangxia"></span></div>
        <div style="margin-top: 10px" v-if="addressList.length<=3"></div>
        <div class="isShow" @click="close" v-if="isShow && addressList.length>3">隐藏更多收货地址<span
          class="iconfont icon-xiangshang"></span></div>
      </template>
    </template>
    <!--到店服务-->
    <div v-if="service_type==1" class="service_form">
      <el-form report-submit='true' label-width="80px">
        <el-form-item label="联系人：" required>
          <el-input type="text" class="discount" v-model="post.real_name" placeholder="请填写联系人姓名" placeholder-class='placeholder' />
        </el-form-item>
        <el-form-item label="手机号：" required>
          <el-input type="text" class="discount" v-model="post.phone" placeholder="请填写联系人电话" placeholder-class='placeholder' />
        </el-form-item>
      </el-form>
    </div>  
    <div class="wrapper wrapper_1200">
      <div class="wrapper_count">
        <div class="title">订单信息</div>
        <div class="order">
          <div class="list">
            <div class="cartCount">
              <div class="storeInfo acea-row row-between-wrapper">
                <div class="name">{{merData.mer_name}}</div>
                <div class="service" @click="chatShow(mer_id)">联系客服 <span class="iconfont icon-lianxikefu"></span></div>
              </div>
              <div class="cartInfo">
                <div class="item">
                  <div class="acea-row row-between-wrapper">
                    <div class="txtPic acea-row row-middle">
                      <div class="pictrue">
                        <img :src='productInfo.image' v-if="productInfo.image">
                        <img src='' v-else>
                      </div>
                      <div class="text">
                        <div class="name line2">{{ productInfo.store_name }}</div>
                        <div class="info" v-if="attrSelected">{{ attrSelected.sku }}</div>
                        <div class="info" v-else>默认</div>  
                      </div>
                    </div>
                    <div class="acea-row row-between-wrapper">
                      <div class="money acea-row row-middle">¥{{ (svipData && svipData.show_svip_price && svipData.show_svip) ? attrSelected.svip_price : attrSelected.price }}
                        <span v-if="svipData && svipData.show_svip_price && svipData.show_svip" class="svip-image"><img src="@/assets/images/svip.png" alt=""></span>
                        <span class="num">x{{ count }}</span>
                      </div>
                      <!-- <span class="font-color">{{ itemn.productAttr.price }}</span> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="coupon">
                <div v-if="productAttr.length>0" class="couponTitle acea-row row-middle">
                  <div class="item-name item-75">服务规格</div>
                  <div>
                    <div class="acea-row list develivery" v-for="(item, index) in productAttr" :key="index">
                      <label
                        v-for="(itm, idx) in item.attr_values"
                        :key="idx"
                        class="deliviery_item"
                        :class="attrSelected.sku == itm ? 'checked' : ''" 
                        @click="changeAttr(item,itm)"
                      >
                        <div class="acea-row cont">
                          <div class="acea-row row-middle row-center name line1">{{ itm }}</div>
                          <div v-if="attrSelected.sku == itm" class="iconfont icon-xuanzhong4"></div>
                        </div>
                      </label>
                    </div>
                  </div>
                </div> 
              </div>
              <div class="coupon">
                <div class="couponTitle acea-row row-middle">
                  <div class="item-name item-75">服务方式</div>
                  <div v-if="reservation_type == 3" class="acea-row list develivery">
                    <div class="deliviery_item" v-for="(item,index) in serviceList" :key="index" :class="{checked : item.type==service_type}" @click="changeType(item)">
                      <div class="cont">
                        <div class="acea-row row-middle row-center name">{{item.name}}</div>
                        <div v-if="item.type==service_type" class="iconfont icon-xuanzhong4"></div>
                      </div>
                    </div>
                  </div>
                  <div class="acea-row list develivery" v-else>
                    <div class="deliviery_item checked">
                      <div class="cont">
                        <div class="acea-row row-middle row-center name">{{service_type == 1 ? '到店服务' : '上门服务'}}</div>
                        <div class="iconfont icon-xuanzhong4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="coupon">
                <div class="couponTitle acea-row row-middle">
                  <div class="item-name item-75">数量</div>
                  <div class="acea-row list develivery row-middle">
                    <div class="counter-wrap">
                      <div class="counter">
                        <button
                          class="iconfont icon-shangpinshuliang-jian"
                          :disabled="(count<=1 || (min_count>0 && count<=min_count))"
                          @click="addCart(0)"
                        ></button>
                        <input v-model="count" @input="inputNum"/>
                        <button
                          class="iconfont icon-shangpinshuliang-jia"
                          :disabled="(max_count>0&&count>=max_count)"
                          @click="addCart(1)"
                        ></button>
                      </div>
                    </div>
                    <div class="buy_limit" v-if="min_count>0 || max_count>0">
                      (<span v-if="min_count>0">{{min_count}}件起购<span v-if="min_count>0 && max_count>0">，</span></span><span v-if="max_count>0">最多{{max_count}}件</span>)
                    </div>
                  </div>
                </div> 
              </div>
              <!--预约时间-->
              <div class="coupon">
                <div class="couponTitle acea-row">
                  <div class="item-name item-75">预约时间</div>
                  <div v-if="sku_id" class="acea-row list develivery">
                    <calendar ref="calendar" :id="id" :skuId="sku_id" @time-selected="handleTimeSelected" />
                  </div>
                </div> 
              </div>
              <!--预约须知-->
              <div class="coupon" style="margin-top: 25px;">
                <div class="acea-row row-between-wrapper">
                  <div class="couponTitle acea-row">
                    <div class="item-name">预约须知</div>
                    <div class="rules">
                      <div class="rule-item">预定规则：预定成功后，系统不支持修改订单，如您有时间调整等需求，请联系商家协商</div>
                      <div class="rule-item">放号规则：用户只能预约{{productInfo.show_reservation_days}}天之内的日期</div>
                      <div class="rule-item" v-if="productInfo.is_cancel_reservation">取消预约：请于预约开始时间前 {{productInfo.cancel_reservation_time}} 小时进行操作。若距离预约开始已不足 {{productInfo.cancel_reservation_time}} 小时，请您联系商家协商处理。</div>
                      <div class="rule-item" v-else>取消预约：预定成功后，不支持取消预约，若您有相关需求请联系商家处理</div>
                    </div>
                  </div>
                </div> 
              </div> 
            </div> 
            <div class="bnt acea-row row-right">
              <button class="submit" @click="nextStep">下一步</button>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="wrapper_count">
        <div class="totalCon">
          <div class="total acea-row row-middle row-right">
            <div><span class=font-color>{{ totalNum }} </span>件商品，商品总金额：</div>
            <div class="money">¥{{ proPrice || 0 }}</div>
          </div>
        </div>
        <div class="totalAmount">应付总额：<span class="money font-color">¥{{ totalPrice }}</span></div>
        <div class="bnt acea-row row-right">
          <button class="submit" @click="nextStep">下一步</button>
        </div>
      </div> -->
    </div>
    <!-- 添加地址弹窗 -->
    <el-dialog
      title="添加收货地址"
      :visible.sync="dialogVisible"
      width="700"
      :before-close="handleClose">
      <div class="form-box">
        <div class="input-item" style="width: 48%;display:inline-block">
          <el-input v-model="formData.name" maxlength="25" placeholder="姓名"></el-input>
        </div>
        <div class="input-item" style="width: 48%;display:inline-block;margin-left: 3%">
          <el-input v-model="formData.phone" placeholder="手机号"></el-input>
        </div>
        <div class="input-item text-wrapper">
          <p @click="bindAdd(false)" v-if="!cityData.province.id">请选择省/市/区/街道</p>
          <p @click="bindAdd(true)" v-if="cityData.province.id" style="color: #333">
            <span v-if="cityData.province.name">{{ cityData.province.name }}</span>
            <span v-if="cityData.city.name">/{{ cityData.city.name }}</span>
            <span v-if="cityData.county.name">/{{cityData.county.name}}</span>
            <span v-if="cityData.district.name">/{{ cityData.district.name }}</span>
          </p>
          <div class="select-wrapper" v-if="isShowSelect">
            <div class="title-box" v-if="!cityData.province.id">选择省/自治区</div>
            <div class="title-box" v-if="cityData.step == 2">
              <span>{{ cityData.province.name }}</span>选择市
            </div>
            <div class="title-box" v-if="cityData.step == 3">
              <span>{{ cityData.county.name }}</span>
              <span>{{ cityData.city.name }}</span>选择区县
            </div>
            <div class="title-box" v-if="cityData.step == 4 && !stepStop">
              <span>{{ cityData.city.name }}</span>
              <span>{{ cityData.county.name }}</span>请选择配送区域
            </div>
            <div class="label-txt">
              <span v-for="(item,index) in cityData.list" :key="index" @click="bindCity(item)" :class="{on:cityData.pid == item.id}">{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="input-item">
          <el-input type="textarea" rows="3" v-model="formData.con" placeholder="详细地址"></el-input>
        </div>
        <div class="input-item">
          <el-checkbox v-model="formData.checked">设为默认</el-checkbox>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="bindSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <chat-room
      v-if="chatPopShow"
      :chatId="mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
import ChatRoom from "@/components/ChatRoom";
import Calendar from '@/components/Calendar.vue'
export default {
  name: "reservation",
  auth: "guest",
  components: { ChatRoom, Calendar },
  data() {
    return {
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() < Date.now()
        }
      },
      upLoadUrl: process.env.BASE_URL + "/api/upload/image/file",
      msgObj: {},
      chatPopShow: false,
      dialogVisible: false,
      isShowSelect: false,
      virtualIndex: 0,
      protocal: "",
      order_model: 2,
      allow_address: true,
      deliveryName: '上门服务',
      agrementTtile: '发票说明',
      order_extend: [],
      extend: {},
      timeVal: '',
      dateVal: '',
      imgUrl: [],
      uploadLimit: 10,
      currentLimit: 10,
      pics: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      post: {
        real_name: '',
        phone: ''
      }, //核销员信息
      formData: {
        name: '',
        phone: '',
        con: '',
        checked: false
      },
      cityData: {
        pid: 0,
        step: 1,
        list: [],
        con: '',
        province: {},
        city: {},
        county: {},
        district: {}
      },
      cityDataOrg:[],
      addressList: [],
      current: 0,
      news: 1,
      cartInfo: [],
      addressId: 0,
      useIntegral: false,//是否使用积分
      couponId: 0,
      subCoupon: {},
      order_type: '',
      computeData: {},
      mark: {},//备注信息
      totalNum: 0,
      priceGroup: {},
      totalPrice: 0,//最终商品金额；
      seckillId: 0,
      isShow:false,
      freight: 0,
      orderStatus: '',
      proPrice: '',
      addressInfo: {},
      selectedIndex: -1,
      selectedArr: [],
      stepStop: false,
      activeIndex: "",
      take: [],
      order_key: '',
      mer_id: 0,
      is_take: false,
      cityShow:1,
      order_form: [],
      attrSelected: {},
      count: 1,
      formCount: 2,
      formList: {},
      formValue: [],
      productInfo: {},
      serviceList: [
        {name: "上门服务",type: 2},
				{name: "到店服务",type: 1},
      ],
      service_type: 1,  //1到店2上门
      reservation_type: 0,
      sku_id: "",
      max_count: 0,
			pay_limit: 0,
			min_count: 0,
			svipData: {},
			merData: {},
      productAttr: [],
      attr: {
				cartAttr: false,
				productSelect: {}
			},
			productValue: {},
      attrValueSelected: "",
      reservationTimeData: [],
      selectedDate: "",
      currentTimeId: "",
      selectedDay: "",
      stock: 0,
    }
  },
  async asyncData({app, query}) {
    try{
      let [goods, mer] = await Promise.all([
        app.$axios.get(`/api/store/product/detail/${query.id}`),
        app.$axios.get(`/api/store/merchant/detail/${query.mer_id}`),
      ]);
      return {
      news: query.new ? 1 : 0,
      id: query.id,
      sku_id: query.sku_id,
      mer_id: query.mer_id,
      productInfo: goods.data,
      productAttr: goods.data.attr,
      productValue: goods.data.sku,
      max_count: goods.data.once_max_count,
      pay_limit: goods.data.pay_limit,
      min_count: goods.data.once_min_count,
      count: goods.data.once_min_count || query.count,
      reservation_type: goods.data.reservation_type,
      service_type: goods.data.reservation_type == 1 ? 1 : 2,
      svipData: goods.data.show_svip_info || null,
      merData: mer.data
    }
    }catch (e){}
    
  },
  fetch({store}) {
    store.commit('isHeader', true);
    store.commit('isFooter', true);
  },
  head() {
    return {
      title: "确认订单-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getAddressList();
    this.getCityList();
    // this.getFormList();
  },
  watch: {
    productAttr: {
      immediate: true,
      handler(attr) {
        for (var key in this.productValue) {
          if (this.productValue[key].value_id == this.sku_id) {
            this.attrSelected = this.productValue[key];
          }
        }
      }
    },
    attrSelected: {
      immediate: true,
      handler(attr) {
        this.totalPrice = this.attrSelected.price*this.count
      }
    }
  },
  methods: {
    //获取商品详情
    getGoodsDetails(){
      let that = this;
      that.$axios
        .get("/api/store/product/detail/" + that.id).then(res => {
          let data = res.data;
				// this.$store.commit('SET_RESERVATE_INFO', res.data);
				this.$set(this, 'productAttr', res.data.attr);
				this.productValue = res.data.sku;
				this.productInfo = res.data;
				this.id = data.product_id;
				this.$set(this, 'max_count', res.data.once_max_count);
				this.$set(this, 'pay_limit', res.data.pay_limit);
				this.$set(this, 'min_count', res.data.once_min_count);	
				this.reservation_type = data.reservation_type;
				this.service_type = data.reservation_type == 1 ? 1 : 2; //1到店，2上门
      })
      .catch(err => {
        this.$message.error(err);
      });
    },
    // 获取商户信息
    getMerData(id) {
      this.$axios.get('/api/store/product/show/'+id).then(res => {
        this.merData = res.data.merchant
      })
    },
    // 切换规格
    changeAttr(item,itm) {
      this.attrSelected = this.productValue[itm]
      this.sku_id = this.productValue[itm]['value_id']
      this.$refs.calendar.loadTimeSlots(this.sku_id)
    },
    // 获取表单数据
    getFormList() {
      let arr = []
      let data = this.getFormData()
      for(var i=0; i<this.formCount; i++) {
        arr.push(JSON.parse(JSON.stringify(data)))
      }
      this.formValue = arr
    },
    // 具体日期
    onchangeDate(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    onchangeTime(e) {
      this.$set(this.order_extend[this.virtualIndex], 'value', e);
    },
    getTime(index){ 
			this.virtualIndex = index;
		},
    addCart(type) {
      let productSelect = this.attrSelected
      //如果没有属性,赋值给商品默认库存
			if (productSelect === undefined && !this.attr.productAttr.length)
				productSelect = this.attr.productSelect;
			//无属性值即库存为0；不存在加减；
			if (productSelect === undefined) return;
			if (type) {
				this.count++;
				if(this.count > this.max_count&&this.max_count!=0&&this.pay_limit!=0){
					this.$set(this, "count", this.max_count);
          return this.$message.error("单次购买件数不能超过"+this.max_count+"件！");
				}
			} else {
				this.count--;
				if (this.count < 1) {
					this.$set(this, "count", 1);
				}
				if(this.count < this.min_count&&this.min_count!=0){
					this.$set(this, "count", this.min_count);
          this.$message.error("单次购买件数不能少于"+this.min_count+"件！");
				}
			}
			this.$set(this, "totalPrice", productSelect.price * this.count);
    },
    inputNum() {
      this.count = parseInt(this.count) <= 1 ? 1 : this.count;
    },
    handleTimeSelected({ timeSlots, date, time, day, stock }) {
      this.reservationTimeData = timeSlots;
      this.selectedDate = date;
      this.currentTimeId = time;
      this.selectedDay = day > 10 ? day : '0'+day;
      this.stock = stock
    },
    changeType(item) {
			this.service_type = item.type
		},
    chatShow(mer_id) {
      if(this.$auth.loggedIn){
        this.mer_id = mer_id;
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    // 表单重置
    formReset() {
      this.formData.name = ''
      this.formData.phone = ''
      this.formData.con = ''
      this.formData.checked = false
      this.cityData.province = {}
      this.cityData.city = {}
      this.cityData.district = {}
      this.cityData.step = 1
      this.cityData.pid = 0
      this.selectedArr = []
    },
    handleClose() {
      this.formReset()
      this.dialogVisible = false
    },
    bindAdd(isReset) {
      if(isReset){this.cityData.step = 1;this.stepStop = false}
      this.isShowSelect = !this.isShowSelect
      if(this.cityData.step == 4 || this.stepStop){
        return
      } else {
        this.cityData.city = {}
        this.cityData.district ={}
        this.cityData.province ={}
        this.cityData.county ={}
        this.getCityList(0,null)
      }
    },
    /*选择快递配送方式*/
    getDevelivery(item,v,index,i){
      if((i == 0 && !item.order.isTake) || (i == 1 && item.order.isTake)) return;
      this.$set(item.order, 'isTake', v);
      this.activeIndex = index;
      this.getData(item);
    },
    beforeUpload(file) {
		  const isImage = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;
		  if (!isImage) {
		    this.$message.error("上传图片只能是 JPG、PNG 格式!");
		  }
		  if (!isLt2M){
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
		  return isImage && isLt2M;
		},
    /** 删除图片*/
		DelPic: function(index) {
			let that = this,
			pic = this.pics[index];
			that.pics.splice(index, 1);
			that.$set(that, 'pics', that.pics);
		},
    //上传图片前的图片验证回调
    beforeAvatarUpload(file) {
      //图片格式
      const isJPG = file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/jpeg';
      //图片大小
      const isLt2M = file.size / 1024 / 1024 <= 2;
      if (!isJPG) {
        this.$message.error('上传图片只能为jpg/jpeg或png格式');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB');
      }
      const _this = this;
      return isJPG && isLt2M;
    },
    handleExceed() {
      this.$message.warning("最多上传10张图片");
    },
    handleRemove(file, fileList) {
      this.pics = [];
      fileList.forEach(item => {
        this.pics.push(item.response.data.url);
      });
      this.currentLimit = this.uploadLimit - this.pics.length - this.imgUrl.length || -1;
    },
    handleRemove1(index) {
      this.imgUrl.splice(index, 1)
      this.currentLimit = this.uploadLimit - this.imgUrl.length || -1;
    },
    // 文件上传失败时的钩子
    handleError(err, file, fileList) {
    },
    handleSuccess(response) { 
      if (response.status === 200) {
        this.pics.push(response.data.path);
      } else if (response.status === 400) {
        this.$message.error(response.msg);
      }
    },
    getData(data){
      this.cartInfo[this.activeIndex] = data
			if (data.order.isTake) {
        this.take.push(data.mer_id)
      } else {
        this.take.forEach((item, i) => {
          if (data.mer_id == item) {
            this.take.splice(i, 1)
          }
        })
      }
    }, 
    getCityList(pid,fun) {
      pid = pid || 0
      this.$axios.get('/api/v2/system/city/lst/'+pid).then(res => {
        this.cityDataOrg = res.data
        this.cityData.list = res.data
        fun && fun()
      })
    },
    addAddress() {
      this.dialogVisible = true
    },
    // 选择城市
    bindCity(item) {
      let that = this;
      if (that.cityData.step == 4) {
        that.cityData.district = item;
        that.selectedArr.push(item);
        that.isShowSelect = false
      } else {
        if (that.cityData.step == 1) {
          that.cityData.province = item;
          that.getCityList(item.id,null);
          that.selectedArr = [item];
          that.cityData.step++;
          return
        }
        if (that.cityData.step == 2) {
          that.cityData.city = item
          that.getCityList(item.id,null)
          that.cityData.step++
          that.selectedArr.push(item);
          return
        }
        if(that.cityData.step == 3){
          that.cityData.county = item
          that.selectedArr.push(item);
          that.cityData.step++
          that.getCityList(item.id,function(){
            if(that.cityData.list && that.cityData.list.length){
              that.stepStop = false
              return
            }else{
              that.stepStop = true
              that.isShowSelect = false
              return
            }
          })
        }
      }
    },
    bindSubmit() {
      if (!this.formData.name) {
        return Message.error('请填写姓名')
      }
      if (!this.formData.phone || !/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formData.phone)) {
        return Message.error('请填写正确的手机号码')
      }
      if (!this.cityData.province.name) {
        return Message.error('请选择省市区')
      }
      if (!this.formData.con) {
        return Message.error('请填写详细地址')
      }
      this.$axios.post('/api/user/address/create', {
        area: this.selectedArr,
        city_id: this.cityData.city.city_id,
        is_default: this.formData.checked ? 1 : 0,
        real_name: this.formData.name,
        phone: this.formData.phone,
        detail: this.formData.con,
        address_id: 0
      }).then(res => {
        this.addressId = res.data.id
        this.dialogVisible = false
        this.getAddressList()
        this.formReset()
        return Message.success('添加成功')
      }).catch(err => {
        return Message.error(err)
      })
    },
    nextStep() {
      let that = this;
      if (that.service_type == 2 && !that.addressId)return Message.error('请选择收货地址')
      if(!that.currentTimeId)return Message.error('请选择时间段')
      let timeData = that.reservationTimeData.find(item => item.attr_reservation_id === that.currentTimeId);
      if(that.count>timeData.stock)return Message.error(`该时间段库存最大为${timeData.stock}`)
      if(that.service_type == 1){
        if(!that.post.real_name){
          return Message.error('请填写联系人姓名')
        }
        if(!that.post.phone){
          return Message.error('请填写联系人电话')
        }
        if(that.post.phone && !/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.post.phone)){
          return Message.error('电话格式不正确')
        }
      }
      if(this.service_type == 1)localStorage.setItem('customerInfo',  JSON.stringify(that.post));
      that.$axios
        .post("/api/user/cart/create", {
          product_id: this.id,
          cart_num: this.count,
          is_new: 1,
          sku_id: this.sku_id,
          product_attr_unique: this.attrSelected.unique,
          product_type: 0,
          reservation_id: that.currentTimeId,
				  reservation_date: that.selectedDate+'-'+that.selectedDay
        })
        .then(res => {
          let cartId = res.data.cart_id
          that.$cookies.remove('cart_checked');
          this.$router.push({
            path: that.productInfo.mer_form_id ? 
              `/reservation_info?cart_id=${cartId}&address_id=${that.addressId}&service_type=${that.service_type}&mer_id=${that.productInfo.mer_id}&form_type=${that.productInfo.reservation_form_type}` 
              : `/order_confirm?cartId=${cartId}&address_id=${that.addressId}&service_type=${that.service_type}&mer_id=${that.productInfo.mer_id}`
          });
        })
        .catch(err => {
          this.$message.error(err);
        });
    },
    getFormData(){
      let formDatas = this.objToArr(this.formList.value)
      formDatas.forEach((item, index, arr)=>{
        item.value = '';
        if(item.name == 'texts'){
          if(item.defaultValConfig.value){
            this.$set(item, 'values' , item.defaultValConfig.value);
          }else{
            this.$set(item, 'values' , '');
          }
        }else if(item.name == 'radios'){
          this.$set(item, 'values' , item.wordsConfig.list[0]['val']);
        }else if(item.name == 'uploadPicture' || item.name == 'checkboxs'){
          this.$set(item, 'values' , []);
        }else if(['timeranges','dateranges'].indexOf(item.name) != -1){
          if(item.valConfig.tabVal==0){
            if(item.valConfig.tabData==0){
              let current = '';
              if(item.name == 'timeranges'){
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm')
              }else{
                 current = this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy/MM/dd')
              }
              this.$set(item, 'values' , [current,current]);
            }else{
              this.$set(item, 'values' , item.valConfig.specifyDate);
            }
          }else{
            this.$set(item, 'values' , ['','']);   
          }
        }else{
          if(['times','dates'].indexOf(item.name) != -1){
            if(item.valConfig.tabVal==0){
              if(item.valConfig.tabData==0){
                if(item.name == 'times'){
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'hh:mm'));
                }else{
                  this.$set(item, 'values' , this.comsys.formatDate(new Date(Number(new Date().getTime())), 'yyyy-MM-dd'));
                }
              }else{
                this.$set(item, 'values' , item.valConfig.specifyDate);
              }
            }else{
              this.$set(item, 'values' , '');
            }
          }else{
            this.$set(item, 'values' , '');
          }
        }
      })
      return formDatas;
    },
    // 对象转数组
    objToArr(data) {
      let obj = Object.keys(data);
      let m = obj.map(key => data[key]);
      return m;
    },
    changeRegion(item){
      this.cityShow = item.valConfig.tabVal;
    },
    lazyLoad(node, resolve){
      let id = 0
      if(node.data){
        id = node.data.id
      }
      this.$axios.get(`api/v2/system/city/lst/${id}`).then((res) => {
        res.data.map(item=>{
          item.leaf = (this.cityShow==0 && item.level >=2) || (this.cityShow==1 && item.level >=3) || (this.cityShow==2 && item._loading == undefined);
        })
        resolve(res.data);
      }).catch(err=>{
        resolve();
      });
    },
    getAddressList() {
      let that = this;
      that.$axios.get('/api/user/address/lst', {
        params: {
          page: 1,
          limit: 50
        }
      }).then(res => {
        that.addressList = res.data.list;
        that.post = res.data.list.length > 0 ? {real_name: res.data.list[0].real_name,phone:res.data.list[0].phone } : {real_name: '', phone: ''}
        
        that.addressList.forEach((item, index) => {
          if (item.is_default) {
            that.addressId = item.address_id;
            that.current = index;
          } else {
            that.addressId = that.addressList[0].address_id;
            that.current = 0;
          }
        });
      })
    },
    tapCurrent(index, item) {
      this.current = index;
      this.addressId = item.address_id;
    },
    open() {
      this.isShow = true
    },
    close() {
      this.isShow = false
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-radio__inner{
  width: 18px;
  height: 18px;
}
::v-deep .el-checkbox__inner{
  width: 16px;
  height: 16px;
}
::v-deep .el-radio__inner::after{
  width: 7px;
  height: 7px;
}
::v-deep .el-checkbox__inner::after{
  left: 5px;
  top:2px
}
.service_form {
  background: #fff;
  padding: 26px;
  margin-bottom: 14px;
  .el-form-item {
    &:last-child {
      margin-bottom: 0;
    }
  }
  .discount {
    width: 334px;
  }
}
.input-item {
  margin-bottom: 20px;
}
.item-require{
	color: #e93323;
	position: absolute;
  left: -8px;
}
.text-wrapper {
  position: relative;
  height: 40px;
  line-height: 40px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  box-sizing: border-box;
  border-radius: 4px;
  color: #cfcfcf;
  .select-wrapper {
    z-index: 10;
    position: absolute;
    left: 0;
    top: 45px;
    width: 100%;
    padding: 0 15px;
    background: #fff;
    border: 1px solid #E93323;
    border-radius: 4px;
    line-height: 2;
    .title-box {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #EFEFEF;
      color: #E93323;
      font-size: 14px;
      span {
        margin-right: 8px;
        color: #666666;
      }
    }
    .label-txt {
      margin: 8px 0 18px;
      color: #666666;
      font-size: 14px;
      span {
        margin-right: 10px;
        cursor: pointer;
        &.on {
          color: #E93323;
        }
      }
    }
  }
}
.order_confirm {
  margin-top: 20px;
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .address {
    background-color: #fff;
    .title {
      height: 64px;
      font-size: 18px;
      padding: 0 28px;
      line-height: 64px;
    }
    .lines {
      width: 100%;
      height: 4px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .list {
      padding: 0 25px 26px 25px;
      height: 217px;
      overflow: hidden;
      &.on {
        height: auto;
      }
      .item {
        width: 250px;
        height: 170px;
        border: 1px solid #EAEAEA;
        padding: 22px 27px;
        overflow: hidden;
        margin: 30px 0 0 30px;
        position: relative;
        cursor: pointer;
        &.on {
          border-color: #E93323;
        }
        .icon-xuanzhong4 {
          position: absolute;
          right: -4px;
          bottom: -4px;
          font-size: 27px;
        }
        .default {
          position: absolute;
          width: 56px;
          height: 23px;
          font-size: 12px;
          color: #fff;
          text-align: center;
          line-height: 23px;
          top: 0;
          right: 0;
        }
        &.add {
          text-align: center;
          .iconfont {
            font-size: 35px;
            color: #BFBFBF;
            margin-top: 25px;
          }
          .tip {
            color: #C8C8C8;
            margin-top: 8px;
          }
        }
        .name {
          font-size: 16px;
        }
        .phone {
          margin-top: 9px;
        }
        .details {
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }
  .develivery{
    margin-left: 30px;
    max-width: 980px;
  }
  .develivery_take{
    margin-left: 100px;
    font-size: 14px;
    font-weight: normal;
    padding-left: 16px;
    position: relative;
    top: -10px;
    padding-bottom: 8px;
    span{
      color: #E93323;
      font-size: 14px;
      position: absolute;
      top: 3px;
      left: 0;
    }
  }
  .deliviery_item{
    margin-right: 12px;
    box-sizing: border-box;
    cursor: pointer;
    .cont{
      position: relative;
      height: 38px;
      border: 1px solid #d3d3d3;
      display: flex;
      overflow: hidden;
      margin-bottom: 10px;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
      }
      .name{
        padding: 0 50px;
        font-size: 14px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -3px;
        font-size: 22px;
      }
  }
  .isShow {
    width: 100%;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #707070;
    cursor: pointer;
    .iconfont {
      margin-left: 8px;
      font-size: 12px;
    }
  }
  .wrapper {
    .wrapper_count{
      background-color: #ffffff;
      padding-bottom: 56px;
      &:last-child{
        margin-top: 14px;
      }
    }
    .checkbox-wrapper{
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
    .money_count{
      margin-right: 30px;
    }
    .money_down{
      color: #fff;
      font-size: 12px;
      line-height: 17px;
      height: 17px;
      background-color: #E93323;
      width: 34px;
      text-align: center;
      border-radius: 3px;
      margin-right: 10px;
    }
    .money_final{
      margin-top: 6px;
      color: #666666;
      font-size: 14px;
    }
    .integral_count{
      padding: 32px 26px;
      border: 1px solid #EFEFEF;
      display: flex;
      justify-content: space-between;
      .integral_title{
        font-size: 18px;
      }
      .money{
        margin-top: 15px;
        align-items: center;
      }
    }
    .title {
      height: 64px;
      line-height: 64px;
      padding: 0 28px;
      font-size: 18px;
    }
    .cartCount {
      padding: 0 32px 26px;
      margin-bottom: 20px;
      border: 1px solid #EFEFEF;
    }
    .cartInfo {
      padding-top: 30px;
      margin-bottom: 20px;
      .item{
        margin-bottom: 15px;
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
    .storeInfo {
      height: 60px;
      border-bottom: 1px solid #EFEFEF;
      position: relative;
      .qrcode {
        position: absolute;
        background: #fff;
        right: -15px;
        display: none;
        z-index: 10;
        bottom: 60px;
        border: 1px solid #ddd;
        width: 110px;
        height: 110px;
        img{
          width: 100%;
        }
      }
      .name {
        color: #666666;
      }
      .service {
        cursor: pointer;
        .iconfont {
          color: #E93323;
          font-size: 18px;
        }
        &:hover {
          + .qrcode {
            display: inline;
          }
        }
      }
    }
    .order {
      width: 1160px;
      margin: 0 auto;
      .list {
        .item {
          // margin-bottom: 26px;
          .txtPic {
            .pictrue {
              width: 62px;
              height: 62px;
              position: relative;
              span {
                display: block;
                width: 100%;
                text-align: center;
                font-size: 12px;
                line-height: 18px;
                background: rgba(0,0,0,.5);
                position: absolute;
                left: 0;
                bottom: 0;
                color: #fff;
              }
              img {
                width: 100%;
                height: 100%;
              }
            }
            .text {
              max-width: 500px;
              margin-left: 10px;
              .name {
                width: 100%;
              }
              .info {
                margin-top: 12px;
                color: #919191;
              }
              .err-txt{
                margin-top: 12px;
                color: #E93323;
                align-items: center;
                .txt{
                  display: inline-block;
                }
                .icon-tishi{
                  position: relative;
                  top: 1px;
                }
              }
            }
          }
          .ship_date{
            margin-top: 10px;
            color: #FD6523;
          }
          .font-color {
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
            text-align: right;
          }
          .num {
            margin-left: 6px;
            font-size: 12px;
          }
          .svip-image{
            width: 35px;
            height: 15px;
            margin-left: 5px;
            img{
              width: 35px;
              height: 15px;
            }
          }
        }
      }
      .coupon {
        border-top: 1px solid #EFEFEF;
        .icon-wenhao{
          color: #fff;
          display: inline-block;
          width: 14px;
          height: 14px;
          text-align: center;
          line-height: 14px;
          background-color: #236FE9;
          border-radius: 100%;
          font-size: 8px;
          margin-right: 5px;
        }
        .plantTitle{
          font-size: 18px;
          position: relative;
          .title{
            align-items: center;
            padding: 0;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
        }
        .couponTitle {
          font-size: 16px;
          padding: 26px 0;
          position: relative;
          .item-name {
            font-size: 16px;
            &.item-75 {
              width: 75px;
            }
          }
          .title{
            align-items: center;
            span{
              margin-left: 3px;
              cursor: pointer;
            }
          }
          .couponPrice {
            font-size: 16px;
            font-weight: bold;
          }
          .couponPriceNo {
            font-size: 14px;
          }
        }
        &.invoice{
          padding-bottom: 26px;
          .couponTitle{
            padding-bottom: 15px;
          }
        }
        .invoice_info{
          font-size: 14px;
          margin-left: 102px;
          color: #236FE9;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .invoice_data{
          position: absolute;
          right: 0;
          bottom: 20px;
          font-size: 16px;
          display: flex;
          .data_item{
            margin-left: 20px;
            &.modify{
              cursor: pointer;
            }
          }
        }
        .couponList {
          .item {
            height: 40px;
            width: 182px;
            border: 1px solid #E93323;
            margin: 0 15px 15px 0;
            position: relative;
            cursor: pointer;
            overflow: hidden;
            &.disabled{
              pointer-events:none;
              opacity: .6;
            }
            &.grey {
              border-color: #B4B4B4;
            }        
            .iconfont {
              position: absolute;
              right: -2px;
              bottom: -4px;
              font-size: 20px;
            }
            .name {
              width: 70px;
              height: 100%;
              color: #fff;
              text-align: center;
              line-height: 40px;
              background-color: #E93323;
              &.grey {
                background-color: #B4B4B4;
              }
            }
            .money {
              width: 110px;
              text-align: center;
              color: #E93323;
              &.grey {
                color: #B4B4B4;
              }
            }
            &.item5 {
              border-color: #333;
              .name {
                background-color: #333;
                color: #FDD7B4;
              }
              .money {
                color: #333;
              }
              .font-color{
                color: #333!important;
              }
            }
          }
        }
        .counter-wrap {
          flex: 1;
          min-width: 0;
          span {
            vertical-align: bottom;
            font-size: 14px;
            color: #5a5a5a;
          }
        }
        .buy_limit {
          margin-left: 20px;
          color: #E93323;
        }
        .rules {
          color: #999999;
          margin-left: 26px;
          .rule-item {
            margin-bottom: 12px;
          }
        }
      .counter {
        display: inline-block;
        border: 1px solid #d3d3d3;
        font-size: 0;
        button {
          width: 44px;
          height: 36px;
          border: none;
          background: none;
          outline: none;
          font-weight: inherit;
          font-size: 12px;
          font-family: inherit;
          color: #707070;
          vertical-align: middle;
          &:disabled {
            color: #d0d0d0;
            cursor: not-allowed;
          }
        }
        input {
          width: 64px;
          height: 36px;
          border: none;
          border-right: 1px solid #d3d3d3;
          border-left: 1px solid #d3d3d3;
          outline: none;
          font-weight: inherit;
          font-size: 16px;
          font-family: inherit;
          text-align: center;
          color: #5a5a5a;
          vertical-align: middle;
        }
      }
        .integralCurrent {
          margin-left: 33px;
          .num {
            margin-left: 6px;
          }
        }
        .msgTitle {
          font-size: 18px;
					text-align: left;
          position: relative;
          word-wrap: break-word;
        }
      }
      .message {
        padding-top: 26px;
        align-items: center;		
				.upload{
					margin-left: 26px;
					width: 800px;
				}
        .textarea {
          width: 820px;
          height: 120px;
          background-color: #F7F7F7;
          border: 0;
          outline: none;
          resize: none;
          padding: 12px 14px;
          margin-left: 26px;
        }
      }
      .integral {
        padding: 26px 0;
      }
    }
    .totalCon {
      padding: 27px 46px;
      .total {
        & ~ .total {
          margin-top: 12px;
        }
        .money {
          width: 120px;
          text-align: right;
        }
      }
    }
    .totalAmount {
      width: 1160px;
      height: 70px;
      line-height: 70px;
      background: #F7F7F7;
      text-align: right;
      padding-right: 22px;
      margin: 0 auto;
      .money {
        font-size: 20px;
        font-weight: bold;
        margin-left: 4px;
        width: 120px;
        display: inline-block;
      }
    }
    .bnt {
      margin: 38px 20px 0 0;
      cursor: pointer;
      .submit {
        width: 180px;
        height: 46px;
        border-radius: 4px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        line-height: 46px;
        outline: none;
        border: none;
        background-color: #E93323 ;
        &:disabled {
          border-color: #fab6b6;
          background-color: #fab6b6;
        }
      }
    }
  }
}
.coupon .message .number ::v-deep.el-input__inner{
  line-height: unset!important; 
}	
.virtual_form{
  margin-top: 30px;
  border-top: 1px solid #EFEFEF;
  .item-title {
    margin-top: 25px;
    font-size: 18px;
  }
  .virtual-item{
    margin-top: 24px;
    display: flex;
    flex: 1 1 50%;
    .item{
      align-items: center;
      &.item-img {
        align-items: flex-start;
        .virtual-title {
          margin-top: 2px;
        }
      }
    }
    .virtual-title{
      width: 110px;
      font-size: 16px;
      margin-right: 20px;
      position: relative;
    }
    .discount,.el-date-editor.el-input,.el-select {
      width: 344px;
    }
    .el-range-editor--small.el-input__inner{
      height: 40px;
      width: 344px;
    }
  }
  /deep/ .el-date-editor--daterange, /deep/ .el-cascader {
    width: 344px;
  }
  /deep/ .el-upload--picture-card {
    width: 70px;
    height: 70px;
    line-height: 75px;
    border: 1px solid #D0D0D0;
  }
  /deep/ .upload{
    width: 344px;
  }
}
/deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 70px;
  height: 70px;
}
.presell_protocol{
  cursor: pointer;
}
.check_protocal .icon{
  border-radius: 0;
  width: 16px;
  height: 16px;
}
.invoice_description img{
  display: block;
  margin:  0 auto;
  width: 100%;
  max-width: 100%;
}
.invoice_data_container{
  padding-left: 26px;
  padding-right: 26px;
}
.invoice_item{
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 14px;
  &:last-child{
    margin-right: 0;
  }
  .cont{
    position: relative;
    height: 32px;
    border: 1px solid #d3d3d3;
    display: flex;
    align-items: center;
    }
     &.checked{
        .cont{
          border-color: #e93323;
          color: #e93323;
        }
        .iconfont{
          display: block;
        }
      }
      .name{
        padding: 0 40px;
        font-size: 14px;
        line-height: 32px;
      }
      .iconfont{
        position: absolute;
        right: -3px;
        bottom: -12px;
        font-size: 22px;
        display: none;
      }
}
.invoice_type_info{
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  margin-top: 10px;
}
</style>
