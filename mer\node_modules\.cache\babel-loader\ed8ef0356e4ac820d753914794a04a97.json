{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue", "mtime": 1750488046426}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\.babelrc", "mtime": 1749087282000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\babel.config.js", "mtime": 1735790252000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.replace\");\nrequire(\"core-js/modules/es6.regexp.split\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nvar _createForOfIteratorHelper2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js\"));\nrequire(\"core-js/modules/es6.number.constructor\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: \"MarketingSettings\",\n  props: {\n    formValidate: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    OneattrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    ManyAttrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    goodList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    manyTabDate: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    specValue: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    open_svip: {\n      type: Boolean,\n      default: false\n    },\n    price: {\n      type: Number,\n      default: 0\n    },\n    svip_rate: {\n      type: Number,\n      default: 0\n    },\n    manyTabTit: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    formThead: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    extensionStatus: {\n      type: Number,\n      default: 0\n    },\n    deductionStatus: {\n      type: Number,\n      default: 0\n    },\n    deduction_set: {\n      type: Number,\n      default: -1\n    },\n    extension_one_rate: {\n      type: String,\n      default: \"\"\n    },\n    extension_two_rate: {\n      type: String,\n      default: \"\"\n    },\n    deduction_ratio_rate: {\n      type: Number,\n      default: 0\n    },\n    baseUrl: {\n      type: String,\n      required: \"\"\n    }\n  },\n  data: function data() {\n    return {\n      manyBrokerageTwo: 0,\n      manyBrokerage: 0,\n      keyNum: 0,\n      manyVipPrice: \"\"\n    };\n  },\n  methods: {\n    // 选择店铺推荐商品\n    openRecommend: function openRecommend() {\n      this.$emit(\"openRecommend\");\n    },\n    // 删除店铺推荐商品\n    deleteRecommend: function deleteRecommend(index) {\n      this.goodList.splice(index, 1);\n    },\n    addCoupon: function addCoupon() {\n      var _this = this;\n      _this.formValidate.give_coupon_ids = [];\n      _this.formValidate.couponData = [];\n      this.$modalCoupon(this.formValidate.couponData, \"wu\", _this.formValidate.give_coupon_ids, this.keyNum += 1, function (row) {\n        _this.$set(_this.formValidate, \"couponData\", row);\n        row.map(function (item) {\n          _this.formValidate.give_coupon_ids.push(item.coupon_id);\n        });\n        _this.$forceUpdate();\n      });\n    },\n    handleCloseCoupon: function handleCloseCoupon(tag) {\n      var _this2 = this;\n      this.formValidate.couponData.splice(this.formValidate.couponData.indexOf(tag), 1);\n      this.formValidate.give_coupon_ids = [];\n      this.formValidate.couponData.map(function (item) {\n        _this2.formValidate.give_coupon_ids.push(item.coupon_id);\n      });\n      this.$forceUpdate();\n    },\n    //设置会员价\n    onChangeSpecs: function onChangeSpecs(item) {\n      if (item == 1 || item == 2 && this.open_svip) {\n        this.OneattrValue[0]['svip_price'] = this.OneattrValue[0]['price'] ? this.accMul(this.OneattrValue[0]['price'], this.svip_rate) : 0;\n        var price = 0;\n        var _iterator = (0, _createForOfIteratorHelper2.default)(this.ManyAttrValue),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var val = _step.value;\n            price = val.price ? this.accMul(val.price, this.svip_rate) : 0;\n            this.$set(val, 'svip_price', price);\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      }\n    },\n    // 乘法\n    accMul: function accMul(arg1, arg2) {\n      var max = 0;\n      var s1 = arg1.toString();\n      var s2 = arg2.toString();\n      try {\n        max += s1.split('.')[1].length;\n      } catch (e) {}\n      try {\n        max += s2.split('.')[1].length;\n      } catch (e) {}\n      return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, max);\n    },\n    // 批量设置\n    batchSet: function batchSet() {\n      var _iterator2 = (0, _createForOfIteratorHelper2.default)(this.ManyAttrValue),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var val = _step2.value;\n          this.manyBrokerage != undefined && this.$set(val, \"extension_one\", this.manyBrokerage);\n          this.manyBrokerageTwo != undefined && this.$set(val, \"extension_two\", this.manyBrokerageTwo);\n          if (this.manyVipPrice != undefined) {\n            this.$set(val, \"svip_price\", this.manyVipPrice);\n          } else {\n            this.$set(val, \"svip_price\", (val.price * (this.manyVipDiscount / 100)).toFixed(2));\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n    },\n    // 切换积分抵扣\n    changeIntergral: function changeIntergral(e) {\n      if (e == -1) {\n        this.formValidate.integral_rate = -1;\n      } else {\n        this.formValidate.integral_rate = this.formValidate.integral_rate;\n      }\n    }\n  }\n};", null]}