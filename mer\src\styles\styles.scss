@charset "UTF-8";
.el-card{
  font-size: 14px;
}
.svg-icon{
  width: 0.8em !important;
  height: 0.8em !important;
}
.el-table--mini {
  font-size: 13px !important;
}
.selWidth{
  width: 280px;
}
.mobile-page{
  width: 100%;
}
.paddingBox{
  padding:0 10px 10px;
}
.mobile-config{
  width: 100%;
}
.c_label{
  font-size: 12px;
  color: #999999;
}
.c_label span{
  margin-left: 10px;
  color: #333;
}
.empty-box{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f5f7;
  border-radius: 6px;
}
.empty-box.on{
  border-radius: 0px;
}
.empty-box .iconfont-diy{
  color: #bbbfc8;
  font-size: 30px;
}
.c_row-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.acea-row {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-lines: multiple;
  -moz-box-lines: multiple;
  -o-box-lines: multiple;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  /* 辅助类 */
}
.acea-row.row-bottom {
  -webkit-box-align: end;
  -moz-box-align: end;
  -o-box-align: end;
  -ms-flex-align: end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}
.acea-row.row-between {
  -webkit-box-pack: justify;
  -moz-box-pack: justify;
  -o-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
/* 上下左右垂直居中 */
.acea-row.row-center-wrapper {
  -webkit-box-align: center;
  -moz-box-align: center;
  -o-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -o-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.acea-row.row-middle {
  -webkit-box-align: center;
  -moz-box-align: center;
  -o-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
/* 上下两边居中对齐 */
.acea-row.row-between-wrapper {
  -webkit-box-align: center;
  -moz-box-align: center;
  -o-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: justify;
  -moz-box-pack: justify;
  -o-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.divBox{
  box-sizing: border-box;
  .el-pagination{
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
  }
}
.seachTiele{
  font-size: 12px;
}
.el-divider--horizontal{
  margin: 19px 0;
}
.suibian-modal{
  .el-dialog__footer{
    display: none !important;
  }
}
.el-message-box__wrapper{
  overflow: auto;
}
.modal-form{
  width: 600px;
}
.modal-form .el-select{
  width: 100%;
}
.upload-form{
  min-width: 1000px;
  max-height:620px;
}
.common-form-upload .fr{
  margin-left: 15px;
}
.listPic{
  .image-slot{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.switchTable{
  .el-switch.is-disabled {
    opacity: 1;
  }
  .el-switch.is-disabled .el-switch__core, .el-switch.is-disabled .el-switch__label {
    cursor: pointer !important;;
  }
}
.line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/**
 * 上传图片的照相机
 */
.upLoadPicBox{
  display: inline-block;
  cursor: pointer;
  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 10px;
     img {
      width: 100%;
      height: 100%;
    }
  }
  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.fc-upload-btn{
  display: flex!important;
  align-items: center;
  justify-content: center;
}
.fc-upload-btn .el-icon-camera{
  font-size: 26px;
  color: #898989;
}
/**
 * 标题带下划线
 */
.dividerTitle{
  .title{
    border-bottom: 2px solid var(--prev-color-primary);
    padding: 0 8px 18px 5px;
    color: #000;
    font-size: 14px;
  }
}
.cameraIconfont{
  color: #898989;
  font-size: 26px;
}
.ml10{
  margin-left: 10px;
}
.mr10{
  margin-right: 10px;
}
.mb15{
  margin-bottom: 15px;
}
.mb20{
  margin-bottom: 20px;
}
.mb5{
  margin-bottom: 5px;
}
.mr15{
  margin-right: 15px;
}
.mt20{
  margin-top: 20px;
}
.mr50 {
  margin-right: 50px;
}
.mr20{
  margin-right: 20px !important;
}
.ml40 {
  margin-left: 40px !important;
}
.ml50{
  margin-left: 50px !important;
}
.mb10{
  margin-bottom: 10px;
}
.mr5{
  margin-right: 5px;
}
.pl25{
  padding-left: 25px;
  box-sizing: border-box;
}
a {
  color: var(--prev-color-primary);
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color .2s ease;
  // font-size: 12px;
}
.spBlock{
  display: block;
}
.onHand{
  cursor: pointer;
}
/**
 * 搜索框标题
 */
.seachTiele{
  line-height: 35px;
  // font-size: 12px;
}
/*switch样式*/
.el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
  font-size: 12px !important;
}
/*打开时文字位置设置*/
.el-switch__label--right {
  z-index: 1;
  font-size: 12px !important;
}
/*关闭时文字位置设置*/
.el-switch__label--left {
  z-index: 1;
  left: 19px;
  font-size: 12px !important;
}
/*显示文字*/
.el-switch__label.is-active {
  display: block;
  color: #fff;
  font-size: 12px;
}
.el-table__row .el-switch .el-switch__core,
.el-table__row .el-switch .el-switch__label {
  font-size: 12px;
  width: 55px!important;
}
.el-switch__label * {
  font-size: 12px;
}
/**
 * 表格下拉内容
 */
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 111px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 33.33%;
}
table .el-image{
  width: 36px;
  height: 36px;
}
//登录页动画
.index_bg {
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, .6) !important;
  z-index: 0 !important;
}

//表格下拉文字
.el-form-item__label, .el-form-item__content{
  font-size: 13px !important;
}
@font-face {
  font-family: "iconfont-shouye"; /* Project id 2955395 */
  src: url('//at.alicdn.com/t/font_2955395_hzsad8tzvr.woff2?t=1637567333533') format('woff2'),
       url('//at.alicdn.com/t/font_2955395_hzsad8tzvr.woff?t=1637567333533') format('woff'),
       url('//at.alicdn.com/t/font_2955395_hzsad8tzvr.ttf?t=1637567333533') format('truetype');
}

.iconfont-shouye {
  font-family: "iconfont-shouye" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-daochuwenjian-shouye:before {
  content: "\e608";
}

.icon-yihaotong-shouye:before {
  content: "\e609";
}

.icon-caiwuguanli-shouye:before {
  content: "\e60a";
}

.icon-fenxiaoguanli-shouye:before {
  content: "\e606";
}

.icon-shangpinguanli-shouye:before {
  content: "\e607";
}

.icon-yonghuguanli-shouye:before {
  content: "\e600";
}

.icon-duanxinpeizhi-shouye:before {
  content: "\e601";
}

.icon-wenzhangguanli-shouye:before {
  content: "\e602";
}

.icon-xitongshezhi-shouye:before {
  content: "\e603";
}

.icon-youhuiquan-shouye:before {
  content: "\e604";
}

.icon-dingdanguanli-shouye:before {
  content: "\e605";
}
.el-menu-item,.el-submenu__title {
  height: 50px;
  line-height: 50px;
}
.styleTwo,.styleTwo .el-menu--popup {
  width: 150px!important;
  padding: 0;
  max-height: auto;
}
.styleTwo .el-menu--popup{
  border-radius: 4px;
  background: #5F5F66!important;
}
.styleTwo{
  &::before{
    content: "";
    border: 10px solid transparent;
    border-right-color: #5F5F66;
    position: absolute;
    left: 0;
    top: 50px;
  }
}
.styleTwo .el-menu--popup-right-start{
  margin-left: 17px;
  margin-right: 12px;
}
.styleTwo li{
  background: #5f5f66!important;
  height: 46px!important;
  line-height: 46px!important;
  font-size: 13px;
  padding-left: 0!important;
}

.styleTwo .el-submenu__title{
  background: #5f5f66!important;
  height: 46px!important;
  line-height: 46px!important;
  font-size: 13px;
  color: #ffffff!important;
}

.styleTwo li:hover,.styleTwo .is-active{
  background: #77777D!important;
  .el-submenu__title{
    background: #77777D!important;
  }
}
.styleTwo .router-link-active{
  background: #ffffff;
}
#app .hideSidebar .style2 .el-submenu>.el-submenu__title{
  text-align: left!important;
}
.el-slider__button-wrapper{
  z-index: 100
}
.arrbox {
  background-color: white;
  font-size: 12px;
  border: 1px solid #dcdee2;
  border-radius: 6px;
  margin-bottom: 0px;
  padding: 0 5px;
  text-align: left;
  box-sizing: border-box;
}
.arrbox_ip {
  font-size: 12px;
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin: 0;
  max-width: inherit;
  min-width: 80px;
  vertical-align: top;
  height: 30px;
  color: #34495e;
  margin: 2px;
  line-height: 30px;
}
.arrbox_ip::placeholder {
  color: #ccc;
}
.line1 {
  white-space: nowrap;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}