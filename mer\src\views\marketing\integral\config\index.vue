<template>
  <div class="divBox">
    <el-card class="box-card">
      <!-- 条件渲染表单，当 FormData 存在时显示 -->
      <form-create
        v-if="FormData"
        ref="fc"
        v-loading="loading"
        :option="option"
        :rule="FormData.rule"
        class="formBox"
        handle-icon="false"
        @submit="onSubmit"
      />
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import formCreate from '@form-create/element-ui'
import { integralConfigApi } from '@/api/marketing'
import request from '@/api/request'

export default {
  name: 'IntegralConfig',
  data() {
    return {
      option: {
        form: {
          labelWidth: '150px',
          size: "small",
        },    
        global: {
          upload: {
            props: {
              onSuccess(rep, file) {
                if (rep.status === 200) {
                  file.url = rep.data.src
                }
              }
            }
          }
        }
      },
      FormData: null,
      loading: false,
      titles: ""
    }
  },
  components: {
    formCreate: formCreate.$form()
  },
  mounted() {
    this.getFrom();
  },
  watch:{
    // 监听路由变化
    '$route.path': {   
      handler: function() {
        this.getFrom();
      },
      immediate: false,
      deep: true
    },
  },
  methods: {
    getFrom() {
      this.loading = true
      integralConfigApi('integral').then(async res => {
        this.FormData = res.data
        this.loading = false
      }).catch(res => {
        this.$message.error(res.message)
        this.loading = false
      })
    },
    onSubmit(formData) {
      request[this.FormData.method.toLowerCase()](this.FormData.api, formData).then((res) => {
        this.$message.success(res.message || '提交成功')
      }).catch(err => {
        this.$message.error(err.message || '提交失败')
      })
    }
  }
}
</script>

<style scoped>

</style>
