// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const deliveryRouter =
  {
    path: `${roterPre}/delivery`,
    name: 'delivery',
    meta: {
      icon: '',
      title: leaveuKeyTerms['同城配送']
    },
    alwaysShow: true,
    component: Layout,
    children: [
      {
        path: 'store_manage',
        name: 'StoreManage',
        meta: {
          title: leaveuKeyTerms['第三方配送点']
        },
        component: () => import('@/views/cityDelivery/storeManage/index')
      },
      {
        path: 'personnel_manage',
        name: 'PersonnelManage',
        meta: {
          title: leaveuKeyTerms['配送员']
        },
        component: () => import('@/views/cityDelivery/personnelManage/index')
      },
      {
        path: 'usage_record',
        name: 'UsageRecord',
        meta: {
          title: leaveuKeyTerms['使用记录']
        },
        component: () => import('@/views/cityDelivery/usageRecord/index')
      },
      {
        path: 'recharge_record',
        name: 'RechargeRecord',
        meta: {
          title: leaveuKeyTerms['充值记录']
        },
        component: () => import('@/views/cityDelivery/rechargeRecord/index')
      }
    ]
  }

export default deliveryRouter
