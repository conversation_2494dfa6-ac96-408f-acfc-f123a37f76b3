<template>
  <div class="settled-wrapper wrapper_1200">
    <div class="title wrapper_1200">
      <nuxt-link class="home" to="/">首页 ></nuxt-link>
      {{title}}
    </div>
    <div class="settled-main">
      <div class="user-com-title">
        {{title}}
      </div>
      <div class="protocolMain">
        <div class="title">{{title}}</div>
        <div class="content">
          <div class="content-main" v-html="agreement"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: "privacyAgreement",
  auth: "guest",
  data() {
    return {
      agreement: '',
      title: '用户协议与隐私政策'
    };
  },
  async asyncData({app,query}){
    return {
      type: query.type,
      title: query.type == 'sys_user_agree' ? '用户协议' : '隐私政策'
    }
  },
  fetch({store}) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: this.title + "-" + this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getAgreement();
  },
  mounted() {
    this.$store.commit("isLogin", false);
  },
  methods: {
    //获取入驻协议内容
    getAgreement() {
      let that = this;
      that.$axios.get("/api/agreement/"+that.type).then(res => {
        that.agreement = that.type == 'sys_user_agree' ? res.data.sys_user_agree : res.data.sys_userr_privacy
      }).catch(err => {
        that.$message.error(err);
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.settled-wrapper {
  .title{
    height: 60px;
    line-height: 60px;
  }
  .settled-main{
    padding: 50px 110px;
    background: #fff;
  }
  .user-com-title {
    font-size: 26px;
    color: #333333;
    text-align: center;
    border: none;
  }
  .content-main{
    color: #333333;
    font-size: 14px;
  }
}
.settled-wrapper .content-main ::v-deep strong {
  font-weight: bold;
}
.footer{
  text-align: center;
}
</style>
