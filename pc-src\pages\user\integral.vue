<template>
    <div class="balance-wrapper">
      <div class="user-com-title">我的积分</div>
      <div class="balance-box">
        <div class="money-box" v-if="$auth.user">
          <div class="money-item">
            <span>当前积分 <span class="iconfont icon-duoshanghupc-shuomingdanchuang" @click="showProtocal = true"></span></span>
            <p>{{userInfo.integral || 0}}</p>
          </div>
          <div class="money-item">
            <span>累计积分</span>
            <p>{{userInfo.totalGainIntegral || 0}}</p>
          </div>
          <div class="money-item">
            <span>累计消费</span>
            <p>{{userInfo.deductionIntegral || 0}}</p>
          </div>
          <div class="money-item">
            <span>冻结积分</span>
            <p>{{userInfo.lockIntegral || 0}}</p>
          </div>
        </div>
        <div class="tab-box">
          <div class="hd clearfix">
            <div class="hd-item" :class="{on:tabCur == item.key}" v-for="(item,index) in tabList" :key="index">{{item.title}}</div>
          </div>
          <div class="bd">
            <div class="bd-item">
              <div class="list">
                <ul>
                  <li v-for="(list, index) in list" :key="index">
                    <span class="txt">{{list.mark}}</span>
                    <p class="txt-time">{{list.create_time}}</p>
                    <span class="num" :class="{green:list.pm}"> {{list.pm?'+':'-'}} ¥{{list.number}}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="pages-box" v-if="total > 0">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="bindPageCur"
            :current-page.sync="currentPage"
            :pageSize="limit"
            :total="total">
          </el-pagination>
        </div>
      </div>
      <div class="empty-box" v-if="total == 0">
        <img src="~/assets/images/noyue.png" alt="">
        <p>亲，暂无积分明细哟~</p>
      </div>
      <!--积分说明-->
      <div v-if="showProtocal" class="protocolModal">
        <div class="protocolCount">
          <div class="protocolMain">
            <div class="title">{{integralTitle || '积分说明'}}</div>
            <div class="content">
              <div class="content-main" v-html="agreement"></div>
            </div>
            <div class="sureBbtn">
              <el-button type="primary" @click="showProtocal=false;">我知道了</el-button>
            </div>
          </div>

        </div>
      </div>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "balance",
      auth: "guest",
      data(){
        return {
          tabCur:0,
          tabList:[
            {
              title:'分值明细',
              key:0
            }
          ],
          list:[],
          page:1,
          limit:10,
          total:0,
          userInfo:{},
          currentPage:1,
          showProtocal: false,
          agreement: '',
          integralTitle: ''
        }
      },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "我的积分-"+this.$store.state.titleCon
        }
      },
      beforeMount() {
        this.getList();
        this.getUserInfo();
        this.getAgreement();
      },
      methods:{
        getUserInfo(){
          this.$axios.get("/api/user/integral/info").then(res=>{
            this.userInfo = res.data;
          })
        },
        bindTab(item){
          this.tabCur= item.key;
          this.page = 1;
          this.currentPage = 1;
          this.getList();
        },
        //获取积分说明内容
        getAgreement() {
          let that = this;
          that.$axios.get("/api/agreement/sys_integral_rule").then(res => {
            that.agreement = res.data.sys_integral_rule
            that.integralTitle = res.data.title
          }).catch(err => {
            that.$message.error(err);
          });
        },
        getList(){
          this.$axios.get(`/api/user/integral/lst`,{
            params:{
              page:this.page,
              limit:this.limit
            }
          }).then(res=>{
            this.total = res.data.count || 0
            this.list = res.data.list
          })
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          this.getList()
        }
      }
    }
</script>

<style lang="scss" scoped>
.balance-wrapper{
  .balance-box{
    padding-top: 45px;
    .money-box{
      .money-item{
        display: inline-block;
        width: 24%;
        .icon-duoshanghupc-shuomingdanchuang{
          color: #E93323;
          font-size: 13px;
          cursor: pointer;
        }
        span{
          color: #969696;
          font-size: 14px;
        }
        p{
          margin-top: 10px;
          color: #282828;
          font-size: 32px;
        }
      }
    }
    .tab-box{
      // padding-right: 54px;
      margin-top: 35px;
      .hd{
        padding-bottom: 15px;
        border-bottom: 1px solid #ECECEC;
        .hd-item{
          position: relative;
          float: left;
          padding: 0 10px;
          margin-right: 30px;
          text-align: center;
          font-size: 16px;
          color: #999999;
          cursor: pointer;
          &.on{
            color: #E93323;
            &:after{
              content: ' ';
              position: absolute;
              left: 0;
              bottom: -16px;
              width:100%;
              height: 2px;
              background: #E93323;
            }
          }
        }
      }
      .bd{
        .bd-item{
          margin-top: 20px;
          .time{
            height: 36px;
            line-height: 36px;
            background: #F4F4F4;
            color: #969696;
            font-size: 14px;
            padding: 0 10px;
          }
          .list li{
            position: relative;
            padding: 18px 10px;
            border-bottom: 1px dashed #D0D0D0;
            .txt{
              color: #282828;
              font-size: 16px;
            }
            .txt-time{
              margin-top: 10px;
              color: #969696;
              font-size: 14px;
            }
            .num{
              position: absolute;
              right: 10px;
              top: 50%;
              margin-top: -8px;
              font-size: 16px;
              font-weight: bold;
              color: #E93323;
            }
            .green{
              color: #43B36D;
            }
          }
        }
      }
    }
  }
}
.pages-box{
  padding-right: 54px;
  margin-top: 30px;
  text-align: right;
}
.protocolModal {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  .protocolCount {
    width: 900px;
    height: 700px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -320px;
    margin-left: -450px;
    .protocolMain {
      width: 100%;
      height: 710px;
      background: #fff;
      border-radius: 6px;
      padding: 0 20px;
      .title {
        padding: 20px 0;
        text-align: center;
        color: #333333;
        font-size: 20px;
        font-weight: bold;
      }

      .content-main {
        color: #333;
        line-height: 20px;
        overflow-y: auto;
        height: 500px;
        
      }
    }
  }
  .sureBbtn {
    text-align: center;
    margin-top: 30px;
    .el-button {
      width: 180px;
      height: 46px;
    }
  }
}
.protocolModal .protocolCount .content-main ::v-deep img{
    max-width: 100%;
}
</style>
