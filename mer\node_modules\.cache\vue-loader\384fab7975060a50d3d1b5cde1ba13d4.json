{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue?vue&type=template&id=7037b1e9&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue", "mtime": 1750488046426}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-row\",\n    [\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: _vm.$t(\"商品推荐：\") } },\n            [\n              _c(\"el-checkbox\", {\n                attrs: {\n                  \"true-label\": 1,\n                  \"false-label\": 0,\n                  label: _vm.$t(\"店铺推荐\"),\n                },\n                model: {\n                  value: _vm.formValidate.is_good,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.formValidate, \"is_good\", $$v)\n                  },\n                  expression: \"formValidate.is_good\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"form-tip\" }, [\n                _vm._v(\n                  _vm._s(\n                    _vm.$t(\n                      \"设置后，该商品会在店铺中其他商品详情店铺推荐备选列表中展示\"\n                    )\n                  )\n                ),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _c(\"el-form-item\", { attrs: { label: _vm.$t(\"关联推荐：\") } }, [\n            _c(\n              \"div\",\n              { staticClass: \"acea-row row-middle\" },\n              [\n                _c(\n                  \"div\",\n                  { staticClass: \"acea-row\" },\n                  [\n                    _vm._l(_vm.goodList, function (item, index) {\n                      return _c(\n                        \"div\",\n                        { key: \"good-\" + index, staticClass: \"pictrue\" },\n                        [\n                          _c(\"img\", { attrs: { src: item.image } }),\n                          _vm._v(\" \"),\n                          _c(\"i\", {\n                            staticClass: \"el-icon-error btndel\",\n                            on: {\n                              click: function ($event) {\n                                return _vm.deleteRecommend(index)\n                              },\n                            },\n                          }),\n                        ]\n                      )\n                    }),\n                    _vm._v(\" \"),\n                    _vm.goodList.length < 18\n                      ? _c(\"div\", { staticClass: \"uploadCont\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"upLoadPicBox\",\n                              on: { click: _vm.openRecommend },\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"upLoad\" }, [\n                                _c(\"i\", {\n                                  staticClass: \"el-icon-camera cameraIconfont\",\n                                }),\n                              ]),\n                            ]\n                          ),\n                        ])\n                      : _vm._e(),\n                  ],\n                  2\n                ),\n                _vm._v(\" \"),\n                _c(\n                  \"el-popover\",\n                  {\n                    attrs: {\n                      placement: \"bottom\",\n                      title: \"\",\n                      \"min-width\": \"200\",\n                      trigger: \"hover\",\n                    },\n                  },\n                  [\n                    _c(\"img\", {\n                      staticStyle: { height: \"270px\" },\n                      attrs: {\n                        src: _vm.baseUrl + \"/static/images/store-recommend.png\",\n                        alt: \"\",\n                      },\n                    }),\n                    _vm._v(\" \"),\n                    _c(\n                      \"el-button\",\n                      {\n                        staticClass: \"ml14\",\n                        staticStyle: { \"font-size\": \"12px\" },\n                        attrs: { slot: \"reference\", type: \"text\" },\n                        slot: \"reference\",\n                      },\n                      [_vm._v(_vm._s(_vm.$t(\"查看示例\")))]\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              1\n            ),\n            _vm._v(\" \"),\n            _c(\"div\", { staticClass: \"form-tip\" }, [\n              _vm._v(\n                _vm._s(\n                  _vm.$t(\"设置后，该商品详情页【店铺推荐】会展示选中的商品\")\n                )\n              ),\n            ]),\n          ]),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _vm.deductionStatus > 0\n            ? _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"积分抵扣：\") } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      on: {\n                        change: function ($event) {\n                          return _vm.changeIntergral(_vm.deduction_set)\n                        },\n                      },\n                      model: {\n                        value: _vm.deduction_set,\n                        callback: function ($$v) {\n                          _vm.deduction_set = $$v\n                        },\n                        expression: \"deduction_set\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-radio\",\n                        { staticClass: \"radio\", attrs: { label: 1 } },\n                        [_vm._v(_vm._s(_vm.$t(\"单独设置\")))]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: -1 } }, [\n                        _vm._v(_vm._s(_vm.$t(\"默认设置\"))),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm.deduction_set == 1\n                    ? _c(\n                        \"div\",\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: {\n                              min: 0,\n                              size: \"small\",\n                              \"controls-position\": \"right\",\n                              placeholder: _vm.$t(\"请输入抵扣比例\"),\n                            },\n                            model: {\n                              value: _vm.formValidate.integral_rate,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"integral_rate\", $$v)\n                              },\n                              expression: \"formValidate.integral_rate\",\n                            },\n                          }),\n                          _vm._v(\"\\n        %\\n      \"),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.deduction_set == -1\n                    ? _c(\n                        \"span\",\n                        {\n                          staticClass: \"form-tip\",\n                          staticStyle: { color: \"#F56464\" },\n                        },\n                        [\n                          _vm._v(\n                            \"（店铺统一设置，抵扣比例\" +\n                              _vm._s(_vm.deduction_ratio_rate) +\n                              \"%）\"\n                          ),\n                        ]\n                      )\n                    : _vm._e(),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              staticClass: \"proCoupon\",\n              attrs: { label: _vm.$t(\"商品赠券：\") },\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"acea-row\" },\n                [\n                  _vm._l(_vm.formValidate.couponData, function (tag, index) {\n                    return _c(\n                      \"el-tag\",\n                      {\n                        key: index,\n                        staticClass: \"mr10\",\n                        attrs: { closable: \"\", \"disable-transitions\": false },\n                        on: {\n                          close: function ($event) {\n                            return _vm.handleCloseCoupon(tag)\n                          },\n                        },\n                      },\n                      [_vm._v(_vm._s(_vm.$t(tag.title)) + \"\\n        \")]\n                    )\n                  }),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      staticClass: \"mr15\",\n                      attrs: { size: \"mini\" },\n                      on: { click: _vm.addCoupon },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"选择优惠券\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"form-tip\" }, [\n                    _vm._v(_vm._s(_vm.$t(\"设置购买该商品可默认赠送的优惠券\"))),\n                  ]),\n                ],\n                2\n              ),\n            ]\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: _vm.$t(\"开启礼包：\") } },\n            [\n              _c(\n                \"el-radio-group\",\n                {\n                  attrs: { disabled: _vm.$route.params.id ? true : false },\n                  model: {\n                    value: _vm.formValidate.is_gift_bag,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.formValidate, \"is_gift_bag\", $$v)\n                    },\n                    expression: \"formValidate.is_gift_bag\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-radio\",\n                    { staticClass: \"radio\", attrs: { label: 0 } },\n                    [_vm._v(_vm._s(_vm.$t(\"否\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-radio\", { attrs: { label: 1 } }, [\n                    _vm._v(_vm._s(_vm.$t(\"是\"))),\n                  ]),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"form-tip\" }, [\n                _vm._v(_vm._s(_vm.$t(\"1. 选择开启礼包后，不可修改\"))),\n              ]),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"form-tip\" }, [\n                _vm._v(\n                  \"\\n        2.\\n        用户购买该分销礼包商品后，可自动成为分销员（即已成为分销员的用户在移动端看不到该分销礼包商品）\\n      \"\n                ),\n              ]),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"form-tip\" }, [\n                _vm._v(\n                  \"\\n        3.\\n        该商品设置为分销礼包后会展示在平台后台的【分销】-【分销礼包】（即不会展示在平台后台-【商品列表】）\\n      \"\n                ),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _vm.extensionStatus > 0\n            ? _c(\n                \"el-form-item\",\n                {\n                  attrs: {\n                    label: _vm.$t(\"佣金设置：\"),\n                    props: \"extension_type\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      on: {\n                        change: function ($event) {\n                          return _vm.onChangetype(\n                            _vm.formValidate.extension_type\n                          )\n                        },\n                      },\n                      model: {\n                        value: _vm.formValidate.extension_type,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.formValidate, \"extension_type\", $$v)\n                        },\n                        expression: \"formValidate.extension_type\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-radio\",\n                        { staticClass: \"radio\", attrs: { label: 1 } },\n                        [_vm._v(_vm._s(_vm.$t(\"单独设置\")))]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 0 } }, [\n                        _vm._v(_vm._s(_vm.$t(\"默认设置\"))),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm.formValidate.extension_type == 0\n                    ? _c(\n                        \"span\",\n                        {\n                          staticClass: \"form-tip\",\n                          staticStyle: { color: \"#F56464\" },\n                        },\n                        [\n                          _vm._v(\n                            \"（平台设置，一级佣金\" +\n                              _vm._s(_vm.extension_one_rate) +\n                              \"%，二级佣金\" +\n                              _vm._s(_vm.extension_two_rate) +\n                              \"%）\"\n                          ),\n                        ]\n                      )\n                    : _vm._e(),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { span: 24 } },\n        [\n          _vm.open_svip\n            ? _c(\n                \"el-form-item\",\n                {\n                  attrs: {\n                    label: _vm.$t(\"付费会员价：\"),\n                    props: \"svip_price_type\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      on: {\n                        change: function ($event) {\n                          return _vm.onChangeSpecs(\n                            _vm.formValidate.svip_price_type\n                          )\n                        },\n                      },\n                      model: {\n                        value: _vm.formValidate.svip_price_type,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.formValidate, \"svip_price_type\", $$v)\n                        },\n                        expression: \"formValidate.svip_price_type\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-radio\",\n                        { staticClass: \"radio\", attrs: { label: 0 } },\n                        [_vm._v(_vm._s(_vm.$t(\"不设置\")))]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [\n                        _vm._v(_vm._s(_vm.$t(\"自定义设置\"))),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-radio\",\n                        { staticClass: \"radio\", attrs: { label: 1 } },\n                        [_vm._v(_vm._s(_vm.$t(\"默认设置\")))]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"span\",\n                    {\n                      staticClass: \"form-tip\",\n                      staticStyle: { color: \"#F56464\" },\n                    },\n                    [\n                      _vm._v(\n                        \"（店铺统一设置，\" + _vm._s(_vm.svip_rate * 10) + \"折）\"\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _vm.formValidate.spec_type === 1\n        ? _c(\n            \"el-col\",\n            { attrs: { xl: 24, lg: 24, md: 24, sm: 24, xs: 24 } },\n            [\n              (_vm.open_svip && _vm.formValidate.svip_price_type == 2) ||\n              (_vm.extensionStatus > 0 && _vm.formValidate.extension_type == 1)\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"批量设置：\") } },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"acea-row\" },\n                        [\n                          _vm.open_svip && _vm.formValidate.svip_price_type == 2\n                            ? _c(\n                                \"div\",\n                                { staticClass: \"mr15 acea-row row-middle\" },\n                                [\n                                  _c(\"span\", [\n                                    _vm._v(_vm._s(_vm.$t(\"会员价：\"))),\n                                  ]),\n                                  _vm._v(\" \"),\n                                  _c(\"el-input-number\", {\n                                    staticStyle: { width: \"150px\" },\n                                    attrs: {\n                                      min: 0,\n                                      size: \"small\",\n                                      \"controls-position\": \"right\",\n                                    },\n                                    model: {\n                                      value: _vm.manyVipPrice,\n                                      callback: function ($$v) {\n                                        _vm.manyVipPrice = $$v\n                                      },\n                                      expression: \"manyVipPrice\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _vm.extensionStatus > 0 &&\n                          _vm.formValidate.extension_type == 1\n                            ? [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"mr15\" },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(_vm._s(_vm.$t(\"一级返佣：\"))),\n                                    ]),\n                                    _vm._v(\" \"),\n                                    _c(\"el-input-number\", {\n                                      staticClass: \"input-number-with-text\",\n                                      attrs: {\n                                        min: 0,\n                                        size: \"small\",\n                                        \"controls-position\": \"right\",\n                                      },\n                                      model: {\n                                        value: _vm.manyBrokerage,\n                                        callback: function ($$v) {\n                                          _vm.manyBrokerage = $$v\n                                        },\n                                        expression: \"manyBrokerage\",\n                                      },\n                                    }),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"mr15\" },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(_vm._s(_vm.$t(\"二级返佣：\"))),\n                                    ]),\n                                    _vm._v(\" \"),\n                                    _c(\"el-input-number\", {\n                                      staticClass: \"input-number-with-text\",\n                                      attrs: {\n                                        min: 0,\n                                        size: \"small\",\n                                        \"controls-position\": \"right\",\n                                      },\n                                      model: {\n                                        value: _vm.manyBrokerageTwo,\n                                        callback: function ($$v) {\n                                          _vm.manyBrokerageTwo = $$v\n                                        },\n                                        expression: \"manyBrokerageTwo\",\n                                      },\n                                    }),\n                                  ],\n                                  1\n                                ),\n                              ]\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _c(\n                            \"div\",\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"primary\", size: \"small\" },\n                                  on: { click: _vm.batchSet },\n                                },\n                                [_vm._v(_vm._s(_vm.$t(\"批量设置\")))]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        2\n                      ),\n                    ]\n                  )\n                : _vm._e(),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-col\",\n        { attrs: { xl: 24, lg: 24, md: 24, sm: 24, xs: 24 } },\n        [\n          _vm.formValidate.spec_type === 0\n            ? _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-table\",\n                    {\n                      staticClass: \"tabNumWidth\",\n                      attrs: {\n                        data: _vm.OneattrValue,\n                        border: \"\",\n                        size: \"mini\",\n                      },\n                    },\n                    [\n                      _c(\"el-table-column\", {\n                        attrs: {\n                          align: \"center\",\n                          label: _vm.$t(\"图片\"),\n                          \"min-width\": \"80\",\n                        },\n                        scopedSlots: _vm._u(\n                          [\n                            {\n                              key: \"default\",\n                              fn: function (scope) {\n                                return [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"upLoadPicBoxspecPictrue\" },\n                                    [\n                                      scope.row.image\n                                        ? _c(\n                                            \"div\",\n                                            { staticClass: \"pictrue tabPic\" },\n                                            [\n                                              _c(\"img\", {\n                                                attrs: { src: scope.row.image },\n                                              }),\n                                            ]\n                                          )\n                                        : _c(\"div\", [\n                                            _vm._v(\n                                              \"\\n                --\\n              \"\n                                            ),\n                                          ]),\n                                    ]\n                                  ),\n                                ]\n                              },\n                            },\n                          ],\n                          null,\n                          false,\n                          1905554668\n                        ),\n                      }),\n                      _vm._v(\" \"),\n                      _vm._l(_vm.specValue, function (item, iii) {\n                        return _c(\"el-table-column\", {\n                          key: iii,\n                          attrs: {\n                            label:\n                              _vm.formThead[iii] && _vm.formThead[iii].title,\n                            align: \"center\",\n                            \"min-width\": \"110\",\n                          },\n                          scopedSlots: _vm._u(\n                            [\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\"div\", [_vm._v(_vm._s(scope.row[iii]))]),\n                                  ]\n                                },\n                              },\n                            ],\n                            null,\n                            true\n                          ),\n                        })\n                      }),\n                      _vm._v(\" \"),\n                      _vm.formValidate.svip_price_type != 0\n                        ? [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"付费会员价\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            disabled:\n                                              _vm.formValidate\n                                                .svip_price_type == 1,\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.svip_price,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"svip_price\",\n                                                $$v\n                                              )\n                                            },\n                                            expression: \"scope.row.svip_price\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3837625467\n                              ),\n                            }),\n                          ]\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.formValidate.extension_type === 1\n                        ? [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"一级返佣(元)\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.extension_one,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"extension_one\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.extension_one\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3204804055\n                              ),\n                            }),\n                            _vm._v(\" \"),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"二级返佣(元)\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.extension_two,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"extension_two\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.extension_two\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3908853119\n                              ),\n                            }),\n                          ]\n                        : _vm._e(),\n                    ],\n                    2\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.formValidate.spec_type === 1\n            ? _c(\n                \"el-form-item\",\n                {\n                  staticClass: \"labeltop\",\n                  attrs: { label: _vm.$t(\"规格列表：\") },\n                },\n                [\n                  _c(\n                    \"el-table\",\n                    {\n                      key: \"2\",\n                      ref: \"specsTable\",\n                      staticClass: \"tabNumWidth\",\n                      attrs: {\n                        data: _vm.ManyAttrValue.slice(1),\n                        border: \"\",\n                        size: \"small\",\n                      },\n                    },\n                    [\n                      _vm.manyTabDate\n                        ? _vm._l(_vm.manyTabDate, function (item, iii) {\n                            return _c(\"el-table-column\", {\n                              key: iii,\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.manyTabTit[iii].title,\n                                \"min-width\": \"80\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"div\", [\n                                          _c(\"span\", [\n                                            _vm._v(\n                                              _vm._s(\n                                                scope.row.detail[\n                                                  _vm.manyTabTit[iii].title\n                                                ]\n                                              )\n                                            ),\n                                          ]),\n                                        ]),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                true\n                              ),\n                            })\n                          })\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _c(\"el-table-column\", {\n                        attrs: {\n                          align: \"center\",\n                          label: _vm.$t(\"图片\"),\n                          \"min-width\": \"80\",\n                        },\n                        scopedSlots: _vm._u(\n                          [\n                            {\n                              key: \"default\",\n                              fn: function (scope) {\n                                return [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"upLoadPicBox specPictrue\" },\n                                    [\n                                      scope.row.image || scope.row.pic\n                                        ? _c(\n                                            \"div\",\n                                            { staticClass: \"pictrue tabPic\" },\n                                            [\n                                              _c(\"img\", {\n                                                attrs: {\n                                                  src:\n                                                    scope.row.image ||\n                                                    scope.row.pic,\n                                                },\n                                              }),\n                                            ]\n                                          )\n                                        : _c(\"div\", [\n                                            _vm._v(\n                                              \"\\n                --\\n              \"\n                                            ),\n                                          ]),\n                                    ]\n                                  ),\n                                ]\n                              },\n                            },\n                          ],\n                          null,\n                          false,\n                          2690197292\n                        ),\n                      }),\n                      _vm._v(\" \"),\n                      _vm._l(_vm.specValue, function (item, iii) {\n                        return _c(\"el-table-column\", {\n                          key: iii,\n                          attrs: {\n                            label: _vm.formThead[iii].title,\n                            align: \"center\",\n                            \"min-width\": \"110\",\n                          },\n                          scopedSlots: _vm._u(\n                            [\n                              {\n                                key: \"default\",\n                                fn: function (scope) {\n                                  return [\n                                    _c(\"div\", [_vm._v(_vm._s(scope.row[iii]))]),\n                                  ]\n                                },\n                              },\n                            ],\n                            null,\n                            true\n                          ),\n                        })\n                      }),\n                      _vm._v(\" \"),\n                      _vm.formValidate.svip_price_type != 0\n                        ? [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"付费会员价\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            disabled:\n                                              _vm.formValidate\n                                                .svip_price_type == 1,\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.svip_price,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"svip_price\",\n                                                $$v\n                                              )\n                                            },\n                                            expression: \"scope.row.svip_price\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3837625467\n                              ),\n                            }),\n                          ]\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.formValidate.extension_type === 1\n                        ? [\n                            _c(\"el-table-column\", {\n                              key: \"1\",\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"一级返佣(元)\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.extension_one,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"extension_one\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.extension_one\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3204804055\n                              ),\n                            }),\n                            _vm._v(\" \"),\n                            _c(\"el-table-column\", {\n                              key: \"2\",\n                              attrs: {\n                                align: \"center\",\n                                label: _vm.$t(\"二级返佣(元)\"),\n                                \"min-width\": \"120\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _c(\"el-input-number\", {\n                                          staticClass: \"priceBox\",\n                                          attrs: {\n                                            min: 0,\n                                            size: \"small\",\n                                            \"controls-position\": \"right\",\n                                          },\n                                          model: {\n                                            value: scope.row.extension_two,\n                                            callback: function ($$v) {\n                                              _vm.$set(\n                                                scope.row,\n                                                \"extension_two\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"scope.row.extension_two\",\n                                          },\n                                        }),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                false,\n                                3908853119\n                              ),\n                            }),\n                          ]\n                        : _vm._e(),\n                    ],\n                    2\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}