<template>
  <div class="importedGoods">
    <el-table
      ref="table"
      v-loading="listLoading"
      :data="tableData.data"
      style="width: 100%"
      size="small"
      highlight-current-row
    >
      <el-table-column prop="broadcast_goods_id" label="ID" min-width="50" />
      <el-table-column :label="$t('商品图')" min-width="60">
        <template slot-scope="scope">
          <el-image
            style="width: 36px; height: 36px"
            :src="scope.row.goods && scope.row.goods.cover_img"
          />
        </template>
      </el-table-column>
      <el-table-column prop="goods.name" :label="$t('商品名称')" min-width="120" />
      <el-table-column :label="$t('库存')" min-width="50">
        <template slot-scope="scope">
          <span>{{ scope.row.goods && scope.row.goods.product && scope.row.goods.product.stock }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="goods.price" :label="$t('直播价')" min-width="50" />
      <el-table-column prop="goods.pay_num" :label="$t('销售数量')" min-width="60" />
      <el-table-column prop="goods.pay_price" :label="$t('销售金额')" min-width="60" />
      <el-table-column :label="$t('上下架')" min-width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.on_sale"
            :active-value="1"
            :inactive-value="0"
            :width="55"
            active-text="上架"
            inactive-text="下架"
            @change="onchangeIsShow(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <div class="block mb20">
      <el-pagination
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { studioProList, broadcastGoodsDeleteApi, studioProShowApi } from "@/api/marketing";
export default {
  name: "GoodsList",
  data() {
    return {
      listLoading: true,
      multipleSelection: [],
      tableData: {
        data: [],
        total: 0,
      },
      tableFrom: {
        page: 1,
        limit: 3,
        keyword: "",
      },
    };
  },
  props: {
    broadcast_room_id:{
      type: Number,
    },
    type: {
      type: String,
      default: 'detail'
    } 
  },
  watch: {
    broadcast_room_id: {
      deep: true,
      handler(val) {
        this.getList()
      }
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList() {
      this.listLoading = true;
      studioProList(this.broadcast_room_id,this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list;
          this.tableData.total = res.data.count;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 上下架
    onchangeIsShow(row) {
      studioProShowApi(row.broadcast_room_id, { goods_id: row.broadcast_goods_id, status: row.on_sale }).then(({ message }) => {
        this.$message.success(message)
        this.getList()
      }).catch(({ message }) => {
        this.$message.error(message)
      })
    },
    // 删除
    handleDelete(item, idx) {
      this.$modalSureDelete(this.$t('确定删除该商品')).then(() => {
        broadcastGoodsDeleteApi({ room_id: item.broadcast_room_id, id: item.broadcast_goods_id }).then(({ message }) => {
          this.$message.success(message);
          this.tableData.data.splice(idx, 1)
        }).catch(({ message }) => {
          this.$message.error(message);
        });
      })  
    },
  }
};
</script>

<style scoped lang="scss">

</style>
