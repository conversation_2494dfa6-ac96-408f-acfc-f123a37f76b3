<template>
  <div class="store-wrapper">
      <div class="store-count">
        <div class="sort acea-row">
          <div class="name">排序：</div>
          <div class="acea-row sort-count">
            <div class="item" :class="iSdefaults === 0 ? 'font-color' : ''" @click="defaults">默认</div>
            <div class="item" :class="iSdefaults === 1 ? 'font-color' : ''" @click="salesSort('sales')" >
            销量
            </div>
            <div class="item" :class="iSdefaults === 2 ? 'font-color' : ''" @click="scoreSort('rate')">
            评分
            </div>
            <div class="item" :class="iSdefaults === 3 ? 'font-color' : ''" @click="productNews">新品</div>
            <div class="item" :class="iSdefaults === 4 ? 'font-color' : ''" @click="priceSort('price_desc', 1)" v-if="priceOrder === 'price_asc'">
            价格
            <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-jiageshaixuanshang"></use>
            </svg>
            </div>
            <div class="item" :class="iSdefaults === 4 ? 'font-color' : ''" @click="priceSort('price_desc', 2)" v-else-if="priceOrder === ''">
            价格
            <span class="iconfont icon-jiageshaixuan"></span>
            </div>
            <div class="item" :class="iSdefaults === 4 ? 'font-color' : ''" @click="priceSort('price_asc', 3)" v-else-if="priceOrder === 'price_desc'">
            价格
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-jiageshaixuanxia"></use>
            </svg>
            </div>
            <div class="item price-range">
              <div class="price-count">
                <el-input class="input" v-model.number="price_on" @input="change($event)"></el-input> -
                <el-input class="input" v-model.number="price_off" @input="change($event)"></el-input>
              </div>
              <div class="action-box">
                <span class="action-btn clear" @click="clearPrice">清空</span>
                <span class="action-btn submit" @click="getProductslist('')">确定</span>
              </div>
            </div>
          </div>
      </div>
      <div class="store-recommend">
        <div class="goods acea-row row-middle" v-if="productslist.length">
          <div class="item" v-for="(item, index) in productslist" :key="index" @click="goDetail(item)">
            <div class="pictrue"><img :src="item.image"></div>
            <div class="money acea-row row-between-wrapper">
              <div v-if="item.show_svip_info.show_svip_price && item.svip_price" class="svip acea-row">
                <span class="font-color">¥{{item.svip_price}}</span>
                <img src="@/assets/images/svip.png">
              </div>  
              <div v-else><span class="font-color">¥{{item.price}}</span></div>
              <div class="label font-color" v-if="item.issetCoupon && 'coupon_id' in item.issetCoupon">券</div>
            </div>
            <div class="name line2"><span v-if="item.product_type == 1" class="trader">秒杀</span>{{item.store_name}}</div>
            <div class="bottom acea-row row-between-wrapper">
              <div>{{item.sales}}人付款</div>
              <div>{{item.rate}}分</div>
            </div>
          </div>
        </div>
        <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
      </div>
      <div class="noGoods" v-if="!productslist.length">
        <div class="pictrue">
          <img src="~/assets/images/noGoods.png">
        </div>
        <div class="name">亲，该分类暂无商品哟~</div>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "storeHome",
      auth: "guest",
      data(){
        return {
            swiperData: [],
            swiperOption: {
            pagination: {
            el: ".paginationBanner",
            clickable: true
            },
            navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev"
            },
            autoplay: {
            disableOnInteraction: false,
            delay: 5000
            },
            loop: true,
            speed: 1000,
            observer: true,
            observeParents: true
        },
        swiperScroll: {
            navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev"
            },
            freeMode: true,
            freeModeMomentum: false,
            slidesPerView: "auto",
            observer: true,
            observeParents: true
        },
        priceOrder: '',
        productslist:[],
        page: 1, //代表页面的初始页数
        limit:12,
        mer_cate_id: '',
        keyword: '',
        total: 0,
        iSdefaults:0,
        order: '',
        price_on: '',
        price_off: '',
        news:0,
        cid: '',
        sid: ''
        }
      },
      async asyncData({ app, query }) {
        return {
          id: query.id,
          keyword: query.title ? query.title : query.search ? query.search : '',
          search: query.search ? query.search : '',
          mer_cate_id: query.cateId ? query.cateId : ''
        };
      },
      watch: {
        "$route": function(newVal,oldVal){
          this.mer_cate_id = newVal.query.cateId ? newVal.query.cateId : '';
          this.id = newVal.query.id ? newVal.query.id : '';
          this.keyword = newVal.query.title ? newVal.query.title : newVal.query.search ? newVal.query.search : '';
          this.getProductslist(1);
        }
      },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "店铺-"+this.$store.state.titleCon
        }
      },
      beforeMount() {
        this.getProductslist('');
      },
      methods:{
        goDetail: function (item) {
          if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
          }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
          }
        },
        getProductslist(num){
          let _this = this;
          _this.page = num ? num : _this.page;
          let currentPage = {page: _this.page,limit: _this.limit,mer_cate_id: this.mer_cate_id,order: this.order,
                            price_on: this.price_on, price_off: this.price_off,keyword : this.keyword, common: 1};
          _this.$axios.get('/api/product/spu/merchant/'+_this.id, {
            params: currentPage
          }).then(function (res) {
            _this.total = res.data.count;
            // 请求完成后，把得到的数据拼接到当前dom里面
            _this.productslist = res.data.list;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getProductslist('')
        },
        change(e){
            this.$forceUpdate()
        },
        defaults(){
          this.iSdefaults = 0;
          this.productslist = [];
          this.page = 1;
          this.order = '';
          this.priceOrder = '';
          this.getProductslist('');
        },
        priceSort(sort, index){
          this.iSdefaults = 4;
          this.productslist = [];
          this.page = 1;
          this.order = sort
          this.priceOrder = sort;
          this.getProductslist('');
        },
        salesSort(sort){
          this.iSdefaults = 1;
          this.productslist = [];
          this.page = 1;
          this.order = sort;
          this.priceOrder = '';
          this.getProductslist('');
        },
        scoreSort(sort){
          this.iSdefaults = 2;
          this.productslist = [];
          this.page = 1;
          this.order = sort;
          this.priceOrder = '';
          this.getProductslist('');
        },
        productNews(){
          this.iSdefaults = 3;
          this.productslist = [];
          this.page = 1;
          this.order = 'is_new';
          this.priceOrder = '';
          this.news = !this.news===true?1:0;
          this.getProductslist('');
        },
        clearPrice(){
          this.price_on = this.price_off = "";
          this.getProductslist('');
        },
      }
    }
</script>
<style lang="scss" scoped>
.trader{
  color: #fff;
  background-color: #e93323;
  display: inline-block;
  width: 32px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 2px;
  margin-right: 5px;
  font-size: 12px;
}
.noGoods{
  text-align: center;
  .pictrue{
    width: 274px;
    height: 174px;
    margin: 130px auto 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .name{
    font-size: 14px;
    color: #969696;
    margin-top: 20px;
    margin-bottom: 290px;
  }
}
.sort{
  background: #fff;
  height: 64px;
  align-items: center;
  padding: 0 15px;
  .sort-count{
    align-items: center;
  }
  .item{
    margin-right: 30px;
    cursor: pointer;
    &:hover{
      color: #E93323;
    }
    .icon{
      font-size: 15px;
      margin-left: 5px;
      color: #E2E2E2;
    }
    .iconfont{
      font-size: 15px;
      color: #E2E2E2;
      margin-left: 5px;
    }
  }
  .name{
    color: #969696;
    margin-right: 20px;
  }
}
.price-range{
  position: relative;
  padding: 10px;
  &:hover{
    box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.08);
    .action-box{
      display: block;
    }
  }
  .action-box{
    display: none;
    position: absolute;
    left: 0;
    bottom: -50px;
    text-align: center;
    background: #F9F9F9;
    height: 50px;
    line-height: 50px;
    width: 100%;
    box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.08);
    padding: 0 15px;
    overflow: hidden;
    .clear{
      float: left;
      color: #999999;
      cursor: pointer;
    }
    .submit{
      float: right;
      color: #666666;
      width: 60px;
      height: 30px;
      line-height: 30px;
      margin-top: 10px;
      background-color: #F1F1F1;
      border-radius: 2px;
      border: 1px solid #D4D4D4;
      cursor: pointer;
    }
  }
  .price-count{
    .input{
      width: 80px;
      height: 32px;
      position: relative;
      &:before{
        content: '¥';
        display: inline-block;
        color: #D4D4D4;
        position: absolute;
        left: 5px;
        top: 0;
        line-height: 32px;
      }
    }
  }
}
.price-range .price-count .input ::v-deep .el-input__inner{
  height: 32px;
  line-height: 32px;
  padding-left: 18px;
}
.store-count{
  .store-recommend{
    .item{
      background-color: #fff;
      padding: 16px;
      width: 224px;
      margin: 20px 20px 0 0;
      cursor: pointer;
      &:nth-child(4n){
        margin-right: 0;
      }
      &:hover{
        box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
      }
      .pictrue{
        width: 192px;
        height: 192px;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .svip{
        align-items: center;
        margin-top: 3px;
        .price{
          font-size: 14px;
          font-weight: bold;
          color: #282828;
          margin-right: 4px;
        }
        img{
          width: 35px;
          height: 15px;
        }
      }
      .money{
        margin-top: 12px;
        .font-color{
          font-weight: bold;
          font-size: 22px;
        }
        .y_money{
          font-size: 12px;
          color: #AAAAAA;
          text-decoration: line-through;
          margin-left: 8px;
        }
        .label{
          width: 20px;
          height: 20px;
          background: linear-gradient(330deg, rgba(231, 85, 67, 0.15) 0%, rgba(244, 103, 83, 0.15) 100%);
          font-size: 12px;
          text-align: center;
          line-height: 20px;
        }
      }
      .name{
        color: #5A5A5A;
        margin-top: 8px;
        height: 40px;
      }
      .bottom{
        font-size: 12px;
        color: #AAAAAA;
        margin-top: 10px;
      }
    }
  }
}
</style>
