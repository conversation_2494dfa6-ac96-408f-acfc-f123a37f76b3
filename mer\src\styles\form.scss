::v-deep .el-radio__input.is-checked .el-radio__inner {
  background-color: var(--prev-color-primary);
  border-color: var(--prev-color-primary);
}
::v-deep .el-radio__input.is-checked+.el-radio__label{
  color: var(--prev-color-primary)
}
::v-deep .el-switch.is-checked .el-switch__core {
  border-color: var(--prev-color-primary);
  background-color: var(--prev-color-primary);
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--prev-color-primary);
  border-color: var(--prev-color-primary);
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--prev-color-primary);
  border-color: var(--prev-color-primary);
} 
::v-deep .el-tabs__item.is-active,
::v-deep .el-tabs__item:hover,
::v-deep .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active,
::v-deep .el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: var(--prev-color-primary) !important;
}
::v-deep .el-tabs__active-bar {
  background-color: var(--prev-color-primary) !important;
}
::v-deep .el-tag {
  color: #303133;
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
::v-deep .el-tag .el-tag__close {
  color: #fff;
  background: #C0C4CC;
  &:hover {
    color: var(--prev-color-text-white);
    background-color: var(--prev-color-primary);
  }
}
::v-deep .el-select-dropdown__item.selected {
  color: var(--prev-color-primary);
}
::v-deep .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: var(--prev-color-primary);
}

::v-deep .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  background-color: var(--prev-color-hover);
  color: var(--prev-color-primary);
}