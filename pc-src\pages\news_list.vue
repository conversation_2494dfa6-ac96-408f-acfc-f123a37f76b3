<template>
  <div class="news">
      <category-List></category-List>
      <div class="wrapper_1200 acea-row">
        <div class="news_count">
          <div class="news_lf">
            <div class="cate_list" v-if="catelist">
              <div v-swiper:mySwiper="swiperOption">
                <div class="swiper-wrapper">
                  <div class="swiper-slide"
                       v-for="(item, index) in catelist"
                       :key="index" :class="index == idx ? 'on' : ''" @click="changeList(item,index)">
                    <span class="line1">{{item.title}}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="news-list">
              <div class="list acea-row row-middle" v-if="newslist.length">
                <div class="item" v-for="(item, index) in newslist" :key="index" @click="goDetail(item)">
                  <div class="item-count acea-row">
                    <div class="pictrue"><img :src="item.image_input"></div>
                    <div class="list-content">
                      <div class="name line1">{{item.title}}</div>
                      <div class="info line2">{{item.synopsis}}</div>
                      <div class="time acea-row">
                        <div><span class="iconfont icon-shijian1"></span>  {{item.create_time}}</div>
                        <!-- <div class="visit"><span class="iconfont icon-shijian"></span>  {{item.visit ? item.visit : 0}}</div>-->
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
            <div class="pages-box" v-if="total > 0">
              <el-pagination
                background
                layout="prev, pager, next"
                @current-change="bindPageCur"
                :pageSize="limit"
                :total="total">
              </el-pagination>
            </div>
            <div class="noCart" v-if="!newslist.length">
              <div class="pictrue"><img src="../assets/images/noGoods.png"></div>
              <div class="tip">亲，暂无内容哟~</div>
            </div>
          </div>
        </div>
        <div class="news_rt">
          <div class="title">最新资讯</div>
          <div v-for="(item, index) in latestList" class="latest-list acea-row">
            <span class="number">{{index+1}}</span>
            <nuxt-link :to="{path:`/news_detail/${item.article_id}`}" class="list line1">
              {{item.title}}
            </nuxt-link>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import hotGoods from '@/components/hotGoods';
import categoryList from "@/components/categoryList";
export default {
    name: "news_list",
    auth: false,
    components:{
      categoryList
    },
    data(){
      return{
        newslist:[],
        catelist: [],
        latestList: [], //最新资讯
        pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
        page: 1, //代表页面的初始页数
        limit:10,
        banner: '',
        scollY: null,// 离底部距离有多少
        total: 0, //总页数
        title:'下拉加载更多',
        idx: 0,
        id: "",
        swiperOption: {
          loop: false,
          speed: 1000,
          observer: true,
          observeParents: true,
          freeMode: true,
          freeModeMomentum: false,
          slidesPerView: "auto"
        },
      }
    },
    watch:{

    },
    async asyncData({app,error}){
      try{
        let [catelist, latestList] = await Promise.all([
          app.$axios.get("/api/article/category/lst"),
          app.$axios.get("/api/article/list")
        ]);
        return {
          catelist: catelist.data,
          latestList: latestList.data.list
        }
      }catch (e){
        error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
      }
    },
    fetch({ store}) {
      store.commit('isHeader', true);
      store.commit('isFooter', true);
    },
    head() {
      return {
        title: "资讯信息-"+this.$store.state.titleCon
      }
    },
    beforeMount(){

    },
    mounted(){
      this.getCateList();
      console.log(this.latestList)
    },
    beforeDestroy() {
      window.onscroll = null;
    },
    methods:{
      goDetail: function (item) {
        this.$router.push({ path: '/news_detail/'+item.article_id });
      },
      changeList(item, index){
        this.idx = index;
        this.page = 1;
        this.limit = 10;
        this.id = item.article_category_id;
        this.getNewslist(item.article_category_id);
      },
      getCateList(){
        let _this = this;
        _this.$axios.get('/api/article/category/lst').then(function (res) {
          _this.catelist = res.data;
          if(_this.catelist && _this.catelist.length){
            _this.getNewslist(_this.catelist[0]['article_category_id'])
            _this.id = _this.catelist[0]['article_category_id']
          }
        }).catch(function (err) {
          _this.$message.error(err);
        })
      },
      getNewslist(id){
        let _this = this,currentPage = {page: this.page,limit:this.limit};
        _this.$axios.get('/api/article/lst/'+ id, {
          params: currentPage
        }).then(function (res) {
          _this.total = res.data.count;
          _this.newslist = res.data.list;
        }).catch(function (err) {
          _this.$message.error(err);
        })
      },
      // 分页点击
      bindPageCur(data){
        this.page = data
        window.scrollTo({
          top: 0,
          left: 0,
        });
        this.getNewslist(this.id)
       }
    }
  }
</script>

<style scoped lang="scss">
  .news{
    .news_count{
      width: 860px;
      margin: 20px auto 0;
      background: #ffffff;
      border-radius: 8px;
      padding-bottom: 90px;
    }
    .swiper-container{
      height: 68px;
    }
    .swiper-slide{
      max-width: 100px;
      margin: 0 10px;
      text-align: center;
      cursor: pointer;
      span{
        display: inline-block;
        max-width: 100px;
      }
      &.on{
        color: #E93323;
        position: relative;
        &::after{
          content: "";
          display: inline-block;
          width: 100%;
          height: 3px;
          background: #E93323;
          position: absolute;
          left: 0;
          bottom: 0;
        }
      }
    }
    .cate_list{
      height: 68px;
      line-height: 68px;
      border-bottom: 1px solid #E1E1E1;
      overflow: hidden;
      .list_count{
        display: inline-block;
        width: 10000px;
      }
    }
    .list{
      width: 100%;
      .item{
        padding: 0 30px;
        width: 100%;
        cursor: pointer;
        &:hover{
          box-shadow: 0 2px 15px rgba(79, 109, 143, 0.15);
        }
        .item-count{
          padding: 40px 0;
          border-bottom: 1px solid #E2E2E2;
        }
        &:last-child{
          .item-count{
            border: none;
          }
        }
        .pictrue{
          width: 260px;
          height: 160px;
          img{
            width: 100%;
            height: 100%;
          }
        }
      .list-content{
        margin-left: 30px;
        color: #282828;
        width: 500px;
        position: relative;
        .name{
          font-size: 18px;
          font-weight: bold;
          line-height: 30px;
        }
        .info{
          margin-top: 15px;
          font-size: 16px;
        }
        .time{
          margin-top: 20px;
          font-size: 14px;
          color: #999999;
          position: absolute;
          bottom: 10px;
        }
        .visit{
          margin-left: 20px;
        }
      }
      }
    }
    .news_rt{
      width: 320px;
      background: #fff;
      padding: 0 35px 20px;
      border-radius: 8px;
      margin-top: 20px;
      .title{
        text-align: center;
        font-size: 18px;
        padding: 19px 0;
        border-bottom: 1px dashed #F1F1F1;
      }
      .latest-list{
        margin-top: 25px;
        color: #666666;
        font-size: 14px;
        .number{
          display: inline-block;
          width: 16px;
          height: 20px;
          margin-right: 10px;
          font-size: 11px;
          background: url("~assets/images/newsIcon.png") 100% center;
          text-align: center;
          line-height: 20px;
          transition: all linear .2s;
        }
        .list{
          display: inline-block;
          width: 223px;
        }
        &:hover{
          .number{
            background: url("~assets/images/newsIcon_red.png") 100% center;
            color: #ffffff;
          }
          .list{
            color: #E93323;
          }
        }
      }
    }
    .noCart{
      text-align: center;
      margin: 70px 0;
      .pictrue{
        width: 216px;
        height: 136px;
        margin: 0 auto;
        img{
          width: 100%;
          height: 100%;
        }
        .tip{
          margin-top: 12px;
          color: #646464;
        }
      }
    }
  }
</style>
