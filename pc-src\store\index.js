// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex);
export const state = () => ({
  banner: false,
  headers: true,
  footers: true,
  cartnumber: 0,
  logoUrl: '',
  titleCon: '',
  login: false,
  users: false,
  code: '',
  uuid: '',
  mer_banner: [],
  top_banner: '',
  sys_service: {},
  domain: '',
  hotline: '',
  openService: '0',
  openSettled: '0'
})

export const mutations = {
  isBanner (state, data) {
    state.banner = data;
  },
  isHeader (state, data) {
    state.headers = data;
  },
  isFooter (state, data) {
    state.footers = data;
  },
  cartNum (state, data){
    state.cartnumber = Number(data);
  },
  sysService (state, data){
    state.sys_service = data;
  },
  openService (state, data){
    state.openService = data;
  },
  openSettled (state, data){
    state.openSettled = data;
  },
  logo (state, data){
    state.logoUrl = data;
  },
  titles (state, data){
    state.titleCon = data;
  },
  isLogin (state, data){
    state.login = data;
  },
  isUser (state, data){
    state.users = data;
  },
  qrCode(state, data){
    state.code = data;
  },
  merBanner(state, data){
    state.mer_banner = data;
  },
  topBanner(state, data){
    state.top_banner = data;
  },
  domainName(state, data){
    if (data[data.length - 1] !== '/') {
      data += '/';
    }
    state.domain = data;
  },
  consumerHotline(state, data){
    state.hotline = data;
  },
  unreadKefu (state, data){
    state.unreadNum = data;
  },
  uuid (state, data){
    state.uuid = data;
  },
}

export const getters = {
  openService:(app) => app.openService,
  openSettled:(app) => app.openSettled
}

export const actions = {
  nuxtServerInit({commit}, {app}) {
    commit('logo', app.$cookies.get('logo'));
    commit('titles', app.$cookies.get('titles'));
    commit('unreadKefu', app.$cookies.get('unreadKefu'));

  }
}
