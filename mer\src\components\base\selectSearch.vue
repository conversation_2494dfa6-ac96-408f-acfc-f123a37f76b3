<template>
 <el-form-item :label="searchName">
    <el-input :placeholder="$t('请输入内容')" v-model="keywords" class="input-with-select selWidth" clearable @keyup.enter.native="changeSearch(1)">
        <el-select v-model="prependSelect" slot="prepend" :placeholder="$t('请选择')">
          <el-option v-for="item in searchSelectList" :key="item.value" :label="item.label" :value="item.value">
            {{item.label}}
          </el-option> 
        </el-select>
    </el-input>  
</el-form-item>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  name: 'SelectSearch',
  props: {
    // 搜索选项列表
    searchSelectList: {
      type: Array,
      default: [
        { label: leaveuKeyTerms['昵称'], value: "nickname" },
        { label: '用户ID', value: "uid" },
        { label: '手机号', value: "phone" },
      ],
    },
    // 默认选中的搜索选项
    select: {
      type: String,
      default: 'nickname',
    },
    // 搜索框的标签名称
    searchName: {
      type: String,
      default: '用户搜索：',
    },
  },
  data() {
    return {
      // 用户输入的搜索关键词
      keywords: '',
      // 当前选中的搜索选项
      prependSelect: this.select,
      // 搜索表单数据
      searchForm: {
        nickname: "",
        uid: "",
        phone: ""
      }
    };
  },

  methods: {
    // 处理搜索事件
    changeSearch(){
      this.searchForm = {nickname: "",uid: "",phone: "",real_name: "",returner: ""}
      this.searchForm[this.prependSelect] = this.keywords;
      this.$emit('search', this.searchForm);
    },
    resetParmas() {
      this.keywords = ""
      this.prependSelect = this.select
      this.changeSearch()
    }
  },
};
</script>

<style scoped>
::v-deep .el-input-group__prepend .el-input{
  min-width: 100px;
}
</style>
