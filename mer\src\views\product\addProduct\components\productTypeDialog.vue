<template>
  <div>
    <el-dialog
      :title="$t('添加商品类型')"
      :visible.sync="isShow"
      width="590px"
      :before-close="handleClose"
    >
      <div>
        <el-form label-width="auto">
          <el-form-item :label="$t('商品类型：')" required>
            <div
              v-for="(item, index) in virtual"
              :key="index"
              class="virtual"
              :class="{
                virtual_boder: addType == item.id,
                virtual_boder2: addType !== item.id,
                disabled: $route.params.id && addType !== item.id
              }"
              @click="virtualbtn(item.id, 2)"
            >
              <div class="virtual_top">{{ item.tit }}</div>
              <div class="virtual_bottom">({{ item.tit2 }})</div>
              <div v-if="addType == item.id" class="virtual_san" />
              <div v-if="addType == item.id" class="virtual_dui">✓</div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">{{ $t('取 消') }}</el-button>
        <el-button type="primary" @click="confirmSelection" size="small"
          >{{ $t('确 定') }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

import { roterPre } from "@/settings";
export default {
  data() {
    return {
      isShow: false,
      addType: 0,
      type: 1,
      virtual: [

        { tit: leaveuKeyTerms['虚拟商品'], id: 1, tit2: leaveuKeyTerms['虚拟发货'] },
        { tit: leaveuKeyTerms['云盘商品'], id: 2, tit2: leaveuKeyTerms['统一链接自动发货'] },
        { tit: leaveuKeyTerms['预约商品'], id: 4, tit2: leaveuKeyTerms['上门/到店服务'] }
      ]
    };
  },
  methods: {
    open(type) {
      if (type) {
        this.type = type;
      } else {
        this.type = 0;
      }
      this.isShow = true;
    },
    handleClose() {
      this.isShow = false;
    },
    virtualbtn(id, type) {
      this.addType = id;
    },
    confirmSelection() {
      if (this.type == 1) {
        this.$router.push({
          path: `${roterPre}/product/list/addProduct`,
          query: { productType: this.addType, type: this.type }
        });
      } else {
        this.$router.push({
          path: `${roterPre}/product/list/addProduct`,
          query: { productType: this.addType }
        });
      }

      this.isShow = false;
      this.addType = 0;
      this.type = 0;
    }
  }
};
</script>
<style scoped lang="scss">
.virtual_boder {
  border: 1px solid var(--prev-color-primary);
}

.virtual_boder2 {
  border: 1px solid #e7e7e7;
}

.virtual_san {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  border-bottom: 24px solid var(--prev-color-primary);
  border-left: 24px solid transparent;
}

.virtual_dui {
  position: absolute;
  bottom: 0px;
  right: 3px;
  color: #ffffff;
  font-family: system-ui;
  font-size: 12px;
}

.virtual {
  width: 140px;
  height: 70px;
  background: #ffffff;
  border-radius: 3px;
  display: inline-block;
  text-align: center;
  padding: 8px 4px;
  position: relative;
  cursor: pointer;
  margin-bottom: 20px;
  line-height: 20px;
  overflow: hidden;
  word-wrap: break-word;

  .virtual_top {
    font-size: 13px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    line-height: 16px;
    margin-bottom: 2px;
  }

  .virtual_bottom {
    font-size: 11px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }

  &.disabled {
    .virtual_top,
    .virtual_bottom {
      color: #ccc;
    }
  }
}

.virtual:nth-child(1n) {
  margin-right: 15px;
}

.virtual:nth-child(4n) {
  margin-right: 0;
}
</style>
