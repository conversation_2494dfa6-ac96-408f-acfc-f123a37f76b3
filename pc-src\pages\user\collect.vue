<template>
    <div class="collect">
      <div class="user-com-tab">
        <span class="item" :class="{on:tabCur == 0}" @click="tabCur= 0">商品收藏</span>
        <span class="item" :class="{on:tabCur == 1}" @click="tabCur= 1">店铺收藏</span>
      </div>
      <ul v-if="tabCur == 1" class="collectStore">
        <li v-for="(item,index) in storeList" :key="index">
          <div class="user-info">
                <div class="store-basis">
                    <div class="store-logo">
                        <img :src="item.merchant.mer_avatar" alt="">
                    </div>
                    <div class="store-name line1">
                        <span v-if="item.merchant.is_trader" class="trader">自营</span>
                        <span class="names line1">{{ item.merchant.mer_name }}</span>
                    </div>
                </div>
                <div class="store-info">
                    <div class="item">
                        <span class="desc">{{ item.merchant.care_count < 10000 ? item.merchant.care_count : (item.merchant.care_count/10000).toFixed(1)+'万'  || 0 }}人关注</span>
                    </div>
                </div>
                <div class="store-favorites">
                    <button class="store-btn toStore" @click="goStore(item.merchant.mer_id)">进店逛逛</button>
                    <button class="store-btn collection" @click="bindDetele(item,index)">取消关注</button>
                </div>
            </div>
        </li>
      </ul>
      <ul v-else class="collectProduct">
        <li v-for="(item,index) in list" :key="index" >
          <div v-if="item.spu" class="hd">
            <div class="img-box"><img :src="item.spu.image" alt=""></div>
            <div class="info line2">
              <!-- <span v-if="item.spu.product_type != 0" :class="'font_bg-red type'+item.spu.product_type">{{item.spu.product_type == 1 ? "秒杀" : item.spu.product_type == 2 ? "预售" : item.spu.product_type == 3 ? "助力" : item.spu.product_type == 4 ? "拼团" : ""}}</span> -->
              {{item.spu.store_name}}
            </div>
          </div>
          <div v-if="item.spu" class="bd">
            <el-button @click="offCollect(item.type_id,index)">取消收藏</el-button>
            <el-button @click="goPage(item.spu.product_id, item)" v-if="item.type < 2">查看商品</el-button>
          </div>
        </li>
      </ul>
      <div class="pages-box" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="bindPageCur"
          :pageSize="limit"
          :total="total">
        </el-pagination>
      </div>
      <div class="empty-box" v-if="total <= 0">
        <img src="~assets/images/nocollect.png" alt="">
        <p>亲，暂无收藏哟~</p>
      </div>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from 'element-ui';
export default {
      name: "collect",
      auth: "guest",
      data(){
        return{
          tabCur: 0,
          list:[],
          storeList: [],
          page:1,
          limit:12,
          total:0
        }
      },
       watch:{
        tabCur(nVal,oVal){
          this.page = 1;
          if(nVal == 1){
            this.getStoreList()
          }else{
            this.getGoodsList()
          }
        }
      },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "我的收藏-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
      },
      mounted() {
        this.getGoodsList()
      },
      methods:{
        //商品收藏
        getGoodsList(){
          this.$axios.get('/api/user/relation/product/lst',{
            params:{
              page:this.page,
              limit:this.limit
            }
          }).then(res=>{
            this.list = res.data.list
            this.total = res.data.count
          })
        },
        // 店铺收藏
        getStoreList(){
          this.$axios.get('/api/user/relation/merchant/lst',{
            params:{
              page:this.page,
              limit:this.limit
            }
          }).then(res=>{
            this.storeList = res.data.list
            this.total = res.data.count
          })
        },
        //取消收藏店铺
        bindDetele(item,index){
          this.$axios.post('/api/user/relation/delete',{
            type:10,
            type_id:item.type_id
          }).then(res=>{
            Message.success(res.message)
            this.storeList.splice(index,1)
            this.getStoreList()
          }).catch(error=>{
            return Message.error(error)
          })
        },
        //取消收藏
        offCollect(id, index){
          this.$axios.post('/api/user/relation/batch/delete',{
            type:1,
            type_id:[id]
          }).then(res=>{
            Message.success(res.message)
            this.list.splice(index, 1);
            this.getGoodsList()
          }).catch(error=>{
            return Message.error(error)
          })
        },
        //去详情
        goPage(id, item) {
          if (item.spu.product_type) {
            this.$router.push({
              path: `/goods_seckill_detail/${id}?time=${item.spu.stop_time}`
            })
          } else {
            this.$router.push({
              path: `/goods_detail/${id}`
            })
          }
        },
        //去店铺
        goStore(id){
            this.$router.push({
                path:`/store`,
                query:{
                    id:id
                }
          })
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          if(this.tabCur == 1){
            this.getStoreList()
          }else{
            this.getGoodsList()
          }
        }
      }
    }
</script>

<style lang="scss" scoped>
.collect{
  .collectProduct{
    li{
      padding: 30px 5px;
      border-bottom: 1px solid #E1E1E1;
      .hd{
        display: flex;
        padding-bottom: 30px;
        border-bottom: 1px dashed #E1E1E1;
        .img-box{
          width: 120px;
          height: 120px;
          img{
            width: 100%;
            height: 100%;
          }
        }
        .info{
          display: flex;
          align-items: center;
          margin-left: 25px;
          width: 60%;
        }
      }
      .bd{
        display: flex;
        justify-content: flex-end;
        padding-top: 25px;
        padding-right: 25px;
      }
    }
  }
}
// .font_bg-red {
// 		background-color: #E93323;
// 		border: 1px solid #E93323;
//     display: inline-block;
//     align-items: center;
//     color: #fff;
//     font-size: 10px;
//     text-align: center;
//     border-radius: 2px;
//     padding: 0 2px;
//     line-height: 14px;
//     margin-right: 2px;
// 		&.type2{
// 			background-color: #FD6523;
// 			border: 1px solid #FD6523;
// 		}
// 	}
.collectStore{
    overflow: hidden;
    margin-top: 30px;
    li{
        float: left;
        border: 1px solid #EFEFEF;;
        width: 210px;
        height: 240px;
        margin: 0 20px 20px 0;
        &:nth-child(4n){
            margin-right: 0;
        }
    }
}
.user-info{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fff;
      color: #282828;
      font-size: 14px;
      padding: 0 15px;
      .store-basis{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .trader{
            display: inline-block;
            width: 32px;
            height: 18px;
            line-height: 18px;
            text-align: center;
            color: #E93323;
            color: #fff;
            background: #E93323;
            border-radius: 2px;
            margin-right: 3px;
            font-size: 12px;
            font-family: 'PingFang SC';
        }
      }
      .store-logo{
          width: 61px;
          height: 61px;
          margin: 30px 0 15px;
          img{
            width: 61px;
            height: 61px;
            border-radius: 50%;
          }
      }
      .store-name{
          color: #333333;
          font-weight: bold;
          font-size: 16px;
        .names{
          max-width: 150px;
          display: inline-block;
          position: relative;
          top: 4px;
        }
      }
      .name{
        margin-top: 10px;
        padding: 0 15px;
      }
    }
    .store-info{
        padding: 15px 0 0;
        position: relative;
        border-bottom: 1px dashed #ECECEC;
        width: 100%;
        text-align: center;
        .item {
          font-size: 12px;
          color: #7e7e7e;
          margin-bottom: 15px;
          .iconfont{
              cursor: pointer;
          }
          .cont {
            margin-left: 8px;
          }
          .desc{
              color: #666666;
              font-size: 16px;
          }
        }
    }
    .store-favorites{
        margin: 9px 0;
        padding: 0 12px;
        overflow: hidden;
        width: 100%;
        position: relative;
           &:after{
               content: "";
               display: inline-block;
               width: 1px;
               height: 20px;
               background: #F3F3F3;
               position: absolute;
               top: 6px; right:50%;
           }
        .store-btn{
            height: 30px;
            line-height: 30px;
            text-align: center;
            color: #333333;
            border: none;
            background: #fff;
            font-size: 13px;
            color: #333333;
        }
        .toStore{
           float: left;

        }
        .collection{
            float: right;
        }
    }
.pages-box{
  margin-top: 30px;
  text-align: right;
}
.user-com-tab span{
  padding: 0 8px;
}
</style>
