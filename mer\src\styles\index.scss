@import "./font/iconfont.css";
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './styles.scss';
@import './variables.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './app.scss';
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}
html {
  height: 100%;
  box-sizing: border-box;
  line-height: normal;
}
#app {
  min-height: 100%;
}
*,
*:before,
*:after {
  box-sizing: inherit;
}
.no-padding {
  padding: 0px !important;
}
.padding-content {
  padding: 4px 0;
}
a:focus,
a:active {
  outline: none;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
div:focus {
  outline: none;
}
.fr {
  float: right;
}
.fl {
  float: left;
}
.pr-5 {
  padding-right: 5px;
}
.pl-5 {
  padding-left: 5px;
}
.mb-5 {
  margin-bottom: 5px;
}
.mt1-5{
  margin-top: 1.5px;
}
.block {
  display: block;
}
.pointer {
  cursor: pointer;
}
.selCard{
  padding: 20px 14px 2px;
  background: #ffffff;
  border-radius: 4px;
}
// .dataBox{
//   min-height: calc(100vh - 200px);
// }
.inlineBlock {
  display: block;
}
.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}
.app-container {
  padding: 20px;
}
.components-container {
  margin: 30px 50px;
  position: relative;
}
.pagination-container {
  margin-top: 30px;
}
.text-center {
  text-align: center
}
.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);
  .subtitle {
    font-size: 20px;
    color: #fff;
  }
  &.draft {
    background: #d0d0d0;
  }
  &.deleted {
    background: #d0d0d0;
  }
}
.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32, 160, 255);
  }
}
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}
.multiselect--active {
  z-index: 1000 !important;
}
.el-button-solt {
  background-color: var(--prev-color-primary)!important;
  border-radius: 0 4px 4px 0;
}
.el-button-solt i {
  color: #fff;
}
.upload-form-temp{
  width: 1100px;
}
// el-image 的 关闭预览样式
.el-image-viewer__close {
  border: 2px solid #fff;
  background: none;
}
.el-drawer__body {
  overflow: auto;
}
.el-date-editor .el-range-separator {
  width: 26px;
}
.selWidth {
  width: 280px;
}
.dialogWidth {
  width: 250px;
}
.pageWidth {
  width: 460px;
}
.width100 {
  width: 100%;
}
