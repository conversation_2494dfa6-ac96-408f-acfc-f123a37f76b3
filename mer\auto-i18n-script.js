/**
 * 留游记 LeaveuKey 项目自动化国际化替换脚本 - 第十一轮成功经验集成版
 * 
 * 项目目标：将CRMEB电商项目转换为留学知识付费平台，通过配置文件 + 国际化替换方式实现业务转换
 * 
 * 🔥 第十轮彻底升级：全面解决中文字符识别不彻底问题
 * 🏆 第十一轮成功集成：自动修复父级菜单条件渲染映射缺失
 * 
 * 核心突破：
 * - 🎯 扩展术语提取模式：从14种扩展到30+种匹配模式
 * - 🎯 全面覆盖文件类型：Vue文件 + JS配置文件（路由、菜单等）
 * - 🎯 彻底解决菜单字符替换：大菜单、小菜单全覆盖
 * - 🎯 详细替换日志：每个替换操作都有日志跟踪
 * - 🏆 智能修复条件渲染映射：自动检测并修复父级菜单显示问题
 * 
 * 核心原则：
 * 1. 🚨 绝对禁止修改任何原有中文标签内容
 * 2. ✅ 仅在原有中文文本外添加 $t() 国际化嵌套
 * 3. ✅ 一对一完整映射：每个UI文本都有完整的对应关系
 * 4. ✅ 避免复杂嵌套：统一使用 {{ $t('完整文本') }} 或 :label="$t('完整文本')"
 * 5. ✅ 全面覆盖：包括模板UI文本和JavaScript常量的完整国际化
 * 6. 🔥 智能上下文识别：精准区分this.$t()和leaveuKeyTerms直接访问的使用场景
 * 
 * 🎯 第十轮技术特性：
 * - 🔥 全面术语识别：30+种正则模式覆盖所有可能的中文字符场景
 * - 🔥 扩展文件支持：Vue文件 + JS配置文件（路由、菜单、导航等）
 * - 🔥 彻底解决菜单问题：专门处理router、menu、nav、sidebar等配置文件
 * - 🔥 智能上下文检测：模块顶层常量、props default函数、data()函数
 * - 🔥 精准替换策略：在正确上下文使用this.$t()，在特殊上下文使用leaveuKeyTerms['文本']
 * - 🔥 详细操作日志：每个替换操作都有详细的before/after日志
 * - 🔥 完美边界追踪：Vue组件边界、函数边界、常量声明边界的精确识别
 * 
 * 🏆 第十一轮新增特性：
 * - 🏆 条件渲染映射修复：自动检测三元运算符中的映射缺失并修复
 * - 🏆 父级菜单专项处理：专门修复长度判断条件渲染中的直接文本访问
 * - 🏆 复杂表达式支持：处理包含substr等方法调用的条件渲染
 * - 🏆 完整菜单系统覆盖：确保水平和垂直布局的父级菜单都正确映射
 * - 多重安全检查机制，避免重复替换和函数破坏
 * - 保守的替换策略，确保语法安全
 * - 保持完整的术语替换功能，不因错误而删减
 * 
 * 使用方式：node auto-i18n-script.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class AutoI18nReplacer {
    constructor() {
        this.srcDir = path.resolve('./src');
        this.configDir = path.join(this.srcDir, 'config');
        this.configFile = path.join(this.configDir, 'leaveuKeyTerms.js');
        this.mainFile = path.join(this.srcDir, 'main.js');
        this.reviewFile = './terms-review.json';
        
        this.extractedTerms = new Set();
        this.processedFiles = [];
        this.replacementStats = {
            templateReplacements: 0,
            scriptReplacements: 0,
            filesProcessed: 0,
            moduleTopLevelSkipped: 0, // 🔥 新增：模块顶层跳过统计
            vueComponentBoundaries: 0, // 🔥 新增：Vue组件边界检测统计
            dataFunctionProtected: 0 // 🔥 修复：data()函数保护统计
        };
    }

    // 启动自动化流程
    async start() {
        console.log('🚀 开始自动化国际化替换流程');
        console.log('📋 项目：留游记 LeaveuKey 术语转换');
        console.log('🎯 目标：Vue文件国际化嵌套（保持原文不变）');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        try {
            // 预检查：验证项目环境
            console.log('\n🔍 预检查：验证项目环境...');
            await this.preflightCheck();
            
            console.log('✅ 环境检查通过，开始处理...');
            // 第一步：扫描并提取术语
            console.log('\n📂 第一步：扫描Vue文件并提取中文术语...');
            await this.scanVueFiles();
            
            // 第二步：生成审核文件
            console.log('\n📝 第二步：生成术语审核文件...');
            await this.generateReviewFile();
            
            // 第三步：人工审核确认
            console.log('\n👁️  第三步：等待人工审核确认...');
            await this.waitForReview();
            
            // 第四步：生成配置文件
            console.log('\n⚙️  第四步：生成国际化配置文件...');
            await this.generateConfigFile();
            
            // 第五步：集成到main.js
            console.log('\n🔗 第五步：集成到main.js全局混入...');
            await this.integrateToMain();
            
            // 第六步：执行替换
            console.log('\n🔄 第六步：执行Vue文件替换...');
            await this.processVueFiles();
            
            // 🔥 新增：验证关键修复
            console.log('\n🔍 第七步：验证关键文件修复...');
            this.verifyKeyFileFixes();
            
            // 完成统计
            this.showStats();
            
        } catch (error) {
            console.error('❌ 脚本执行错误:', error.message);
            process.exit(1);
        }
    }

    // 预检查：验证项目环境
    async preflightCheck() {
        const checks = [];
        
        // 检查src目录是否存在
        if (!fs.existsSync(this.srcDir)) {
            checks.push('❌ src目录不存在');
        } else {
            checks.push('✅ src目录存在');
        }
        
        // 检查是否为Vue项目
        const packageJsonPath = './package.json';
        if (fs.existsSync(packageJsonPath)) {
            try {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
                if (packageJson.dependencies && packageJson.dependencies.vue) {
                    checks.push('✅ 检测到Vue项目');
                } else {
                    checks.push('⚠️  未检测到Vue依赖');
                }
            } catch (error) {
                checks.push('⚠️  package.json解析失败');
            }
        } else {
            checks.push('⚠️  package.json不存在');
        }
        
        // 检查Vue文件数量
        const vueFiles = this.findVueFiles(this.srcDir);
        if (vueFiles.length > 0) {
            checks.push(`✅ 找到${vueFiles.length}个Vue文件`);
        } else {
            checks.push('❌ 未找到任何Vue文件');
        }
        
        // 检查是否存在main.js
        if (fs.existsSync(this.mainFile)) {
            checks.push('✅ main.js文件存在');
        } else {
            checks.push('⚠️  main.js文件不存在');
        }
        
        // 输出检查结果
        checks.forEach(check => console.log(`   ${check}`));
        
        // 如果有严重错误，终止执行
        const hasErrors = checks.some(check => check.includes('❌'));
        if (hasErrors) {
            throw new Error('环境检查失败，请确认项目结构正确');
        }
    }

    // 🔥 第十轮升级：扫描所有相关文件提取术语 - 包括菜单配置文件
    async scanVueFiles() {
        // 🔥 扩展文件扫描范围：Vue文件 + 路由配置文件 + 菜单配置文件
        const vueFiles = this.findVueFiles(this.srcDir);
        const routerFiles = this.findRouterFiles(this.srcDir);
        const allFiles = [...vueFiles, ...routerFiles];
        
        console.log(`📁 找到 ${vueFiles.length} 个Vue文件`);
        console.log(`📁 找到 ${routerFiles.length} 个路由/菜单配置文件`);
        console.log(`📁 总计 ${allFiles.length} 个文件需要扫描`);
        
        for (const filePath of allFiles) {
            try {
                const content = fs.readFileSync(filePath, 'utf-8');
                const fileExt = path.extname(filePath);
                
                if (fileExt === '.vue') {
                    this.extractTermsFromContent(content);
                    console.log(`✅ 已扫描Vue文件: ${path.relative(this.srcDir, filePath)}`);
                } else if (fileExt === '.js') {
                    // 🔥 专门处理JS配置文件中的术语提取
                    this.extractTermsFromJsConfig(content);
                    console.log(`✅ 已扫描JS配置文件: ${path.relative(this.srcDir, filePath)}`);
                }
            } catch (error) {
                console.log(`⚠️  跳过文件: ${filePath} (${error.message})`);
            }
        }
        
        console.log(`📊 共提取到 ${this.extractedTerms.size} 个唯一中文术语`);
    }

    // 递归查找Vue文件
    findVueFiles(dir) {
        let vueFiles = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // 跳过node_modules等目录
                    if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
                        vueFiles = vueFiles.concat(this.findVueFiles(fullPath));
                    }
                } else if (item.endsWith('.vue')) {
                    vueFiles.push(fullPath);
                }
            }
        } catch (error) {
            console.log(`⚠️  无法读取目录: ${dir}`);
        }
        
        return vueFiles;
    }

    // 🔥 修复：递归查找路由和菜单配置文件 - 支持Windows路径
    findRouterFiles(dir) {
        let routerFiles = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // 跳过node_modules等目录
                    if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
                        routerFiles = routerFiles.concat(this.findRouterFiles(fullPath));
                    }
                } else if (item.endsWith('.js')) {
                    // 🔥 关键修复：更宽泛的文件识别策略
                    const fileName = item.toLowerCase();
                    const normalizedPath = fullPath.replace(/\\/g, '/'); // 🔥 Windows路径标准化
                    
                    // 🔥 扩展识别范围：所有router目录下的JS文件 + 特定命名的文件
                    if (fileName.includes('router') || 
                        fileName.includes('route') || 
                        fileName.includes('menu') || 
                        fileName.includes('nav') || 
                        fileName.includes('sidebar') ||
                        fileName.includes('config') ||
                        normalizedPath.includes('/router/') ||
                        normalizedPath.includes('/menu/') ||
                        normalizedPath.includes('/config/') ||
                        // 🔥 新增：router/modules目录下的所有JS文件
                        normalizedPath.includes('/router/modules/') ||
                        // 🔥 新增：常见的路由模块文件名
                        ['accounts.js', 'app.js', 'cms.js', 'community.js', 'delivery.js', 
                         'freight.js', 'group.js', 'maintain.js', 'marketing.js', 
                         'merchant.js', 'order.js', 'product.js', 'promoter.js', 
                         'routine.js', 'safe.js', 'service.js', 'setting.js', 
                         'sms.js', 'station.js', 'systemform.js', 'user.js', 
                         'userfeedback.js'].includes(fileName)) {
                        routerFiles.push(fullPath);
                        console.log(`🔍 发现路由配置文件: ${path.relative(this.srcDir, fullPath)}`);
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️  无法读取目录: ${dir}`);
        }
        
        return routerFiles;
    }

    // 🔥 新增：专门从JS配置文件中提取中文术语
    extractTermsFromJsConfig(content) {
        // 🔥 专注于JS配置文件中的特定模式
        const jsConfigPatterns = [
            // 🔥 路由配置中的meta.title
            /meta\s*:\s*\{[\s\S]*?title\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g,
            
            // 🔥 菜单配置中的常见字段
            /(?:title|name|label|text|menuName|displayName)\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 对象属性值中的中文（更宽泛的匹配）
            /:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 数组元素中的中文
            /\[\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?:\s*,|\s*\])/g,
            /,\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?:\s*[,\]])/g,
            
            // 🔥 常量定义中的中文
            /(?:const|let|var)\s+\w+\s*=\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n])/g,
            
            // 🔥 函数返回值中的中文
            /return\s+['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n}])/g,
            
            // 🔥 Vue组件注册时的中文
            /component\s*\(\s*['"][\w-]+['"],\s*\{[\s\S]*?['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g
        ];
        
        let totalMatches = 0;
        for (const pattern of jsConfigPatterns) {
            let matches;
            while ((matches = pattern.exec(content)) !== null) {
                const term = matches[1] ? matches[1].trim() : matches[0].trim();
                if (term && this.isValidChineseTerm(term) && !this.shouldSkipTermExtraction(term)) {
                    this.extractedTerms.add(term);
                    totalMatches++;
                }
            }
        }
        
        if (totalMatches > 0) {
            console.log(`    ✅ 本JS配置文件匹配到 ${totalMatches} 个中文术语`);
        }
    }

    // 🔥 第十轮终极升级：全面彻底的中文术语提取方法
    extractTermsFromContent(content) {
        // 🔥 大幅扩展模板中的中文文本提取 - 覆盖所有可能的HTML属性
        const templatePatterns = [
            // 🔥 扩展所有可能的HTML标签属性 - 增强标点符号识别
            /(?:label|placeholder|title|alt|value|content|text|name|id|class|data-[\w-]+)=["']([^"']*[\u4e00-\u9fa5][^"']*[：:]*[^"']*)["']/g,
            
            // 🔥 Vue指令属性（:label, v-model等）- 增强标点符号识别
            /(?::[\w-]+|v-[\w-]+)=["']([^"']*[\u4e00-\u9fa5][^"']*[：:]*[^"']*)["']/g,
            
            // 🔥 Element UI组件的所有可能属性 - 增强标点符号识别
            /(?:text|content|message|description|header|footer|confirm-button-text|cancel-button-text|type|size|effect|theme|placement|trigger|tooltip)=["']([^"']*[\u4e00-\u9fa5][^"']*[：:]*[^"']*)["']/g,
            
            // 🔥 标签内容中的中文（更精确的匹配）
            />([^<>{}]*[\u4e00-\u9fa5][^<>{}]*)</g,
            
            // 🔥 Vue指令内的中文字符串
            /v-[\w-]+=["'][^"']*'([^'"]*[\u4e00-\u9fa5][^'"]*)'[^"']*["']/g,
            /v-[\w-]+=["'][^"']*"([^'"]*[\u4e00-\u9fa5][^'"]*)"[^"']*["']/g,
            
            // 🔥 Vue模板插值中的字符串
            /\{\{\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}\}/g,
            
            // 🔥 Element UI表格列配置
            /prop=["'][\w-]*["']\s+label=["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
            
            // 🔥 表单验证信息
            /message\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 Element UI组件的slot内容
            /<template[^>]*slot[^>]*>([^<]*[\u4e00-\u9fa5][^<]*)</g,
            
            // 🔥 注释中的中文（用于提取业务含义）
            /<!--[^>]*?([\u4e00-\u9fa5][^>]*?)-->/g
        ];
        
        // 🔥 大幅扩展JavaScript中的中文文本提取 - 覆盖菜单、路由等所有场景
        const scriptPatterns = [
            // 🔥 对象属性值中的中文（所有情况）
            /:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 数组元素中的中文（所有情况）
            /\[\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?:\s*,|\s*\])/g,
            /,\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?:\s*[,\]])/g,
            
            // 🔥 变量赋值中的中文（所有情况）
            /=\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n},])/g,
            
            // 🔥 函数调用参数中的中文
            /\(\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s,\)]/g,
            
            // 🔥 路由配置中的中文（meta.title等）
            /meta\s*:\s*\{[\s\S]*?title\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g,
            /name\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 菜单配置中的中文
            /(?:menuName|title|label|text|name)\s*:\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}])/g,
            
            // 🔥 Vue组件data中的中文
            /data\s*\(\s*\)\s*\{[\s\S]*?['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g,
            
            // 🔥 Vue组件computed中的中文
            /computed\s*:\s*\{[\s\S]*?['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g,
            
            // 🔥 Vue组件methods中的中文
            /methods\s*:\s*\{[\s\S]*?['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\S]*?\}/g,
            
            // 🔥 对象字面量的属性名（中文key）
            /['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*:)/g,
            
            // 🔥 switch case中的中文
            /case\s+['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*:)/g,
            
            // 🔥 if条件判断中的中文字符串
            /(?:===|==|!==|!=)\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[\)\{])/g,
            
            // 🔥 模板字符串中的中文
            /`[^`]*?([^$]*[\u4e00-\u9fa5][^$]*?)(?:\$\{|`)/g,
            
            // 🔥 console.log等调试输出中的中文
            /console\.\w+\(\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s,\)]/g,
            
            // 🔥 alert、confirm等弹窗中的中文
            /(?:alert|confirm|prompt)\s*\(\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s,\)]/g,
            
            // 🔥 return语句中的中文
            /return\s+['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n}])/g,
            
            // 🔥 throw语句中的中文
            /throw\s+(?:new\s+)?Error\s*\(\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"][\s\)]/g
        ];
        
        // 🔥 合并所有模式进行全面扫描
        const allPatterns = [...templatePatterns, ...scriptPatterns];
        
        console.log(`🔍 使用 ${allPatterns.length} 种匹配模式进行全面中文术语扫描...`);
        
        let totalMatches = 0;
        for (const pattern of allPatterns) {
            let matches;
            while ((matches = pattern.exec(content)) !== null) {
                const term = matches[1] ? matches[1].trim() : matches[0].trim();
                if (term && this.isValidChineseTerm(term) && !this.shouldSkipTermExtraction(term)) {
                    this.extractedTerms.add(term);
                    totalMatches++;
                    
                    // 🔥 新增：自动添加标点符号变体到提取集合
                    this.addTermVariants(term);
                }
            }
        }
        
        if (totalMatches > 0) {
            console.log(`    ✅ 本文件匹配到 ${totalMatches} 个中文术语`);
        }
    }

    // 术语提取时的安全跳过检查
    shouldSkipTermExtraction(term) {
        // 跳过已经包含国际化标记的术语
        if (term.includes('$t(') || term.includes('{{') || term.includes('}}')) {
            return true;
        }
        
        // 跳过URL和路径
        if (/^(http[s]?:\/\/|\.\/|\/|\\)/.test(term)) {
            return true;
        }
        
        // 跳过文件扩展名
        if (/\.(js|css|html|vue|json|png|jpg|gif|svg)$/i.test(term)) {
            return true;
        }
        
        // 跳过纯数字或特殊字符组合
        if (/^[\d\s\-_.:：！？。，、；@#$%^&*()+=\[\]{}|\\<>?/]*$/.test(term)) {
            return true;
        }
        
        return false;
    }

    // 🔥 第十轮强化：严格验证有效的中文术语
    isValidChineseTerm(term) {
        // 必须包含中文字符
        if (!/[\u4e00-\u9fa5]/.test(term)) return false;
        
        // 过滤掉过短或过长的文本
        if (term.length < 1 || term.length > 50) return false;
        
        // 过滤掉纯符号或数字
        if (/^[\d\s\-_.:：！？。，、；]*$/.test(term)) return false;
        
        // 过滤掉HTML标签和HTML片段
        if (/<[^>]*>/.test(term) || /href=|class=|span|div|img/.test(term)) return false;
        
        // 🔥 新增：过滤掉包含代码片段的文本
        if (/[{}[\]();]/.test(term) || /\w+\s*:\s*$/.test(term)) return false;
        
        // 🔥 新增：过滤掉明显的代码注释片段
        if (/\/\/|\/\*|\*\//.test(term)) return false;
        
        // 🔥 新增：过滤掉包含连续标点符号的无意义文本
        if (/[^\u4e00-\u9fa5\w\s]{3,}/.test(term)) return false;
        
        // 🔥 新增：过滤掉以特殊字符开头或结尾的文本（但允许冒号结尾）
        if (/^[^\u4e00-\u9fa5\w]/.test(term)) return false;
        if (/[^\u4e00-\u9fa5\w：:]$/.test(term)) return false;
        
        // 🔥 新增：过滤掉明显的配置键值对片段
        if (/^\w+:$|^:\s*$/.test(term)) return false;
        
        return true;
    }

    // 🔥 新增：自动添加术语变体到提取集合
    addTermVariants(term) {
        const variants = new Set();
        
        // 如果术语不以冒号结尾，添加带冒号的变体
        if (!term.endsWith('：') && !term.endsWith(':')) {
            variants.add(term + '：');
            // variants.add(term + ':'); // 通常中文使用中文冒号
        }
        
        // 如果术语以冒号结尾，添加不带冒号的变体
        if (term.endsWith('：')) {
            variants.add(term.slice(0, -1));
        }
        if (term.endsWith(':')) {
            variants.add(term.slice(0, -1));
        }
        
        // 为表单标签类术语自动添加常见变体
        if (/^(商品|用户|订单|分类|标签|名称|状态|时间|数量|金额|价格|类型|等级)/.test(term)) {
            if (!term.includes('：') && !term.includes(':')) {
                variants.add(term + '：');
            }
        }
        
        // 添加所有有效的变体到提取集合
        variants.forEach(variant => {
            if (this.isValidChineseTerm(variant) && !this.shouldSkipTermExtraction(variant)) {
                this.extractedTerms.add(variant);
                console.log(`🔥 自动添加术语变体: "${term}" → "${variant}"`);
            }
        });
    }

    // 生成审核文件
    async generateReviewFile() {
        const reviewData = {
            metadata: {
                extractedAt: new Date().toISOString(),
                totalTerms: this.extractedTerms.size,
                projectName: "留游记 LeaveuKey",
                description: "CRMEB电商系统转换为留学知识付费平台的术语国际化"
            },
            instructions: {
                review: "请审核以下术语列表，确认每个术语是否需要进行国际化处理",
                rules: [
                    "保持原有中文内容完全不变",
                    "只添加$t()国际化嵌套",
                    "不进行任何术语转换",
                    "确认术语的业务含义和使用场景"
                ]
            },
            terms: Array.from(this.extractedTerms).sort().map(term => ({
                original: term,
                approved: true, // 默认全部通过
                category: this.categorizeTerms(term),
                context: "UI文本",
                notes: ""
            }))
        };
        
        fs.writeFileSync(this.reviewFile, JSON.stringify(reviewData, null, 2), 'utf-8');
        console.log(`📄 已生成审核文件: ${this.reviewFile}`);
        console.log(`📋 包含 ${reviewData.terms.length} 个待审核术语`);
    }

    // 术语分类
    categorizeTerms(term) {
        const categories = {
            '操作': /^(添加|删除|修改|编辑|保存|取消|确定|提交|搜索|查询|筛选|导出|导入|刷新|重置|返回|关闭)$/,
            '状态': /^(启用|禁用|正常|异常|成功|失败|待审核|已审核|已发布|草稿)$/,
            '表单': /^(请输入|请选择|必填|选填|格式|验证|提示).*$/,
            '时间': /^(创建时间|更新时间|开始时间|结束时间|日期|时间)$/,
            '分页': /^(共|条|页|首页|上一页|下一页|尾页|跳转)$/,
            '通用': /^(名称|标题|内容|描述|备注|排序|状态|操作)$/
        };
        
        for (const [category, pattern] of Object.entries(categories)) {
            if (pattern.test(term)) {
                return category;
            }
        }
        
        return '其他';
    }

    // 等待人工审核
    async waitForReview() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        return new Promise((resolve) => {
            console.log(`\n📋 请审核文件: ${this.reviewFile}`);
            console.log('📝 审核说明:');
            console.log('   1. 检查提取的术语是否准确');
            console.log('   2. 确认术语分类是否合理');
            console.log('   3. 修改approved字段控制是否替换(true/false)');
            console.log('   4. 在notes字段中添加备注信息');
            
            rl.question('\n✅ 审核完成后请按回车键继续...', () => {
                rl.close();
                resolve();
            });
        });
    }

    // 生成配置文件
    async generateConfigFile() {
        // 读取审核结果
        let reviewData;
        try {
            const reviewContent = fs.readFileSync(this.reviewFile, 'utf-8');
            reviewData = JSON.parse(reviewContent);
            console.log(`📄 成功读取审核文件: ${this.reviewFile}`);
        } catch (error) {
            console.log('⚠️  无法读取审核文件，使用默认配置');
            reviewData = { terms: Array.from(this.extractedTerms).map(term => ({ original: term, approved: true })) };
        }
        
        // 创建配置目录
        if (!fs.existsSync(this.configDir)) {
            fs.mkdirSync(this.configDir, { recursive: true });
            console.log(`📁 创建配置目录: ${this.configDir}`);
        }
        
        // 生成配置文件内容
        const approvedTerms = reviewData.terms.filter(term => term.approved);
        const configContent = this.generateConfigContent(approvedTerms);
        
        // 🔥 关键修复：确保配置文件包含必要的映射
        console.log(`\n📋 配置文件内容预览 (前10个映射):`);
        approvedTerms.slice(0, 10).forEach((term, index) => {
            console.log(`   ${index + 1}. '${term.original}' → '${term.original}'`);
        });
        if (approvedTerms.length > 10) {
            console.log(`   ... 还有 ${approvedTerms.length - 10} 个映射`);
        }
        
        fs.writeFileSync(this.configFile, configContent, 'utf-8');
        console.log(`⚙️  已生成配置文件: ${this.configFile}`);
        console.log(`📊 包含 ${approvedTerms.length} 个术语映射`);
        
        // 🔥 验证配置文件是否正确生成
        try {
            const generatedContent = fs.readFileSync(this.configFile, 'utf-8');
            if (generatedContent.includes('export default') && generatedContent.includes('商户')) {
                console.log(`✅ 配置文件验证通过，包含预期内容`);
            } else {
                console.log(`⚠️  配置文件可能不完整，请检查内容`);
            }
        } catch (error) {
            console.log(`❌ 配置文件验证失败: ${error.message}`);
        }
    }

    // 生成配置文件内容
    generateConfigContent(approvedTerms) {
        const termsMap = {};
        
        // 一对一映射，保持原文不变
        approvedTerms.forEach(termObj => {
            const term = termObj.original;
            termsMap[term] = term; // 关键：保持原文完全不变
        });
        
        // 🔥 新增：自动生成常见标点符号变体，填补边界情况
        const additionalVariants = new Set();
        approvedTerms.forEach(termObj => {
            const term = termObj.original;
            // 如果术语不带冒号，自动添加带冒号的变体
            if (!term.endsWith('：') && !term.endsWith(':')) {
                additionalVariants.add(term + '：');
                additionalVariants.add(term + ':');
            }
            // 如果术语带冒号，确保也有不带冒号的变体
            if (term.endsWith('：')) {
                additionalVariants.add(term.slice(0, -1));
            }
            if (term.endsWith(':')) {
                additionalVariants.add(term.slice(0, -1));
            }
            
            // 为一些常见的标签类术语添加变体
            if (/^(商品|用户|订单|分类|标签|名称|状态|时间|数量|金额|价格)/.test(term)) {
                // 添加常见的后缀变体
                const suffixes = ['：', ':', '名称', '信息', '详情', '列表', '管理'];
                suffixes.forEach(suffix => {
                    if (!term.includes(suffix)) {
                        const variant = term + suffix;
                        additionalVariants.add(variant);
                    }
                });
            }
        });
        
        // 将变体添加到映射中
        additionalVariants.forEach(variant => {
            if (!termsMap[variant]) {
                termsMap[variant] = variant;
                console.log(`🔥 自动添加术语变体: "${variant}"`);
            }
        });
        
        return `/**
 * 留游记 LeaveuKey 项目术语国际化配置
 * 自动生成时间: ${new Date().toLocaleString('zh-CN')}
 * 
 * 重要说明:
 * 1. 此文件通过自动化脚本生成，保持原有中文内容不变
 * 2. 采用一对一完整映射策略，为后续双语扩展做准备
 * 3. 不包含任何术语转换，严格保持原文
 */

export default {
  // 基础术语映射 (${Object.keys(termsMap).length} 项)
  ${Object.entries(termsMap)
    .map(([key, value]) => `  '${key}': '${value}'`)
    .join(',\n')}
};

/**
 * Vue.js 全局混入配置
 * 在 main.js 中使用以下代码集成:
 * 
 * import leaveuKeyTerms from './config/leaveuKeyTerms.js'
 * 
 * Vue.mixin({
 *   methods: {
 *     $t(key) {
 *       return leaveuKeyTerms[key] || key;
 *     }
 *   }
 * });
 */
`;
    }

    // 集成到main.js
    async integrateToMain() {
        if (!fs.existsSync(this.mainFile)) {
            console.log('⚠️  main.js文件不存在，跳过集成步骤');
            return;
        }
        
        let mainContent = fs.readFileSync(this.mainFile, 'utf-8');
        console.log(`📄 读取main.js文件: ${this.mainFile}`);
        
        // 🔥 更精确的集成检查
        const hasLeaveuKeyImport = mainContent.includes('leaveuKeyTerms');
        const hasTMethod = mainContent.includes('$t(key)') || mainContent.includes('$t: function');
        
        if (hasLeaveuKeyImport && hasTMethod) {
            console.log('✅ main.js已包含国际化配置，跳过集成');
            return;
        }
        
        console.log(`📋 main.js集成状态检查:`);
        console.log(`   导入leaveuKeyTerms: ${hasLeaveuKeyImport ? '已存在' : '需要添加'}`);
        console.log(`   $t方法注册: ${hasTMethod ? '已存在' : '需要添加'}`);
        
        // 🔥 创建备份
        const backupPath = this.mainFile + '.backup';
        fs.writeFileSync(backupPath, mainContent, 'utf-8');
        console.log(`💾 已创建main.js备份: ${backupPath}`);
        
        // 🔥 修复：更安全的导入语句插入
        if (!hasLeaveuKeyImport) {
            const importStatement = "import leaveuKeyTerms from './config/leaveuKeyTerms.js';";
            
            // 在Vue导入之后插入术语导入，避免破坏现有import结构
            if (mainContent.includes('import Vue from "vue"')) {
                mainContent = mainContent.replace(
                    'import Vue from "vue";',
                    `import Vue from "vue";\n${importStatement}`
                );
                console.log('✅ 在Vue导入后添加leaveuKeyTerms导入');
            } else if (mainContent.includes("import Vue from 'vue'")) {
                mainContent = mainContent.replace(
                    "import Vue from 'vue';",
                    `import Vue from 'vue';\n${importStatement}`
                );
                console.log('✅ 在Vue导入后添加leaveuKeyTerms导入');
            } else {
                // 如果找不到Vue导入，在文件开头添加
                mainContent = `${importStatement}\n${mainContent}`;
                console.log('✅ 在文件开头添加leaveuKeyTerms导入');
            }
        }
        
        // 🔥 智能混入配置插入 - 解决Vue.use()执行顺序问题
        if (!hasTMethod) {
            const mixinCode = `\n// 🔥 留游记 LeaveuKey 国际化配置 - 自动集成
Vue.mixin({
  methods: {
    $t(key) {
      return leaveuKeyTerms[key] || key;
    }
  }
});`;
            
            // 🔥 关键升级：智能检测Vue.use()调用并提前注册mixin
            if (mainContent.includes('Vue.use(')) {
                console.log('🔍 检测到Vue.use()调用，将在所有Vue.use()之前注册$t方法');
                
                // 找到第一个Vue.use()的位置
                const firstVueUseMatch = mainContent.match(/Vue\.use\(/);
                if (firstVueUseMatch) {
                    const insertIndex = firstVueUseMatch.index;
                    // 在第一个Vue.use()之前插入mixin配置
                    mainContent = mainContent.slice(0, insertIndex) + 
                                 mixinCode + '\n\n' + 
                                 mainContent.slice(insertIndex);
                    console.log('✅ 已将$t方法注册在Vue.use()之前，避免执行顺序问题');
                }
            } else if (mainContent.includes('Vue.prototype.')) {
                console.log('🔍 检测到Vue.prototype配置，将在其附近插入');
                // 如果存在Vue.prototype配置，在其后插入
                const lastPrototypeIndex = mainContent.lastIndexOf('Vue.prototype.');
                const lineEndIndex = mainContent.indexOf('\n', lastPrototypeIndex);
                
                if (lineEndIndex !== -1) {
                    mainContent = mainContent.slice(0, lineEndIndex + 1) + 
                                 mixinCode + 
                                 mainContent.slice(lineEndIndex + 1);
                    console.log('✅ 在Vue.prototype配置后插入$t方法');
                } else {
                    // 如果找不到换行，在文件末尾的new Vue之前插入
                    mainContent = this.insertBeforeNewVue(mainContent, mixinCode);
                }
            } else {
                console.log('🔍 未检测到特定Vue配置，将在new Vue之前插入');
                // 最后的fallback：在new Vue之前插入
                mainContent = this.insertBeforeNewVue(mainContent, mixinCode);
            }
        }
        
        fs.writeFileSync(this.mainFile, mainContent, 'utf-8');
        console.log('🔗 已智能集成到main.js');
        
        // 🔥 验证集成结果
        try {
            const updatedContent = fs.readFileSync(this.mainFile, 'utf-8');
            const hasImport = updatedContent.includes('leaveuKeyTerms');
            const hasMixin = updatedContent.includes('$t(key)');
            
            console.log(`📋 main.js集成验证:`);
            console.log(`   leaveuKeyTerms导入: ${hasImport ? '✅' : '❌'}`);
            console.log(`   $t方法mixin: ${hasMixin ? '✅' : '❌'}`);
            
            if (hasImport && hasMixin) {
                console.log('✅ main.js集成验证通过');
                // 删除备份文件
                fs.unlinkSync(backupPath);
            } else {
                console.log('⚠️  main.js集成可能不完整，保留备份文件');
            }
        } catch (error) {
            console.log(`❌ main.js集成验证失败: ${error.message}`);
        }
    }

    // 🔥 新增辅助方法：安全地在new Vue之前插入代码
    insertBeforeNewVue(content, codeToInsert) {
        // 查找所有可能的new Vue实例化位置
        const newVuePatterns = [
            /new Vue\s*\(/,
            /export\s+default\s+new\s+Vue\s*\(/
        ];
        
        for (const pattern of newVuePatterns) {
            const match = content.match(pattern);
            if (match) {
                const insertIndex = match.index;
                return content.slice(0, insertIndex) + 
                       codeToInsert + '\n\n' + 
                       content.slice(insertIndex);
            }
        }
        
        // 如果找不到new Vue，在文件末尾添加
        return content + codeToInsert;
    }

    // 🔥 第十轮升级：处理所有相关文件替换 - 包括JS配置文件
    async processVueFiles() {
        const vueFiles = this.findVueFiles(this.srcDir);
        const routerFiles = this.findRouterFiles(this.srcDir);
        const allFiles = [...vueFiles, ...routerFiles];
        
        console.log(`🔄 开始处理 ${vueFiles.length} 个Vue文件`);
        console.log(`🔄 开始处理 ${routerFiles.length} 个JS配置文件`);
        console.log(`🔄 总计处理 ${allFiles.length} 个文件`);
        
        // 🔥 关键修复：确保所有文件都被处理
        let actualProcessedCount = 0;
        let titleReplacementCount = 0;
        
        for (const filePath of allFiles) {
            try {
                const originalContent = fs.readFileSync(filePath, 'utf-8');
                const fileExt = path.extname(filePath);
                let modifiedContent;
                
                console.log(`\n📂 正在处理: ${path.relative(this.srcDir, filePath)}`);
                console.log(`   📄 文件类型: ${fileExt}, 原始大小: ${originalContent.length} 字符`);
                
                if (fileExt === '.vue') {
                    modifiedContent = this.replaceInVueFile(originalContent);
                } else if (fileExt === '.js') {
                    // 🔥 专门处理JS配置文件的替换
                    modifiedContent = this.replaceInJsConfigFile(originalContent);
                }
                
                // 🔥 新增：详细比较修改前后的内容
                if (originalContent !== modifiedContent) {
                    const originalLines = originalContent.split('\n');
                    const modifiedLines = modifiedContent.split('\n');
                    let changedLines = 0;
                    
                    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
                        if (originalLines[i] !== modifiedLines[i]) {
                            changedLines++;
                            if (changedLines <= 3) { // 只显示前3个变化
                                console.log(`   🔧 第${i+1}行变化:`);
                                console.log(`      原: ${originalLines[i] ? originalLines[i].trim() : '(空行)'}`);
                                console.log(`      新: ${modifiedLines[i] ? modifiedLines[i].trim() : '(空行)'}`);
                            }
                        }
                    }
                    
                    if (changedLines > 3) {
                        console.log(`      ... 还有 ${changedLines - 3} 行发生了变化`);
                    }
                }
                
                if (originalContent !== modifiedContent) {
                    // 🔥 安全保存：先备份再写入
                    const backupPath = filePath + '.backup';
                    fs.writeFileSync(backupPath, originalContent, 'utf-8');
                    
                    fs.writeFileSync(filePath, modifiedContent, 'utf-8');
                    this.processedFiles.push(path.relative(this.srcDir, filePath));
                    this.replacementStats.filesProcessed++;
                    actualProcessedCount++;
                    
                    // 统计title相关的替换
                    const titleMatches = (modifiedContent.match(/\$t\([^)]*\.title[^)]*\)/g) || []).length;
                    titleReplacementCount += titleMatches;
                    
                    console.log(`✅ 已处理${fileExt === '.vue' ? 'Vue文件' : 'JS配置文件'}: ${path.relative(this.srcDir, filePath)} (${titleMatches}个title替换)`);
                    
                    // 删除备份文件
                    fs.unlinkSync(backupPath);
                } else {
                    console.log(`⏭️  跳过(无需修改): ${path.relative(this.srcDir, filePath)}`);
                }
            } catch (error) {
                console.log(`❌ 处理失败: ${filePath} (${error.message})`);
                console.error('详细错误:', error);
            }
        }
        
        console.log(`\n📊 处理完成统计:`);
        console.log(`   📁 扫描文件总数: ${allFiles.length}`);
        console.log(`   📝 实际修改文件数: ${actualProcessedCount}`);
        console.log(`   🎯 Title相关替换总数: ${titleReplacementCount}`);
        
        // 🔥 关键验证：确保主要的菜单文件都被处理了
        const criticalFiles = [
            'layout/navMenu/vertical.vue',
            'layout/navMenu/horizontal.vue', 
            'layout/navMenu/subItem.vue',
            'layout/navBars/breadcrumb/breadcrumb.vue'
        ];
        
        console.log(`\n🔍 关键文件处理状态验证:`);
        for (const criticalFile of criticalFiles) {
            const isProcessed = this.processedFiles.some(file => file.includes(criticalFile.replace(/\//g, '\\')));
            console.log(`   ${isProcessed ? '✅' : '⚠️ '} ${criticalFile}: ${isProcessed ? '已处理' : '未修改'}`);
        }
    }

    // Vue文件内容替换
    replaceInVueFile(content) {
        // 🔥 智能检测：区分已处理和需要处理的情况
        const t_count = (content.match(/\$t\(/g) || []).length;
        const leaveuKeyTermsCount = (content.match(/leaveuKeyTerms\[/g) || []).length;
        const chineseCount = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
        const titleAccessCount = (content.match(/\.title\s*\}\}/g) || []).length;
        const unprocessedChineseStrings = (content.match(/['"][^'"]*[\u4e00-\u9fa5][^'"]*['"](?!\s*\))/g) || []).length;
        
        console.log(`   📊 文件分析: $t()=${t_count}, leaveuKeyTerms[]=${leaveuKeyTermsCount}, 中文字符=${chineseCount}, title访问=${titleAccessCount}, 未处理中文字符串=${unprocessedChineseStrings}`);
        
        // 🔥 关键修复：更智能的处理判断 - 重点检查未处理的中文字符串
        if (titleAccessCount > 0 || unprocessedChineseStrings > 0) {
            console.log(`   🔧 检测到需要处理的内容(title访问=${titleAccessCount}, 未处理字符串=${unprocessedChineseStrings})，继续处理...`);
        } else if (t_count > 10 && leaveuKeyTermsCount > 5 && unprocessedChineseStrings === 0) {
            console.log(`   ✅ 文件已充分处理(${t_count}个$t(), ${leaveuKeyTermsCount}个leaveuKeyTerms引用)，跳过`);
            return content;
        }
        
        let modifiedContent = content;
        
        // 🔥 修复：分离处理Vue文件的不同部分
        // 第一步：仅处理模板部分的替换（<template>标签内或Vue单文件的模板部分）
        console.log(`   🔧 处理模板部分...`);
        modifiedContent = this.replaceTemplateContent(modifiedContent);
        
        // 第二步：单独处理<script>标签内的JavaScript代码
        console.log(`   🔧 处理脚本部分...`);
        modifiedContent = this.replaceVueScriptBlocks(modifiedContent);
        
        // 🔥 验证处理结果
        const newTitleAccessCount = (modifiedContent.match(/\.title\s*\}\}/g) || []).length;
        const newTCount = (modifiedContent.match(/\$t\(/g) || []).length;
        
        if (newTitleAccessCount < titleAccessCount || newTCount > t_count) {
            console.log(`   ✅ 处理成功: title访问 ${titleAccessCount}→${newTitleAccessCount}, $t()调用 ${t_count}→${newTCount}`);
        } else if (titleAccessCount === 0 && newTCount === t_count) {
            console.log(`   ⏭️  无需修改: 文件已是最新状态`);
        } else {
            console.log(`   ⚠️  处理结果异常，请检查`);
        }
        
        return modifiedContent;
    }

    // 🔥 新增：专门处理JS配置文件的替换
    replaceInJsConfigFile(content) {
        console.log('🔧 开始处理JS配置文件中的中文替换...');
        
        // 🔥 第十一轮根本修复：首先修复重复嵌套错误，然后进行正常处理
        let result = content;
        let needsImport = false;
        
        // 🔥 关键修复：修复重复嵌套错误 leaveuKeyTerms[leaveuKeyTerms['文本']] → leaveuKeyTerms['文本']
        const duplicateNestingPattern = /leaveuKeyTerms\[leaveuKeyTerms\[(['"][^'"]*['"])\]\]/g;
        const duplicateMatches = result.match(duplicateNestingPattern);
        if (duplicateMatches) {
            console.log(`🔧 检测到${duplicateMatches.length}个重复嵌套错误，开始修复...`);
            result = result.replace(duplicateNestingPattern, (match, innerKey) => {
                console.log(`🔧 修复重复嵌套: ${match} → leaveuKeyTerms[${innerKey}]`);
                return `leaveuKeyTerms[${innerKey}]`;
            });
            needsImport = true; // 如果有leaveuKeyTerms使用，标记需要导入
        }
        
        // 🔥 检测是否已经被正确处理过（没有重复嵌套且有合理数量的引用）
        const correctLeaveuKeyTermsCount = (result.match(/leaveuKeyTerms\[[^[]*?\]/g) || []).length;
        const duplicateNestingCount = (result.match(/leaveuKeyTerms\[leaveuKeyTerms\[/g) || []).length;
        const chineseTextCount = (result.match(/['"][^'"]*[\u4e00-\u9fa5][^'"]*/g) || []).length;
        
        // 🔥 关键修复：只有当文件已被充分处理且没有遗留中文时才跳过
        if (correctLeaveuKeyTermsCount > 3 && duplicateNestingCount === 0 && chineseTextCount < 3) {
            console.log(`✅ 检测到该JS配置文件已被正确处理(${correctLeaveuKeyTermsCount}个正确引用，${chineseTextCount}个剩余中文)，跳过进一步处理`);
            // 确保有导入语句
            if (!result.includes('import leaveuKeyTerms')) {
                if (result.includes('import ')) {
                    result = result.replace(
                        /(import[^;]+;)/,
                        `$1\nimport leaveuKeyTerms from '@/config/leaveuKeyTerms.js';`
                    );
                } else {
                    result = `import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\n${result}`;
                }
            }
            return result;
        }
        
        // 🔥 如果检测到大量未处理的中文，继续进行替换
        if (chineseTextCount > 0) {
            console.log(`🔧 检测到${chineseTextCount}个待处理中文字符，继续进行替换...`);
        }
        
                 // 1. 🔥 强化对象属性值替换：使用leaveuKeyTerms['文本'] - 支持更多模式
         result = result.replace(
             /(\w+\s*:\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}\n\r])/g,
            (match, prefix, text) => {
                if (this.shouldSkipScriptReplacement(match, text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件对象属性替换: ${prefix}"${text}" → ${prefix}leaveuKeyTerms['${text}']`);
                return `${prefix}leaveuKeyTerms['${text}']`;
            }
        );
        
        // 🔥 新增：处理对象属性值后面直接换行的情况
        result = result.replace(
            /(\w+\s*:\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*$)/gm,
            (match, prefix, text) => {
                if (this.shouldSkipScriptReplacement(match, text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件行尾对象属性替换: ${prefix}"${text}" → ${prefix}leaveuKeyTerms['${text}']`);
                return `${prefix}leaveuKeyTerms['${text}']`;
            }
        );
        
        // 2. 数组元素替换
        result = result.replace(
            /(\[\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,\]])/g,
            (match, prefix, text) => {
                if (this.shouldSkipArrayReplacement(text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件数组替换: [${text}] → [leaveuKeyTerms['${text}']]`);
                return `${prefix}leaveuKeyTerms['${text}']`;
            }
        );
        
        // 3. 赋值语句替换
        result = result.replace(
            /(=\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n])/g,
            (match, prefix, text) => {
                if (this.shouldSkipAssignmentReplacement(text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件赋值替换: = "${text}" → = leaveuKeyTerms['${text}']`);
                return `${prefix}leaveuKeyTerms['${text}']`;
            }
        );
        
        // 🔥 新增：专门处理meta对象中的属性 - 路由文件常见模式
        result = result.replace(
            /(meta\s*:\s*\{[^}]*?)(\w+\s*:\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"]([^}]*?\})/g,
            (match, beforeProp, propName, text, afterProp) => {
                if (this.shouldSkipScriptReplacement(match, text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件meta对象替换: ${propName}"${text}" → ${propName}leaveuKeyTerms['${text}']`);
                return `${beforeProp}${propName}leaveuKeyTerms['${text}']${afterProp}`;
            }
        );
        
        // 🔥 新增：处理简单的字符串赋值（不在对象中的情况）
        result = result.replace(
            /^(\s*)(['"])([^'"]*[\u4e00-\u9fa5][^'"]*)(['"])(\s*[,;]?\s*)$/gm,
            (match, leadingSpace, quote1, text, quote2, trailingSpace) => {
                // 跳过已经在对象属性中的情况
                if (match.includes(':')) return match;
                if (this.shouldSkipScriptReplacement(match, text)) return match;
                needsImport = true;
                this.replacementStats.scriptReplacements++;
                console.log(`🔧 JS配置文件独立字符串替换: "${text}" → leaveuKeyTerms['${text}']`);
                return `${leadingSpace}leaveuKeyTerms['${text}']${trailingSpace}`;
            }
        );
        
        // 🔥 关键修复：确保导入语句被正确添加
        if (needsImport && !result.includes('import leaveuKeyTerms')) {
            console.log('🔧 JS配置文件需要添加leaveuKeyTerms导入语句');
            // 找到合适的位置插入import语句
            if (result.includes('import ')) {
                // 在最后一个import语句之后插入，避免破坏import顺序
                const importLines = result.split('\n');
                let lastImportIndex = -1;
                for (let i = 0; i < importLines.length; i++) {
                    if (importLines[i].trim().startsWith('import ')) {
                        lastImportIndex = i;
                    }
                }
                if (lastImportIndex !== -1) {
                    importLines.splice(lastImportIndex + 1, 0, "import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';");
                    result = importLines.join('\n');
                } else {
                    // fallback：在第一个import后插入
                    result = result.replace(
                        /(import[^;]+;)/,
                        `$1\nimport leaveuKeyTerms from '@/config/leaveuKeyTerms.js';`
                    );
                }
            } else {
                // 在文件开头插入
                result = `import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\n${result}`;
            }
            console.log('✅ leaveuKeyTerms导入语句已添加');
        }
        
        return result;
    }

    // 🔥 新增方法：专门处理Vue文件中的<script>标签块
    replaceVueScriptBlocks(content) {
        return content.replace(/<script[^>]*>([\s\S]*?)<\/script>/gi, (match, scriptContent) => {
            const modifiedScriptContent = this.replaceScriptContent(scriptContent);
            return match.replace(scriptContent, modifiedScriptContent);
        });
    }

    // 替换模板内容
    replaceTemplateContent(content) {
        let result = content;
        
        // 先移除HTML注释，避免在注释中进行替换
        const htmlComments = [];
        let commentIndex = 0;
        
        // 提取并标记HTML注释
        result = result.replace(/<!--[\s\S]*?-->/g, (match) => {
            const placeholder = `<!--COMMENT_PLACEHOLDER_${commentIndex}-->`;
            htmlComments[commentIndex] = match;
            commentIndex++;
            return placeholder;
        });
        
        // 🔥 关键修复：移除<script>标签内容，避免在JavaScript代码中进行模板替换
        const scriptBlocks = [];
        let scriptIndex = 0;
        
        // 提取并标记所有<script>标签块 - 使用更安全的占位符格式
        result = result.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, (match) => {
            const placeholder = `__SCRIPT_BLOCK_${scriptIndex}__`;  // 🔥 修复：使用更安全的占位符
            scriptBlocks[scriptIndex] = match;
            scriptIndex++;
            return placeholder;
        });
        
        // 🔥 关键修复：同样处理<style>标签，避免在CSS中进行替换
        const styleBlocks = [];
        let styleIndex = 0;
        
        result = result.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, (match) => {
            const placeholder = `__STYLE_BLOCK_${styleIndex}__`;  // 🔥 修复：使用更安全的占位符
            styleBlocks[styleIndex] = match;
            styleIndex++;
            return placeholder;
        });
        
        // 🔥 第十轮升级：全面彻底的Vue模板替换 - 覆盖所有可能的场景
        
        // 1. 🔥 扩展所有HTML属性替换：attribute="文本" → :attribute="$t('文本')"
        result = result.replace(
            /(\s+)((?:label|placeholder|title|alt|value|content|text|name|header|footer|confirm-button-text|cancel-button-text|tooltip|data-[\w-]+))=(?!:)["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
            (match, spacing, attrName, text) => {
                if (text.includes('{{') || text.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 属性替换: ${attrName}="${text}" → :${attrName}="$t('${text}')"`);
                return `${spacing}:${attrName}="$t('${text}')"`;
            }
        );
        
        // 2. 🔥 Element UI表格列标题替换：label="文本" → :label="$t('文本')"
        result = result.replace(
            /(<el-table-column[^>]*\s+)label=["']([^"']*[\u4e00-\u9fa5][^"']*)["']([^>]*>)/g,
            (match, prefix, text, suffix) => {
                if (text.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 表格列替换: label="${text}" → :label="$t('${text}')"`);
                return `${prefix}:label="$t('${text}')"${suffix}`;
            }
        );
        
        // 3. 🔥 按钮和标签文本替换：>文本< → >{{ $t('文本') }}<
        result = result.replace(
            />([^<>{}]*[\u4e00-\u9fa5][^<>{}]*)</g,
            (match, text) => {
                const trimmedText = text.trim();
                if (!trimmedText || trimmedText.includes('{{') || trimmedText.includes('$t(')) return match;
                if (/</.test(trimmedText) || />/.test(trimmedText)) return match;
                // 跳过注释占位符
                if (trimmedText.includes('COMMENT_PLACEHOLDER_')) return match;
                // 🔥 新增：跳过脚本和样式占位符
                if (trimmedText.includes('__SCRIPT_BLOCK_') || trimmedText.includes('__STYLE_BLOCK_')) return match;
                // 🔥 新增：跳过包含换行符的多行文本
                if (/[\r\n]/.test(trimmedText)) return match;
                // 🔥 新增：跳过过长的文本（防止复杂文本被替换）
                if (trimmedText.length > 50) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 标签文本替换: >${trimmedText}< → >{{ $t('${trimmedText}') }}<`);
                return `>{{ $t('${trimmedText}') }}<`;
            }
        );
        
        // 4. 🔥 Vue指令中的文本替换
        result = result.replace(
            /(v-[\w-]+="[^"]*)'([^']*[\u4e00-\u9fa5][^']*)'([^"]*")/g,
            (match, prefix, text, suffix) => {
                if (text.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 Vue指令替换: ${match} → ${prefix}$t('${text}')${suffix}`);
                return `${prefix}$t('${text}')${suffix}`;
            }
        );
        
        // 5. 🔥 新增：Element UI组件特殊属性替换
        result = result.replace(
            /(\s+)(type|size|effect|theme|placement|trigger)=["']([^"']*[\u4e00-\u9fa5][^"']*)["']/g,
            (match, spacing, attrName, text) => {
                if (text.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 Element UI组件替换: ${attrName}="${text}" → :${attrName}="$t('${text}')"`);
                return `${spacing}:${attrName}="$t('${text}')"`;
            }
        );
        
        // 6. 🔥 新增：Vue slot内容替换
        result = result.replace(
            /(<template[^>]*slot[^>]*>)([^<]*[\u4e00-\u9fa5][^<]*)(<\/template>)/g,
            (match, openTag, text, closeTag) => {
                const trimmedText = text.trim();
                if (!trimmedText || trimmedText.includes('{{') || trimmedText.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 Slot内容替换: ${text} → {{ $t('${trimmedText}') }}`);
                return `${openTag}{{ $t('${trimmedText}') }}${closeTag}`;
            }
        );

        // 🔥 关键修复：Vue插值表达式中的title属性访问 - 解决菜单显示核心问题
        // 7. 处理所有title属性访问：{{ val.title }}, {{ item.meta.title }} → {{ $t(val.title) }}, {{ $t(item.meta.title) }}
        result = result.replace(
            /\{\{\s*([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)\s*\}\}/g,
            (match, titleAccess) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 Vue插值title属性替换: ${match} → {{ $t(${titleAccess}) }}`);
                return `{{ $t(${titleAccess}) }}`;
            }
        );

        // 8. 处理单独的title变量：{{ title }} → {{ $t(title) }}
        result = result.replace(
            /\{\{\s*title\s*\}\}/g,
            (match) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 Vue插值单独title替换: ${match} → {{ $t(title) }}`);
                return `{{ $t(title) }}`;
            }
        );

        // 9. 处理v-text指令中的title：v-text="val.title" → v-text="$t(val.title)"
        result = result.replace(
            /(v-text\s*=\s*["'])([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)(["'])/g,
            (match, prefix, titleAccess, suffix) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 v-text指令title替换: ${match} → ${prefix}$t(${titleAccess})${suffix}`);
                return `${prefix}$t(${titleAccess})${suffix}`;
            }
        );

        // 10. 处理v-html指令中的title：v-html="val.title" → v-html="$t(val.title)"
        result = result.replace(
            /(v-html\s*=\s*["'])([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)(["'])/g,
            (match, prefix, titleAccess, suffix) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 v-html指令title替换: ${match} → ${prefix}$t(${titleAccess})${suffix}`);
                return `${prefix}$t(${titleAccess})${suffix}`;
            }
        );

        // 🔥 新增：处理复杂的title显示场景
        // 11. 处理三元运算符中的title：{{ condition ? item.title : default }}
        result = result.replace(
            /\{\{\s*([^}]*?\?[^}]*?)([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)([^}]*?:[^}]*?)\}\}/g,
            (match, beforeTitle, titleAccess, afterTitle) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 三元运算符title替换: ${match} → {{ ${beforeTitle}$t(${titleAccess})${afterTitle} }}`);
                return `{{ ${beforeTitle}$t(${titleAccess})${afterTitle} }}`;
            }
        );

        // 12. 处理过滤器中的title：{{ item.title | filter }}
        result = result.replace(
            /\{\{\s*([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)(\s*\|[^}]*?)\s*\}\}/g,
            (match, titleAccess, filterPart) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 过滤器title替换: ${match} → {{ $t(${titleAccess})${filterPart} }}`);
                return `{{ $t(${titleAccess})${filterPart} }}`;
            }
        );

        // 🔥 关键新增：处理动态路由信息显示 - 针对面包屑等场景
        // 13. 处理路由meta信息：{{ $route.meta.title }} → {{ $t($route.meta.title) }}
        result = result.replace(
            /\{\{\s*(\$route\.meta\.title)\s*\}\}/g,
            (match, routeAccess) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 路由meta.title替换: ${match} → {{ $t(${routeAccess}) }}`);
                return `{{ $t(${routeAccess}) }}`;
            }
        );

        // 14. 处理this.$route.meta.title访问
        result = result.replace(
            /\{\{\s*(this\.\$route\.meta\.title)\s*\}\}/g,
            (match, routeAccess) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 this.$route.meta.title替换: ${match} → {{ $t(${routeAccess}) }}`);
                return `{{ $t(${routeAccess}) }}`;
            }
        );

        // 🔥 新增：专门修复条件渲染中的映射缺失问题 - 解决父级菜单显示
        // 15. 修复三元运算符条件渲染中的直接文本访问：? ... : v.title → ? ... : $t(v.title)
        result = result.replace(
            /(\{\{\s*[^}]*\?\s*[^}]*:\s*)([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)\s*(\}\})/g,
            (match, beforeTitle, titleAccess, afterTitle) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 条件渲染title替换: ${match} → ${beforeTitle}$t(${titleAccess})${afterTitle}`);
                return `${beforeTitle}$t(${titleAccess})${afterTitle}`;
            }
        );

        // 16. 修复三元运算符中的简单变量访问：? ... : title → ? ... : $t(title)
        result = result.replace(
            /(\{\{\s*[^}]*\?\s*[^}]*:\s*)(title)\s*(\}\})/g,
            (match, beforeTitle, titleVar, afterTitle) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔧 条件渲染简单title替换: ${match} → ${beforeTitle}$t(${titleVar})${afterTitle}`);
                return `${beforeTitle}$t(${titleVar})${afterTitle}`;
            }
        );

        // 🔥 关键修复：专门针对父级菜单的条件渲染模式
        // 17. 修复长度判断条件渲染：{{ v.title && v.title.length >= 4 ? ... : v.title }}
        result = result.replace(
            /(\{\{\s*[a-zA-Z_$][a-zA-Z0-9_$.]*\.title[^?]*\?\s*[^:]*:\s*)([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)\s*(\}\})/g,
            (match, beforeTitle, titleAccess, afterTitle) => {
                if (match.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔥 父级菜单条件渲染修复: ${match} → ${beforeTitle}$t(${titleAccess})${afterTitle}`);
                return `${beforeTitle}$t(${titleAccess})${afterTitle}`;
            }
        );

        // 18. 修复更复杂的条件渲染模式（包含substr等方法调用）
        result = result.replace(
            /(\{\{\s*[^}]*\?\s*\$t\([^)]+\)\.substr\([^)]+\)\s*:\s*)([a-zA-Z_$][a-zA-Z0-9_$.]*\.title)\s*(\}\})/g,
            (match, beforeTitle, titleAccess, afterTitle) => {
                if (titleAccess.includes('$t(')) return match;
                this.replacementStats.templateReplacements++;
                console.log(`🔥 复杂条件渲染修复: ${match} → ${beforeTitle}$t(${titleAccess})${afterTitle}`);
                return `${beforeTitle}$t(${titleAccess})${afterTitle}`;
            }
        );
        
        // 恢复<style>标签块
        for (let i = 0; i < styleBlocks.length; i++) {
            result = result.replace(`__STYLE_BLOCK_${i}__`, styleBlocks[i]);  // 🔥 修复：使用正确的占位符格式
        }
        
        // 恢复<script>标签块
        for (let i = 0; i < scriptBlocks.length; i++) {
            result = result.replace(`__SCRIPT_BLOCK_${i}__`, scriptBlocks[i]);  // 🔥 修复：使用正确的占位符格式
        }
        
        // 恢复HTML注释
        for (let i = 0; i < htmlComments.length; i++) {
            result = result.replace(`<!--COMMENT_PLACEHOLDER_${i}-->`, htmlComments[i]);
        }
        
        return result;
    }

    // 🔥 第九轮终极升级：完美的精准替换策略，从源头避免运行时错误
    replaceScriptContent(content) {
        // 🔥 强力注释保护：按行处理，完全跳过所有注释行
        const lines = content.split('\n');
        const modifiedLines = [];
        let isInVueComponent = false; // 🔥 Vue组件边界检测
        let braceDepth = 0; // 🔥 大括号深度追踪
        let isInDataFunction = false; // 🔥 data()函数上下文检测
        let dataFunctionBraceDepth = 0; // 🔥 data()函数大括号深度
        let isInPropsDefaultFunction = false; // 🔥 NEW: props default函数上下文检测
        let isInModuleTopLevelConstant = false; // 🔥 NEW: 模块顶层常量声明检测
        let needsLeaveuKeyImport = false; // 🔥 检测是否需要导入leaveuKeyTerms
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // 🔥 最高优先级：检查是否为JavaScript注释行 - 完全跳过
            if (this.isJavaScriptComment(line)) {
                modifiedLines.push(line); // 原样保留，不做任何替换
                continue;
            }
            
            // 🔥 精准上下文检测 - 智能边界追踪系统
            const trimmedLine = line.trim();
            
            // 🔥 NEW: 检测模块顶层常量声明（const、let、var 开头的复杂声明）
            if (!isInVueComponent && this.isModuleTopLevelConstantDeclaration(trimmedLine)) {
                isInModuleTopLevelConstant = true;
                console.log(`🔧 检测到模块顶层常量声明，使用leaveuKeyTerms直接模式 - 行 ${i + 1}: ${trimmedLine.substring(0, 50)}...`);
            }
            
            // 检测Vue组件开始
            if (trimmedLine.includes('export default {') || 
                trimmedLine.match(/^export\s+default\s*{/)) {
                isInVueComponent = true;
                braceDepth = 1; // 开始括号深度追踪
                isInModuleTopLevelConstant = false; // 进入Vue组件后，退出模块顶层模式
                this.replacementStats.vueComponentBoundaries++; // 🔥 统计Vue组件边界检测
            }
            
            // 在Vue组件内部时，追踪大括号深度
            if (isInVueComponent) {
                            // 🔥 关键修复：检测data()函数开始 - 增强检测模式
            if (trimmedLine.match(/data\s*\(\s*\)\s*{/) || 
                trimmedLine.match(/data\s*:\s*function\s*\(\s*\)\s*{/) ||
                trimmedLine.match(/data\s*\(\s*\)\s*\{/) ||
                trimmedLine.match(/data\(\)\s*\{/) ||
                (trimmedLine.includes('data()') && trimmedLine.includes('{'))) {
                isInDataFunction = true;
                dataFunctionBraceDepth = 1; // 开始data()函数的大括号追踪
                console.log(`🔧 检测到data()函数开始，使用leaveuKeyTerms直接替换模式 - 行 ${i + 1}: ${trimmedLine.substring(0, 50)}...`);
            }
                
                // 🔥 NEW: 检测props的default函数开始
                if (this.isPropsDefaultFunction(trimmedLine, lines, i)) {
                    isInPropsDefaultFunction = true;
                    console.log(`🔧 检测到props default函数，使用leaveuKeyTerms直接替换模式 - 行 ${i + 1}`);
                }
                
                // 在data()函数内部时，追踪其大括号深度
                if (isInDataFunction) {
                    const openBraces = (line.match(/\{/g) || []).length;
                    const closeBraces = (line.match(/\}/g) || []).length;
                    dataFunctionBraceDepth += openBraces - closeBraces;
                    
                    // 如果data()函数结束，退出data()上下文
                    if (dataFunctionBraceDepth <= 0) {
                        isInDataFunction = false;
                        dataFunctionBraceDepth = 0;
                        console.log(`✅ data()函数结束，退出直接替换模式 - 行 ${i + 1}`);
                    }
                }
                
                // 计算本行的大括号变化（整个Vue组件）
                const openBraces = (line.match(/\{/g) || []).length;
                const closeBraces = (line.match(/\}/g) || []).length;
                braceDepth += openBraces - closeBraces;
                
                // 如果回到顶层深度，说明Vue组件结束
                if (braceDepth <= 0) {
                    isInVueComponent = false;
                    braceDepth = 0;
                    isInDataFunction = false; // 确保data()函数状态也重置
                    dataFunctionBraceDepth = 0;
                }
            }
            
            // 🔥 新增：模块顶层安全检查 - 不在Vue组件内则跳过替换
            if (!isInVueComponent && this.isModuleTopLevel(line)) {
                modifiedLines.push(line); // 原样保留模块顶层代码
                this.replacementStats.moduleTopLevelSkipped++; // 🔥 统计模块顶层跳过次数
                continue;
            }
            
            // 处理非注释行
            let modifiedLine = line;
            
            // 🔥 终极升级：智能上下文替换策略 - 在所有需要直接访问的上下文中使用leaveuKeyTerms
            if (isInDataFunction || isInPropsDefaultFunction || isInModuleTopLevelConstant) {
                const contextType = isInDataFunction ? 'data()函数' : 
                                  isInPropsDefaultFunction ? 'props default函数' : 
                                  '模块顶层常量';
                
                // 检测到常量声明结束（分号或换行），退出模块顶层常量模式
                if (isInModuleTopLevelConstant && this.isConstantDeclarationEnd(trimmedLine)) {
                    isInModuleTopLevelConstant = false;
                    console.log(`✅ 模块顶层常量声明结束，退出直接替换模式 - 行 ${i + 1}`);
                }
                
                // 检测props default函数结束
                if (isInPropsDefaultFunction && this.isPropsDefaultFunctionEnd(trimmedLine)) {
                    isInPropsDefaultFunction = false;
                    console.log(`✅ props default函数结束，退出直接替换模式 - 行 ${i + 1}`);
                }
                // 1. 特殊上下文中的对象属性值替换：使用leaveuKeyTerms['文本'] - 增强模式匹配
                modifiedLine = modifiedLine.replace(
                    /(\w+\s*:\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}\r\n])/g,
                    (match, prefix, text, ...args) => {
                        if (this.shouldSkipScriptReplacement(match, text, args)) return match;
                        needsLeaveuKeyImport = true; // 标记需要导入
                        this.replacementStats.dataFunctionProtected++; // 统计特殊上下文中的替换
                        console.log(`🔧 ${contextType}中替换: ${match} → ${prefix}leaveuKeyTerms['${text}']`);
                        return `${prefix}leaveuKeyTerms['${text}']`;
                    }
                );
                
                // 1.1 增强：处理行尾的对象属性（没有逗号的情况）
                modifiedLine = modifiedLine.replace(
                    /(\w+\s*:\s*)(?!leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](\s*)$/g,
                    (match, prefix, text, trailing, ...args) => {
                        if (this.shouldSkipScriptReplacement(match, text, args)) return match;
                        needsLeaveuKeyImport = true;
                        this.replacementStats.dataFunctionProtected++;
                        console.log(`🔧 ${contextType}中行尾替换: ${match} → ${prefix}leaveuKeyTerms['${text}']${trailing}`);
                        return `${prefix}leaveuKeyTerms['${text}']${trailing}`;
                    }
                );
                
                // 2. 特殊上下文中的数组元素替换
                modifiedLine = modifiedLine.replace(
                    /(\[\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,\]])/g,
                    (match, prefix, text) => {
                        if (this.shouldSkipArrayReplacement(text)) return match;
                        needsLeaveuKeyImport = true;
                        this.replacementStats.dataFunctionProtected++;
                        console.log(`🔧 ${contextType}中数组替换: ${match} → ${prefix}leaveuKeyTerms['${text}']`);
                        return `${prefix}leaveuKeyTerms['${text}']`;
                    }
                );
                
                // 3. 特殊上下文中的简单赋值语句替换
                modifiedLine = modifiedLine.replace(
                    /(=\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n])/g,
                    (match, prefix, text) => {
                        if (this.shouldSkipAssignmentReplacement(text)) return match;
                        needsLeaveuKeyImport = true;
                        this.replacementStats.dataFunctionProtected++;
                        console.log(`🔧 ${contextType}中赋值替换: ${match} → ${prefix}leaveuKeyTerms['${text}']`);
                        return `${prefix}leaveuKeyTerms['${text}']`;
                    }
                );
                
                modifiedLines.push(modifiedLine);
                continue;
            }
            
            // 🔥 第十轮升级：全面彻底的JavaScript替换 - 覆盖菜单、路由等所有场景
            
            // 1. 🔥 对象属性值替换（最安全的上下文） - 包括菜单配置 - 增强模式匹配
            modifiedLine = modifiedLine.replace(
                /(\w+\s*:\s*)(?!this\.\$t|leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,}\r\n])/g,
                (match, prefix, text, ...args) => {
                    // 多重安全检查：避免破坏现有功能
                    if (this.shouldSkipScriptReplacement(match, text, args)) {
                        return match;
                    }
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS对象属性替换: ${prefix}"${text}" → ${prefix}this.$t('${text}')`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 1.1 增强：处理行尾的对象属性（没有逗号的情况）
            modifiedLine = modifiedLine.replace(
                /(\w+\s*:\s*)(?!this\.\$t|leaveuKeyTerms\[)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](\s*)$/g,
                (match, prefix, text, trailing, ...args) => {
                    if (this.shouldSkipScriptReplacement(match, text, args)) {
                        return match;
                    }
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS对象属性行尾替换: ${prefix}"${text}" → ${prefix}this.$t('${text}')${trailing}`);
                    return `${prefix}this.$t('${text}')${trailing}`;
                }
            );
            
            // 2. 🔥 扩展数组元素替换（包括菜单数组）
            modifiedLine = modifiedLine.replace(
                /(\[\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,\]])/g,
                (match, prefix, text) => {
                    if (this.shouldSkipArrayReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS数组元素替换: [${text}] → [this.$t('${text}')]`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 3. 🔥 数组中间元素替换
            modifiedLine = modifiedLine.replace(
                /(,\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[,\]])/g,
                (match, prefix, text) => {
                    if (this.shouldSkipArrayReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS数组中间元素替换: ,${text} → ,this.$t('${text}')`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 4. 🔥 简单赋值语句替换
            modifiedLine = modifiedLine.replace(
                /(=\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n])/g,
                (match, prefix, text) => {
                    if (this.shouldSkipAssignmentReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS赋值替换: = "${text}" → = this.$t('${text}')`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 5. 🔥 新增：函数调用参数中的中文替换
            modifiedLine = modifiedLine.replace(
                /(\(\s*)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[\),])/g,
                (match, prefix, text) => {
                    if (this.shouldSkipFunctionParameterReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS函数参数替换: (${text}) → (this.$t('${text}'))`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 6. 🔥 新增：return语句中的中文替换
            modifiedLine = modifiedLine.replace(
                /(return\s+)['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[;\n}])/g,
                (match, prefix, text) => {
                    if (this.shouldSkipReturnReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS return替换: return "${text}" → return this.$t('${text}')`);
                    return `${prefix}this.$t('${text}')`;
                }
            );
            
            // 7. 🔥 新增：条件判断中的中文字符串替换
            modifiedLine = modifiedLine.replace(
                /(===|==|!==|!=)\s*['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"](?=\s*[\)\{])/g,
                (match, operator, text) => {
                    if (this.shouldSkipConditionReplacement(text)) return match;
                    this.replacementStats.scriptReplacements++;
                    console.log(`🔧 JS条件判断替换: ${operator} "${text}" → ${operator} this.$t('${text}')`);
                    return `${operator} this.$t('${text}')`;
                }
            );
            
            modifiedLines.push(modifiedLine);
        }
        
        // 🔥 关键：如果检测到需要导入leaveuKeyTerms，添加到文件顶部
        let result = modifiedLines.join('\n');
        if (needsLeaveuKeyImport && !result.includes('import leaveuKeyTerms')) {
            console.log('🔧 检测到特殊上下文使用leaveuKeyTerms，添加导入语句');
            result = "import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\n" + result;
        }
        
        return result;
    }

    // 🔥 重新添加：检测JavaScript注释行的方法
    isJavaScriptComment(line) {
        const trimmedLine = line.trim();
        
        // 单行注释 //
        if (trimmedLine.startsWith('//')) {
            return true;
        }
        
        // 多行注释开始 /*
        if (trimmedLine.startsWith('/*')) {
            return true;
        }
        
        // 多行注释结束 */
        if (trimmedLine.includes('*/')) {
            return true;
        }
        
        // JSDoc注释 /** 或者注释中间的 *
        if (trimmedLine.startsWith('/**') || trimmedLine.startsWith('*')) {
            return true;
        }
        
        return false;
    }

    // 🔥 新增：检测模块顶层代码模式
    isModuleTopLevel(line) {
        const trimmedLine = line.trim();
        
        // 检测模块顶层声明模式
        const moduleTopLevelPatterns = [
            // 常量/变量声明
            /^const\s+\w+\s*=/,
            /^let\s+\w+\s*=/,
            /^var\s+\w+\s*=/,
            
            // 函数声明
            /^function\s+\w+\s*\(/,
            
            // ES6导入导出
            /^import\s+/,
            /^export\s+/,
            
            // 类声明
            /^class\s+\w+/,
            
            // 直接调用（如IIFE）
            /^\(/,
            
            // 其他顶层结构
            /^;/  // 空语句
        ];
        
        // 检查是否匹配任何模块顶层模式
        for (const pattern of moduleTopLevelPatterns) {
            if (pattern.test(trimmedLine)) {
                return true;
            }
        }
        
        return false;
    }

    // 🔥 NEW: 精准检测模块顶层常量声明（包含复杂对象和数组）
    isModuleTopLevelConstantDeclaration(line) {
        const trimmedLine = line.trim();
        
        // 检测const/let/var开头的复杂声明（包含对象或数组）
        const constantDeclarationPatterns = [
            /^const\s+\w+\s*=\s*\[/,        // const arr = [
            /^const\s+\w+\s*=\s*\{/,        // const obj = {
            /^let\s+\w+\s*=\s*\[/,          // let arr = [
            /^let\s+\w+\s*=\s*\{/,          // let obj = {
            /^var\s+\w+\s*=\s*\[/,          // var arr = [
            /^var\s+\w+\s*=\s*\{/,          // var obj = {
        ];
        
        return constantDeclarationPatterns.some(pattern => pattern.test(trimmedLine));
    }

    // 🔥 NEW: 检测常量声明结束
    isConstantDeclarationEnd(line) {
        const trimmedLine = line.trim();
        
        // 检测常量声明结束的标志
        return trimmedLine.endsWith('];') ||    // 数组结束
               trimmedLine.endsWith('};') ||    // 对象结束
               trimmedLine === '];' ||          // 单独的数组结束行
               trimmedLine === '};';            // 单独的对象结束行
    }

    // 🔥 NEW: 检测props的default函数
    isPropsDefaultFunction(line, lines, currentIndex) {
        const trimmedLine = line.trim();
        
        // 检测 default: function() { 或 default() { 模式
        if (trimmedLine.includes('default:') && 
            (trimmedLine.includes('function') || trimmedLine.includes('()'))) {
            return true;
        }
        
        // 检测多行的props default情况
        // 向上查找几行，看是否在props定义内
        for (let i = Math.max(0, currentIndex - 5); i < currentIndex; i++) {
            const prevLine = lines[i] ? lines[i].trim() : '';
            if (prevLine.includes('props:') || prevLine.includes('props')) {
                if (trimmedLine.includes('default:')) {
                    return true;
                }
            }
        }
        
        return false;
    }

    // 🔥 NEW: 检测props default函数结束
    isPropsDefaultFunctionEnd(line) {
        const trimmedLine = line.trim();
        
        // 检测函数结束的标志
        return trimmedLine === '},' ||          // 函数结束
               trimmedLine === '}' ||           // 单独的结束括号
               trimmedLine.endsWith('},');      // 带逗号的函数结束
    }

    // 🔥 第十一轮根本修复：精确重复嵌套检测机制
    shouldSkipScriptReplacement(match, text, args) {
        // 🔥 最高优先级：强力重复嵌套检测 - 防止任何形式的嵌套调用
        if (match.includes('this.$t(') || match.includes('$t(') || text.includes('$t(')) {
            console.log(`🛡️ 检测到$t()嵌套，跳过替换: ${match}`);
            return true;
        }
        
        // 🔥 精确检测：避免leaveuKeyTerms重复嵌套，但不阻止正常替换
        if (match.includes('leaveuKeyTerms[') || text.includes('leaveuKeyTerms[')) {
            console.log(`🛡️ 检测到leaveuKeyTerms[]嵌套，跳过替换: ${match}`);
            return true;
        }
        
        // 🔥 关键修复：只检测已经被包装的内容，不阻止对原始中文的替换
        // 移除过于宽泛的 leaveuKeyTerms 检测，只检测具体的嵌套情况
        
        // 检查2：避免破坏危险函数调用 - 修复args[0]可能为undefined的问题
        const dangerousFunctions = ['join(', 'indexOf(', 'split(', 'replace(', 'slice(', 'substring('];
        if (args && args.length > 0 && typeof args[0] === 'string' && dangerousFunctions.some(func => args[0].includes(func))) {
            console.log(`🛡️ 检测到危险函数调用，跳过替换: ${match}`);
            return true;
        }
        
        // 检查3：文本长度限制
        if (text.length > 30) {
            console.log(`🛡️ 文本过长(${text.length}字符)，跳过替换: ${text}`);
            return true;
        }
        
        // 检查4：避免URL、路径等特殊字符串
        if (/^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) || /\.(js|css|html|vue|json|png|jpg|gif|svg)$/.test(text)) {
            console.log(`🛡️ 检测到URL/路径，跳过替换: ${text}`);
            return true;
        }
        
        // 检查5：避免正则表达式字符串
        if (/[\[\](){}.*+?^$|\\]/.test(text)) {
            console.log(`🛡️ 检测到正则表达式字符，跳过替换: ${text}`);
            return true;
        }
        
        // 🔥 新增检查6：跳过包含换行符的多行文本
        if (/[\r\n]/.test(text)) {
            console.log(`🛡️ 检测到多行文本，跳过替换: ${text}`);
            return true;
        }
        
        return false;
    }

    // 🔥 第十一轮根本修复：数组替换精确安全检查
    shouldSkipArrayReplacement(text) {
        // 🔥 精确重复嵌套检测
        if (text.includes('$t(') || text.includes('this.$t(')) {
            console.log(`🛡️ 数组检测到$t()嵌套，跳过: ${text}`);
            return true;
        }
        
        // 🔥 关键修复：只检测已经被包装的leaveuKeyTerms，不阻止对原始中文的替换
        if (text.includes('leaveuKeyTerms[')) {
            console.log(`🛡️ 数组检测到leaveuKeyTerms[]引用，跳过: ${text}`);
            return true;
        }
        
        if (text.length > 20) {
            console.log(`🛡️ 数组文本过长，跳过: ${text}`);
            return true;
        }
        
        if (/^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) ||
            /[\[\](){}.*+?^$|\\]/.test(text) ||
            /[\r\n]/.test(text)) {
            console.log(`🛡️ 数组检测到特殊字符，跳过: ${text}`);
            return true;
        }
        
        return false;
    }

    // 🔥 第十一轮根本修复：赋值替换精确安全检查
    shouldSkipAssignmentReplacement(text) {
        // 🔥 精确重复嵌套检测
        if (text.includes('$t(') || text.includes('this.$t(')) {
            console.log(`🛡️ 赋值检测到$t()嵌套，跳过: ${text}`);
            return true;
        }
        
        // 🔥 关键修复：只检测已经被包装的leaveuKeyTerms，不阻止对原始中文的替换
        if (text.includes('leaveuKeyTerms[')) {
            console.log(`🛡️ 赋值检测到leaveuKeyTerms[]引用，跳过: ${text}`);
            return true;
        }
        
        if (text.length > 25) {
            console.log(`🛡️ 赋值文本过长，跳过: ${text}`);
            return true;
        }
        
        if (/^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) ||
            /[\[\](){}.*+?^$|\\]/.test(text) ||
            /[\r\n]/.test(text)) {
            console.log(`🛡️ 赋值检测到特殊字符，跳过: ${text}`);
            return true;
        }
        
        return false;
    }

    // 🔥 第十一轮根本修复：函数参数替换精确安全检查
    shouldSkipFunctionParameterReplacement(text) {
        return text.includes('$t(') || 
               text.includes('leaveuKeyTerms[') || // 🔥 精确检测：只检测已包装的内容
               text.length > 30 || 
               /^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) ||
               /[\[\](){}.*+?^$|\\]/.test(text) ||
               /[\r\n]/.test(text) ||
               // 跳过JavaScript关键字和特殊值
               /^(true|false|null|undefined|NaN|Infinity)$/i.test(text);
    }

    // 🔥 第十一轮根本修复：return语句替换精确安全检查
    shouldSkipReturnReplacement(text) {
        return text.includes('$t(') || 
               text.includes('leaveuKeyTerms[') || // 🔥 精确检测：只检测已包装的内容
               text.length > 30 || 
               /^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) ||
               /[\[\](){}.*+?^$|\\]/.test(text) ||
               /[\r\n]/.test(text) ||
               // 跳过可能是状态码或ID的纯数字文本
               /^\d+$/.test(text);
    }

    // 🔥 第十一轮根本修复：条件判断替换精确安全检查
    shouldSkipConditionReplacement(text) {
        return text.includes('$t(') || 
               text.includes('leaveuKeyTerms[') || // 🔥 精确检测：只检测已包装的内容
               text.length > 20 || 
               /^(http[s]?:\/\/|\.\/|\/|\\)/.test(text) ||
               /[\[\](){}.*+?^$|\\]/.test(text) ||
               /[\r\n]/.test(text) ||
               // 跳过可能是枚举值或状态的简短文本
               /^[A-Z_]+$/.test(text) ||
               // 跳过可能是配置键的文本
               /^[\w\.-]+$/.test(text) && text.length < 5;
    }

    // 🔥 新增：验证关键文件修复状态
    verifyKeyFileFixes() {
        console.log('\n🔍 验证关键文件修复状态...');
        
        const criticalFiles = [
            {
                path: 'src/layout/component/columnsAside.vue',
                description: '垂直分栏主菜单',
                expectedPattern: /\$t\(v\.title\)/,
                avoidPattern: /:\s*v\.title\s*\}\}/
            },
            {
                path: 'src/layout/component/transverseAside.vue',
                description: '水平分栏主菜单',
                expectedPattern: /\$t\(v\.title\)/,
                avoidPattern: /:\s*v\.title\s*\}\}/
            }
        ];
        
        let allFixed = true;
        
        for (const file of criticalFiles) {
            const fullPath = path.join(this.srcDir, '..', file.path);
            
            try {
                if (fs.existsSync(fullPath)) {
                    const content = fs.readFileSync(fullPath, 'utf-8');
                    const hasExpectedPattern = file.expectedPattern.test(content);
                    const hasAvoidPattern = file.avoidPattern.test(content);
                    
                    if (hasExpectedPattern && !hasAvoidPattern) {
                        console.log(`   ✅ ${file.description}: 条件渲染映射修复成功`);
                    } else if (!hasExpectedPattern && hasAvoidPattern) {
                        console.log(`   ❌ ${file.description}: 仍存在直接文本访问，需要修复`);
                        allFixed = false;
                    } else if (!hasExpectedPattern && !hasAvoidPattern) {
                        console.log(`   ⚠️  ${file.description}: 无法确定修复状态`);
                    } else {
                        console.log(`   🔄 ${file.description}: 部分修复，建议检查`);
                    }
                } else {
                    console.log(`   ⚠️  ${file.description}: 文件不存在`);
                }
            } catch (error) {
                console.log(`   ❌ ${file.description}: 读取失败 - ${error.message}`);
                allFixed = false;
            }
        }
        
        if (allFixed) {
            console.log('\n🎉 关键文件修复验证通过！父级菜单映射应该正常工作。');
        } else {
            console.log('\n⚠️  部分关键文件可能需要手动检查和修复。');
        }
        
        return allFixed;
    }

    // 显示统计信息
    showStats() {
        console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log('🎉 第十轮彻底升级版本 - 自动化国际化替换完成！');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        // 统计文件类型分布
        const vueFiles = this.processedFiles.filter(file => file.endsWith('.vue')).length;
        const jsFiles = this.processedFiles.filter(file => file.endsWith('.js')).length;
        
        console.log(`📊 处理统计:`);
        console.log(`   📁 总处理文件数: ${this.replacementStats.filesProcessed}`);
        console.log(`   📄 Vue文件: ${vueFiles} 个`);
        console.log(`   📄 JS配置文件: ${jsFiles} 个`);
        console.log(`   🏷️  模板替换数: ${this.replacementStats.templateReplacements}`);
        console.log(`   📝 脚本替换数: ${this.replacementStats.scriptReplacements}`);
        console.log(`   🛡️  模块顶层跳过: ${this.replacementStats.moduleTopLevelSkipped}`);
        console.log(`   🔒 data()函数保护: ${this.replacementStats.dataFunctionProtected}`);
        console.log(`   🔍 Vue组件边界检测: ${this.replacementStats.vueComponentBoundaries}`);
        console.log(`   📋 术语总数: ${this.extractedTerms.size}`);
        console.log(`   ⚙️  配置文件: ${this.configFile}`);
        console.log(`   🔗 主入口文件: ${this.mainFile}`);
        
        if (this.processedFiles.length > 0) {
            console.log(`\n📂 已处理的文件:`);
            this.processedFiles.forEach(file => {
                console.log(`   ✅ ${file}`);
            });
        }
        
        console.log('\n🚀 下一步操作建议:');
        console.log('   1. 运行 npm run dev 验证项目是否正常启动');
        console.log('   2. 检查页面显示是否正常');
        console.log('   3. 验证所有中文文本是否通过$t()方法渲染');
        console.log('   4. 如有问题，使用git回退并调整脚本');
        console.log('\n✨ 留游记 LeaveuKey 国际化改造完成！');
        
        console.log('\n🎯 预期效果验证:');
        console.log('   ✅ 子菜单正确显示：如"专家列表"、"专家分类"等');
        console.log('   ✅ 父级菜单正确显示：如"专家"（而不是"商户"）');
        console.log('   ✅ 水平和垂直布局都正确映射');
        console.log('   ✅ 通过修改leaveuKeyTerms.js即可改变前端显示');
        console.log('   ✅ 完整的菜单系统国际化实现');
        
        console.log('\n🧪 验证方法:');
        console.log('   1. 修改 src/config/leaveuKeyTerms.js 中的映射');
        console.log('   2. 例如: \'商户\': \'专家\', \'商户列表\': \'专家列表\'');
        console.log('   3. 刷新浏览器，观察菜单是否立即更新');
        console.log('   4. 检查columnsAside.vue和transverseAside.vue文件');
        console.log('   5. 确认条件渲染中使用了$t()而不是直接文本');
        
        // 🔥 第十轮升级：全面中文识别和菜单处理提示
        console.log('\n🔧 第十轮彻底升级亮点:');
        console.log('   🎯 扩展了30+种正则模式，彻底解决中文字符识别不全问题');
        console.log('   🎯 新增JS配置文件处理，解决菜单、路由等配置中的中文替换');
        console.log('   🎯 每个替换操作都有详细日志，便于问题追踪和验证');
        console.log('   🎯 全面覆盖Vue模板、JS脚本、路由配置、菜单配置等所有场景');
        console.log('\n🔥 第十一轮成功经验集成:');
        console.log('   🏆 自动修复父级菜单条件渲染映射缺失问题');
        console.log('   🏆 智能检测并修复三元运算符中的直接文本访问');
        console.log('   🏆 专门处理长度判断条件渲染中的映射丢失');
        console.log('   🏆 确保水平和垂直布局菜单系统完整映射');
        console.log('   🏆 实现从干净工作空间到完整菜单国际化的一键自动化');
        
        if (this.replacementStats.dataFunctionProtected > 0) {
            console.log('\n🔧 智能上下文处理说明:');
            console.log(`   在特殊上下文中完成 ${this.replacementStats.dataFunctionProtected} 次中文替换`);
            console.log('   包括：data()函数、props default函数、模块顶层常量声明');
            console.log('   使用 leaveuKeyTerms[\'文本\'] 直接访问，从源头避免this.$t()运行时错误');
        }
    }
}

// 主执行入口
if (require.main === module) {
    const replacer = new AutoI18nReplacer();
    replacer.start().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = AutoI18nReplacer; 