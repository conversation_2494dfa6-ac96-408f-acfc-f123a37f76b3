<template>
    <div class="mycoupon-wrapper">
      <div class="main-section">
        <div class="main-bd">
          <div class="list acea-row">
            <div v-for="item in list" :key="item.id" class="item acea-row" :class="item.issue ? 'disabled' : ''">
              <div class="text">
                <div class="label">{{item.type===0?'店铺券':'商品券'}}</div>
                <div class="text-cont acea-row row-middle">
                  <div class="money">
                    ￥<span class="num">{{ parseFloat(item.coupon_price) }}</span>
                  </div>
                  <div class="info">
                    <div class="name line1">{{ item.title }}</div>
                    <div>满{{ item.use_min_price}}可用</div>
                  </div>
                </div>
                <div v-if="item.coupon_type == 1" class="time">
                  {{ item.use_start_time | dateFormat }}~{{ item.use_end_time | dateFormat }}
                </div>
                <div v-if="item.coupon_type == 0" class="time">
                  领取后{{ item.coupon_time}}天内可用
                </div>
              </div>
              <div class="btn acea-row row-middle" v-if="item.issue">已领取</div>
              <div class="btn acea-row row-middle" v-else @click="receiveCoupon(item)">立即领取</div>
            </div>
          </div>
          <div class="empty-box" v-if="list.length == 0">
            <img src="~/assets/images/noCoupou.png" alt="">
            <p>亲，暂无可使用优惠券哟~</p>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "myCoupon",
      auth: "guest",
      filters: {
        dateFormat(value) {
          if (!value) {
            return;
          }
          return value.split(' ')[0];
        }
      },
      data(){
        return {
          tabCur: 0,
          list:[],
          couponList:[],
          expireList:[]
        }
      },
      async asyncData({ query }) {
        return {
            id: query.id,
        };
      },
      watch:{
        tabCur(nVal,oVal){
          if(nVal == 1){
            this.list = this.expireList
          }else{
            this.list = this.couponList
          }
        }
      },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "店铺优惠券-"+this.$store.state.titleCon
        }
      },
      beforeMount() {
        this.getCouponList()
      },
      methods:{
        getCouponList() {
          this.$axios
            .get("/api/coupon/store/"+this.id, {params: {all: 1}})
            .then(res => {
              this.list = res.data
            })
            .catch(err => {
              this.$message.error(err);
            });
        },
        receiveCoupon(item) {
            this.$axios.post("/api/coupon/receive/"+item.coupon_id)
                .then(res => {
                    item.issue = 1;
                    this.$message.success(res.message);
                })
                .catch(err => {
                    this.$message.error(err);
                });
        }
      }
    }
</script>
<style lang="scss" scoped>
.main-section{
    background-color: #fff;
    padding: 0 13px;
    min-height: 686px;
  .list {
    .item {
      width: 300px;
      height: 130px;
      margin: 15px 15px 15px 0;
      background: url("~assets/images/coupon-back1.png") center/cover no-repeat;
      box-shadow: 0 3px 20px rgba(0, 0, 0, 0.08);
      &:nth-child(3n){
          margin-right: 0;
      }
      &.disabled {
        background-image: url("~assets/images/coupon-back2.png");
        .label {
          background-color: rgba(145, 145, 145, 0.1);
          color: #acacac;
        }
        .text-cont {
          color: #d0d0d0;
        }
        .money {
          color: #bfbfbf;
        }
        .name {
          color: #bfbfbf;
        }
        .time {
          color: #d0d0d0;
        }
      }
      .text {
        flex: 1;
        padding-left: 14px;
      }
      .text-cont {
        margin-top: 14px;
        font-size: 14px;
        color: #969696;
      }
      .label {
        width: 62px;
        height: 24px;
        background-color: rgba(233, 51, 35, 0.1);
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        color: #e93323;
      }
      .money {
        margin-right: 14px;
        font-size: 16px;
        color: #e93323;
        .num {
          font-weight: bold;
          font-size: 32px;
        }
      }
      .info {
        flex: 1;
        min-width: 0;
      }
      .name {
        margin-bottom: 6px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 16px;
        color: #282828;
        width: 107px;
      }
      .time {
        margin-top: 14px;
        font-size: 12px;
        color: #969696;
      }
      .btn {
        width: 52px;
        padding-right: 18px;
        padding-left: 18px;
        font-size: 16px;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
}
</style>
