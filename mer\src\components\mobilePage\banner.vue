<template>
  <div class="mobile-page" :style="{ marginTop: `${mTOP}px`,background: (bgColor.length > 0 && isShow) ? `linear-gradient(180deg,${bgColor[0].item},${bgColor[1].item})` : 'transparent' }">
    <!-- <div
      class="bg"
      :style="{
        background: `linear-gradient(180deg,${bgColor[0].item},${bgColor[1].item})`,
      }"
      v-if="bgColor.length > 0 && isShow"
    ></div> -->
    <div
      class="banner"
      :class="bgColor.length > 0 && isShow?'on':''"
      :style="{
        marginLeft: edge + 'px',
        marginRight: edge + 'px',
      }"
    >
      <img :class="{ doc: imgStyle }" :src="imgSrc" alt="" v-if="imgSrc" />
      <div class="empty-box" :class="{ on: imgStyle }" v-else>
        <span class="iconfont-diy icontupian"></span>
      </div>
    </div>
    
    <div>
      <div class="dot" :style="{paddingLeft: edge+10 + 'px',paddingRight: edge+10 + 'px',justifyContent: (dotPosition===1?'center':dotPosition===2?'flex-end':'flex-start')}" v-if="docStyle == 0">
        <div class="dot-item" style="background: #fff;"></div>
        <div class="dot-item"></div>
        <div class="dot-item"></div>
      </div>
      <div class="dot line-dot" :style="{paddingLeft: edge+10 + 'px',paddingRight: edge+10 + 'px',justifyContent: (dotPosition===1?'center':dotPosition===2?'flex-end':'flex-start')}" v-if="docStyle == 1">
        <div class="line_dot-item" style="background: #fff;"></div>
        <div class="line_dot-item"></div>
        <div class="line_dot-item"></div>
      </div>
     
    </div>
  </div>
</template>

<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { mapState } from "vuex";
export default {
  name: "banner", // 组件名称
  cname: this.$t(this.$t('轮播图')), // 标题名称
  icon: "iconlunbotu",
  defaultName: "swiperBg", // 外面匹配名称
  configName: "c_banner", // 右侧配置名称
  type: 0, // 0 基础组件 1 营销组件 2工具组件
  props: {
    index: {
      type: null,
    },
    num: {
      type: null,
    },
  },
  computed: {
    ...mapState("mobildConfig", ["defaultArray"]),
  },
  watch: {
    pageData: {
      handler(nVal, oVal) {
        this.setConfig(nVal);
      },
      deep: true,
    },
    num: {
      handler(nVal, oVal) {
        let data = this.$store.state.mobildConfig.defaultArray[nVal];
        this.setConfig(data);
      },
      deep: true,
    },
    defaultArray: {
      handler(nVal, oVal) {
        let data = this.$store.state.mobildConfig.defaultArray[this.num];
        this.setConfig(data);
      },
      deep: true,
    },
  },
  data() {
    return {
      // 默认初始化数据禁止修改
      defaultConfig: {
        name: "swiperBg",
        timestamp: this.num,
        setUp: {
          tabVal: '0'
        },

        // 图片列表
        swiperConfig: {
          title:
            "最多可添加10张图片，建议宽度750*345px；鼠标拖拽左侧圆点可调整图片 顺序",
          maxList: 10,
          list: [
            {
              img: "",
              info: [
                {
                  title: leaveuKeyTerms['标题'],
                  value: leaveuKeyTerms['今日推荐'],
                  tips: leaveuKeyTerms['选填，不超过4个字'],
                  max: 4,
                },
                {
                  title: leaveuKeyTerms['链接'],
                  value: "",
                  tips: leaveuKeyTerms['请输入链接'],
                  max: 100,
                },
              ],
            },
          ],
        },
        isShow: {
          title: leaveuKeyTerms['是否显示背景色'],
          val: true
        },
        // 背景颜色
        bgColor: {
          title: "背景颜色(渐变)",
          default: [
            {
              item: "#FFFFFF",
            },
            {
              item: "#FFFFFF",
            },
          ],
          color: [
            {
              item: "#FFFFFF",
            },
            {
              item: "#FFFFFF",
            },
          ],
        },
        // 左右间距
        lrConfig: {
          title: leaveuKeyTerms['左右边距'],
          val: 10,
          min: 0,
        },
        // 页面间距
        mbConfig: {
          title: leaveuKeyTerms['页面间距'],
          val: 0,
          min: 0,
        },
        // 轮播图点样式
        docConfig: {
          cname: "swiper",
          title: leaveuKeyTerms['指示器样式'],
          type: 0,
          list: [
            {
              val: leaveuKeyTerms['圆形'],
              icon: "iconDot",
            },
            {
              val: leaveuKeyTerms['直线'],
              icon: "iconSquarepoint",
            },
            {
              val: leaveuKeyTerms['无指示器'],
              icon: "iconjinyong",
            },
          ],
        },
        txtStyle: {
          title: leaveuKeyTerms['指示器位置'],
          type: 0,
          list: [
            {
              val: leaveuKeyTerms['居左'],
              icon:'icondoc_left'
            },
            {
              val: leaveuKeyTerms['居中'],
              icon:'icondoc_center'
            },
            {
              val: leaveuKeyTerms['居右'],
              icon:'icondoc_right'
            }
          ]
        },
        // 图片样式
        imgConfig: {
          cname: "docStyle",
          title: leaveuKeyTerms['轮播图样式'],
          type: 0,
          list: [
            {
              val: leaveuKeyTerms['圆角'],
              icon: "iconPic_fillet",
            },
            {
              val: leaveuKeyTerms['直角'],
              icon: "iconPic_square",
            },
          ],
        },
      },
      pageData: {},
      bgColor: [],
      mTOP: 0,
      edge: 0,
      imgStyle: 0,
      imgSrc: "",
      docStyle: 0,
      dotPosition: 0,
      isShow:true,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.pageData = this.$store.state.mobildConfig.defaultArray[
        this.num
      ];
      this.setConfig(this.pageData);
    });
  },
  methods: {
    setConfig(data) {
      if (!data) return;
      if(data.mbConfig){
        this.isShow = data.isShow.val;
        this.bgColor = data.bgColor.color;
        this.mTOP = data.mbConfig.val;
        this.edge = data.lrConfig.val;
        this.imgStyle = data.imgConfig.type;
        this.imgSrc = data.swiperConfig.list.length
                ? data.swiperConfig.list[0].img
                : "";
        this.docStyle = data.docConfig.type;
        this.dotPosition = data.txtStyle.type;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.empty-box{
  height: 170px;
}
.mobile-page {
  position: relative;
  width: auto;
  .banner {
    // width: 100%;
    // height: 150px;
    margin-top: 0;
		// &.on{
		// 	margin-top: -150px;
		// }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      &.doc {
        border-radius: 0;
      }
    }
  }
  .bg {
    width: 100%;
    height: 150px;
    background: linear-gradient(90deg, #F62C2C 0%, #F96E29 100%);
  }
}
.dot {
  position: absolute;
  left: 0;
  bottom: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  &.number{
    bottom: 4px;
  }
  .num{
    width: 25px;
    height: 18px;
    line-height: 18px;
    background-color: #000;
    color: #fff;
    opacity: 0.3;
    border-radius: 8px;
    font-size: 12px;
    text-align: center;
  }
  .dot-item {
    width: 5px;
    height: 5px;
    background: #AAAAAA;
    border-radius: 50%;
    margin: 0 3px;
  }
  &.line-dot {
    bottom: 20px;
    .line_dot-item {
      width: 8px;
      height: 2px;
      background: #AAAAAA;
      margin: 0 3px;
    }
  }
}
</style>
