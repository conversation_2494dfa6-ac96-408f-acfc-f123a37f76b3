<template>
  <div class="goods_count">
    <div class="store-banner" v-if="storeDetail.top_banner">
      <img :src="storeDetail.top_banner" alt="">
    </div>
    <div class="menu-count">
      <div class="user-menu user-wrapper acea-row row-middle">
        <div class="menu-main acea-row row-middle">
          <div @click="goPage(menu,index)" @mouseenter="showCategory(index)" class="menu-item" v-for="(menu,index) in userMenu" :key="index">{{menu.title}}</div>
        </div>
        <div class="menu-search acea-row row-middle">
          <div class="text"><input type="text" placeholder="店内商品搜索" v-model="search"></div>
          <div class="bnt" @click="submit"><span class="iconfont icon-xiazai5"></span></div>
        </div>
        <div v-if="seen" class="category acea-row row-middle" @mouseleave="leave()">
          <div class="sort">
            <div
              class="item acea-row row-between-wrapper"
              v-for="(item, index) in category"
              :key="index"
            >
              <div class="name line1">{{ item.cate_name }}<span class="iconfont icon-you"></span></div>
              <div class="sortCon">
                <div class="erSort acea-row">
                  <div
                    class="sub-item line1"
                    v-for="(itemn, indexn) in item.children"
                    :key="indexn"
                    @click="goCategoryGoods(itemn.store_category_id)"
                  >
                    {{ itemn.cate_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <nuxt-link class="moreBtn" :to="{path:'/store/category',query:{id:storeInfo.mer_id}}">查看全部商品</nuxt-link>
        </div>
      </div>
    </div>
    <div class="goods-detail">
      <div class="wrapper_1200 acea-row">
        <div class="goods-main">
          <div class="acea-row" style="position: relative;">
            <div class="carousel">
              <img :src="storeInfo.slider_image[slideIndex]" class="preview" />
              <client-only>
                <div v-swiper:carousel="swiperOption">
                  <div class="swiper-wrapper">
                    <div
                      v-for="(item, index) in storeInfo.slider_image"
                      :key="index"
                      :class="{ on: slideIndex === index }"
                      class="swiper-slide"
                      @mouseover="swiperMouseover(index)"
                    >
                      <img :src="item" />
                    </div>
                  </div>
                  <div class="swiper-button-prev swiper-button-white"></div>
                  <div class="swiper-button-next swiper-button-white"></div>
                </div>
              </client-only>
              <div class="acea-row row-middle">
                <div class="btn" style="width:60px" @click="collect">
                <span
                  :class="[
                    'iconfont',
                    storeInfo.isRelation ? 'icon-yishoucang' : 'icon-shoucang2'
                  ]"
                ></span
                >{{ storeInfo.isRelation ? "已收藏" : "收藏" }}
                </div>
                <div class="btn contactBtn" @click="chatShow" v-if="mer_service.services_type== 1">
                  <span class="iconfont icon-kefu2"></span>联系客服
                </div>
                <div class="btn contactBtn" v-else-if="mer_service.services_type == 2 && mer_service.service_phone">
                  <el-tooltip popper-class="tps" effect="dark" :content="'客服电话：'+mer_service.service_phone" placement="right">  
                    <div><span class="iconfont icon-kefu2"></span>联系客服</div>
                  </el-tooltip>
                </div>
                <a class="btn contactBtn" v-if="mer_service.services_type== 4" :href="mer_service.mer_customer_link" target="blank">
                  <span class="iconfont icon-kefu2"></span>联系客服
                </a>
              </div>
            </div>
            <div class="text-wrapper">
              <div class="title">{{ storeInfo.store_name }}</div>
              <div class="acea-row row-middle money-wrapper">
                <div class="money-wrap">
                  <div class="acea-row row-middle price_count">
                    <div class="price">
                      <span>预售价</span>
                      ￥<span class="presell_price">{{
                        attrValueSelected
                          ? attrValueSelected.price
                          : storeInfo.price
                      }}</span>
                       <del class="ot_price">￥{{
                      attrValueSelected && attrValueSelected.ot_price
                        ? attrValueSelected.ot_price
                        : storeInfo.ot_price
                    }}</del>
                    </div>

                  </div>
                </div>
                <div class="acea-row row-column row-center-wrapper sales">
                  <div class="num">
                    {{storeInfo.sales ? storeInfo.sales : 0}}
                  </div>
                  <div>已售</div>
                </div>
              </div>
              <div class="ship_date" v-if="presellInfo.delivery_type == 1">
								<div v-if="presellInfo.presell_type==1">发货时间：<span>支付成功后{{ presellInfo.delivery_day }}天内发货</span></div>
								<div v-if="presellInfo.presell_type==2">发货时间：<span>付尾款成功后{{ presellInfo.delivery_day }}天内发货</span></div>
							</div>
							<div class="ship_date" v-if="presellInfo.delivery_type == 2">
               发货时间：<span>预售结束后{{ presellInfo.delivery_day }}天内发货</span>
							</div>
              <div
                v-for="(item, index) in productAttr"
                :key="index"
                class="acea-row size-wrapper"
              >
                <div class="label">{{ item.attr_name }}</div>
                <div class="acea-row list">
                  <label
                    v-for="(itm, idx) in item.attr_values"
                    :key="idx"
                    class="item"
                  >

                    <input
                      v-model="attrSelected[index]"
                      type="radio"
                      :name="index"
                      :value="itm"
                      hidden
                    />
                    <div class="acea-row cont">
                      <div class="acea-row row-middle name">{{ itm }}</div>
                      <div class="iconfont icon-xuanzhong4"></div>
                    </div>
                    <!-- {{attrSelected[index]}} -->
                  </label>
                </div>
              </div>
              <div v-if="shippingValue" class="number-wrapper acea-row">
                <div class="label" @mouseover="tempTitle = true" @mouseleave="tempTitle = false">运费 <span class="iconfont icon-duoshanghupc-shuomingdanchuang" v-if="storeInfo.temp&&storeInfo.temp.info"></span></div>
                <div class="guaranee_tel" :style="{display:((tempTitle || tempInfo) && storeInfo.temp.info)? 'block':'none'}"  @mouseover="tempInfo = true" @mouseleave="tempInfo = false">
                  <div class="content" style="white-space: pre-line;">
                    {{storeInfo.temp.info}}
                  </div>
                </div>
                <div class="counter-wrap">
                  {{shippingValue}}
                </div>
              </div>
              <div v-if="guarantee && guarantee.length" class="number-wrapper acea-row">
                <div class="label" @mouseover="guaranteeTitle = true" @mouseleave="guaranteeTitle = false">保障 <span class="iconfont icon-duoshanghupc-shuomingdanchuang"></span></div>
                <div class="guaranee_tel" :style="{display:(guaranteeTitle || guaranteeInfo)? 'block':'none'}"  @mouseover="guaranteeInfo = true" @mouseleave="guaranteeInfo = false">
                  <div class="content">
                    <div v-for="(item,index) in guarantee" class="item">
                      <div class="name">{{item.guarantee_name}}</div>
                      <div class="info" style="white-space: pre-line;">{{item.guarantee_info}}</div>
                    </div>
                  </div>
                </div>
                <div class="guaranteeAttr">
                  <div class="atterTxt1" v-for="(item,index) in guarantee">
                    <span class="iconfont icon-duoshanghupc-baozhang"></span>{{item.guarantee_name ? item.guarantee_name : ''}}
                  </div>
                </div>
                </div>
              <div class="number-wrapper acea-row">
                <div class="label">数量</div>
                <div class="counter-wrap">
                  <div class="counter">
                    <button
                      class="iconfont icon-shangpinshuliang-jian"
                      :disabled="count === 1 || !stock"
                      @click="minus"
                    ></button>
                    <input v-model="count" @input="inputNum"/>
                    <button
                      class="iconfont icon-shangpinshuliang-jia"
                      :disabled="count === stock || !stock"
                      @click="plus"
                    ></button>
                  </div>
                  <span>（限量 {{ stock }}{{ storeInfo.unit_name }}）</span>
                </div>
              </div>
              <div class="button-wrapper">
                <button
                  v-if="presellInfo.presell_status == 1"
                  class="btn"
                  :disabled="buyDisabled"
                  @click="buy(1, $event)"
                >
                  {{ presellInfo.presell_type === 1 ? "立即支付" : "支付定金："+minNum+'元起'  }}
                </button>
                 <button
                  v-if="presellInfo.presell_status == 1 && !stock"
                  class="btn"
                  disabled
                >
                  已售罄
                </button>
                <button
                  v-if="presellInfo.presell_status == 0 || presellInfo.presell_status == 2"
                  class="btn"
                  disabled
                >
                   {{ presellInfo.presell_status == 0 ? "未开始" : "已结束"  }}
                </button>
              </div>
            </div>
          </div>
          <div class="detail-wrapper">
            <div v-if="presellInfo.presell_type == 2" class="presell_process">
              <div class="process_item">
                <span class="title">预售流程</span>
              </div>
              <div class="process_item">
                <img src="~/assets/images/presell_process_01.png"/>
                <div class="process_step">
                  <span class="step_name">1.付定金</span>
                  <div class="step_time">{{presellInfo.end_time}}</div>
                </div>
              </div>
              <div class="process_item">
                <img src="~/assets/images/presell_process_02.png"/>
                <div class="process_step">
                  <span class="step_name">2.支付尾款</span>
                  <div v-if="presellInfo.final_start_time && presellInfo.final_end_time" class="step_time">
                  {{ presellInfo.final_start_time }}
										<span class='area_line'>-</span>
										{{ presellInfo.final_end_time }}
                  </div>
                </div>
              </div>
              <div class="process_item">
                <img src="~/assets/images/presell_process_03.png"/>
                <div class="process_step">
                  <span class="step_name">3.商品发货</span>
                  <div class="step_time">
                    付尾款成功后{{ presellInfo.delivery_day }}天内发货
                  </div>
                </div>
              </div>
            </div>
            <div class="detail-hd acea-row">
              <div class="tab acea-row">
                <div
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 0 }"
                  @click="tab(0)"
                >
                  产品详情
                </div>
                <div
                  v-if="storeInfo.params.length > 0"
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 2 }"
                  @click="tab(2)"
                >
                  商品参数
                </div>
                <div
                  v-if="replyInfo.stat.count > 0"
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 1 }"
                  @click="tab(1)"
                >
                  累计评论({{  replyInfo.stat.count }})
                </div>
              </div>
              <div
                class="acea-row row-center-wrapper qrcode-button"
                @mouseover="qrcodeShow = true"
                @mouseout="qrcodeShow = false"
              >
                <span class="iconfont icon-saoma"></span>手机购买<span
                :class="[
                  'iconfont',
                  qrcodeShow ? 'icon-xiangshang1' : 'icon-xiangxia2'
                ]"
              ></span>
                <div class="qrcode">
                  <div class="phoneBuy">
                    <div class="qrcode1 contactService">
                      <client-only>
                        <vue-qr :text="$store.state.domain + 'pages/activity/goods_seckill_details/index?id=' + storeInfo.product_id" :size="200" :margin="6" style="display: block"></vue-qr>
                      </client-only>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="detail-bd">
              <div v-show="tabIndex === 0">
                <div
                  v-if="storeInfo.content.content || storeInfo.content.content.image"
                  class="detail-html">
                  <div v-html="storeInfo.content.content"></div>
                  <div v-if="storeInfo.content.type === 1" class="product_content">
                    <div v-if="storeInfo.content && storeInfo.content.content.title" class="title">{{storeInfo.content.content.title}}</div>
                    <div v-if="storeInfo.content && storeInfo.content.content.image" class="pictures">
                      <img v-for="(item,index) in storeInfo.content.content.image" :key="index" :src="item"/>
                    </div>
                  </div>
                </div>
                <div v-else class="nothing">
                  <img src="@/assets/images/noDetail.png" />
                  <div>暂无商品详情</div>
                </div>
              </div>
               <div v-show="tabIndex === 2">
                <div class="productSpecs">
                  <div class="item" v-for="(item,index) in storeInfo.params" :key="index">
                    <div class="name">{{item.name}}</div>
                    <div class="val">{{item.value}}</div>
                  </div>
                </div>
              </div>
              <div v-show="tabIndex === 3 && priceRule.content">
                <div style="padding: 20px;">
                  <div v-html="priceRule.content"></div>
                </div>
              </div>
              <div v-show="tabIndex === 1" class="comment">
                <div class="comment-hd">
                  <div class="acea-row row-between-wrapper">
                    <div class="rate">
                      <span>{{ replyInfo.rate }}</span><span>满意</span>
                    </div>
                    <div class="acea-row row-middle score">
                      评分
                      <div class="cont">
                      <span
                        v-for="(v, i) in 5"
                        :key="i"
                        :class="{ on: i + 1 <= storeInfo.rate }"
                        class="iconfont icon-pingjia"
                      ></span>
                      </div>
                    </div>
                  </div>
                  <div class="menu">
                    <div
                      class="item"
                      :class="{ on: reply.type === 0 }"
                      @click="replyTypeChange(0, 'count')"
                    >
                      全部({{ replyInfo.stat.count }})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 1 }"
                      @click="replyTypeChange(1, 'best')"
                    >
                      好评({{ replyInfo .stat.best}})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 2 }"
                      @click="replyTypeChange(2, 'middle')"
                    >
                      中评({{ replyInfo.stat.middle }})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 3 }"
                      @click="replyTypeChange(3, 'negative')"
                    >
                      差评({{ replyInfo.stat.negative }})
                    </div>
                  </div>
                </div>
                <div class="comment-bd">
                  <template v-if="replyList.length">
                    <div v-for="item in replyList" :key="item.id" class="item">
                      <div class="acea-row row-middle item-hd">
                        <div class="image">
                          <img v-if="item.avatar" :src="item.avatar"/>
                          <img v-else src="~assets/images/f.png" alt="">
                        </div>
                        <div class="text">
                          <div class="acea-row row-middle name">
                            {{ item.nickname }}
                            <div class="star">
                            <span
                              v-for="(v, i) in 5"
                              :key="i"
                              class="iconfont icon-pingjia"
                              :class="{ on: i + 1 <= item.rate }"
                            ></span>
                            </div>
                          </div>
                          <div>{{ item.create_time }}</div>
                        </div>
                      </div>
                      <div class="item-bd">
                        <div>{{ item.comment }}</div>
                        <div class="image-wrapper">
                          <div
                            v-for="(itm, idx) in item.pics"
                            :key="idx"
                            class="image"
                          >
                            <el-image
                              style="width: 54px; height: 54px"
                              :src="itm"
                              :preview-src-list="item.pics"
                            ></el-image>
                          </div>
                        </div>
                        <div v-if="item.merchant_reply_content" class="reply">
                          <div class="item">
                            <span>店小二：</span>{{ item.merchant_reply_content }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <img
                    v-else-if="!replyList.length"
                    src="@/assets/images/noEvaluate.png"
                  />
                </div>
                <div v-if="replyList.length" class="acea-row row-right">
                  <el-pagination
                    layout="prev, pager, next"
                    prev-text="上一页"
                    next-text="下一页"
                    :page-size="reply.limit"
                    :total="reply.count"
                    @current-change="getReply"
                    @prev-click="getReply"
                    @next-click="getReply"
                  ></el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="recom-section">
          <div class="user-info">
            <div class="store-basis">
              <div class="store-logo">
                <img :src="storeDetail.mer_avatar" alt="">
              </div>
              <div class="line1">
                <span v-if="storeDetail.is_trader" class="trader">自营</span>
                <span class="store-name line1">{{storeDetail.mer_name}}</span>
              </div>
            </div>
            <div class="store-info">
              <div class="items acea-row row-middle">
                <span class="titles">店铺评分</span>
                <div class="cont">
                        <span
                          v-for="(v, i) in 5"
                          :key="i"
                          :class="{ on: i + 1 <= score.number.toFixed(1) }"
                          class="iconfont icon-pingjia star"
                        ></span>
                </div>
              </div>
              <div class="items acea-row row-middle">
                <span class="titles">关注人数</span>
                <span class="desc">{{storeDetail.care_count < 10000 ? storeDetail.care_count : (storeDetail.care_count/10000).toFixed(1)+'万'}}人</span>
              </div>
              <div v-if="mer_service.service_phone && mer_service.services_type == 2" class="items acea-row row-middle">
                <span class="titles">联系电话</span>
                <span class="desc">{{mer_service.service_phone}}</span>
              </div>
              <div v-if="storeDetail.isset_certificate" class="items acea-row row-middle">
                <span class="titles">店铺资质</span>
                <nuxt-link class="desc" :to="{path:'/qualifications',query:{id:storeDetail.mer_id,storeName:storeDetail.mer_name}}">
                  <img class="store_qualify" src="~/assets/images/store_qualify.png" alt="">
                  <span class="license">企业营业执照</span>
                </nuxt-link>
              </div>
            </div>
            <div class="store-favorites">
              <button class="collection" @click="goStore">进店逛逛</button>
              <button class="collection" :class="storeDetail.care ? 'care' : ''" @click="followToggle(mer_id)">{{storeDetail.care ? '已收藏' : '收藏店铺'}}</button>
            </div>
          </div>
          <div class="store-recommend" v-if="goodsList && goodsList.length">
            <div class="title"><span>店铺推荐</span></div>
            <div class="list">
              <nuxt-link
                v-for="(item, index) in goodsList"
                :key="index"
                :to="`/goods_detail/${item.product_id}`"
                class="item"
              >
                <div class="image">
                  <img :src="item.image" />
                </div>
                <div class="text">
                  <div class="name">{{ item.store_name }}</div>
                  <div class="acea-row row-between-wrapper">
                    <div class="money">
                      ￥<span>{{ item.price }}</span>
                    </div>
                    <div class="sales">销量 {{ item.sales }}</div>
                  </div>
                </div>
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    <chat-room
      v-if="chatPopShow"
      :chatId="storeInfo.mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>

</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
import ChatRoom from "@/components/ChatRoom";

export default {
  components: { countDown, ChatRoom },
  auth: false,
  data() {
    return {
      chatPopShow: false,
      swiperOption: {
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        },
        slidesPerView: "auto",
        spaceBetween: 10,
        slidesOffsetBefore: 25,
        slidesOffsetAfter: 25,
        observer: true,
        observeParents: true
      },
      slideIndex: 0,
      tabIndex: 0,
      search: '',
      userMenu:[
        {
          key:0,
          link:'/store',
          title:'店铺首页',
        },
        {
          key:4,
          title:'全部分类',
          link:'/store/category'
        },
        {
          key:1,
          title:'领优惠券',
          link:'/store/storeCoupon'
        }
      ],
      category: [], //店铺分类
      seen: false,
      menuCur: '',
      reply: {
        type: 'count',
        page: 1,
        limit: 40,
        finished: false,
        count: 10
      },
      coupon: {
        page: 1,
        limit: 3,
        finished: false
      },
      attrSelected: [],
      attrValueSelected: null,
      count: 1,
      stock: 1,
      unique: "",
      qrcodeShow: false,
      codeUrl: "",
      buyDisabled: false,
      replyCount: 0,
      replyList: [],
      replyInfo: { stat: {} },
      id: '',
      storeDetail: {},
      mer_service: {},
      priceRule: {},
      guaranteeTitle: false,
      guaranteeInfo: false,
      tempTitle: false,
      tempInfo: false,
      minNum: 0,
      slideNum: 0,
    };
  },
  computed: {
    score: function() {
      let store = this.storeDetail,
        score = {
          star: 0,
          number: 0
        };
      if ('postage_score' in store) {
        score.number = (parseFloat(store.postage_score) + parseFloat(store.product_score) + parseFloat(store.service_score)) / 3;
        score.star = score.number / 5 * 100;
      }
      return score;
    }
  },
  watch: {
    productValue: {
      immediate: true,
      handler(attr) {
          let keyArr = [];
          for (const key in attr) {
            if (attr.hasOwnProperty(key)) {
              keyArr.push(key);
            }
          }
          let first = keyArr[0];
          let arr = first.split(",");
          this.attrSelected = arr;
      }
    },
    attrValueSelected(n){
      if(n.image) {
        if(this.slideNum <= 1){
          this.storeInfo.slider_image.unshift(n.image)
        }else{
          this.storeInfo.slider_image[0] = n.image
        } 
        this.slideIndex = 0;
      }
    },
    attrSelected: {
      immediate: true,
      handler(attr) {
        if (attr.length) {
          let name = attr.join(),
            value = this.productValue[name];
          if (value) {
            this.buyDisabled = false;
            this.attrValueSelected = value;
            this.stock = value.stock;
            this.unique = value.unique;
          }else{
            this.buyDisabled = true;
            this.stock = 0;
          }
        } else {
          this.stock = this.storeInfo.stock;
          this.unique = this.productValue[""].unique
        }
      }
    }
  },
  async asyncData({ error, params, app, query }) {
    try {
      const [detail] = await Promise.all([
        app.$axios.get(`/api/store/product/presell/detail/${params.id}`),
      ]);
      return {
        productAttr: detail.data.product.attr,
        productValue: detail.data.product.sku,
        storeInfo: detail.data.product,
        presellInfo: detail.data,
        goodsList: detail.data.product.merchant.recommend,
        storeDetail: detail.data.product.merchant,
        mer_service: detail.data.product.merchant.services_type,
        id: params.id,
        mer_id: detail.data.mer_id,
        shippingValue: detail.data.temp ? detail.data.temp.name : '',
        guaranteeValue: detail.data.product.guarantee ? detail.data.product.guarantee.template_name : '',
        guarantee: detail.data.product.guaranteeTemplate ? detail.data.product.guaranteeTemplate : [],
        shipping: detail.data.product.temp ? detail.data.product.temp.info : ''
      };
    } catch (e) {
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: this.storeInfo.store_name+"-"+this.$store.state.titleCon
    }
  },
  beforeMount() {

    this.getReply(1)
  },
  mounted() {

    this.$nextTick(() => {
      document.body.setAttribute("style", "background:#ffffff");
    });
    // 找到最小定金金额
		let objs = Object.keys(this.storeInfo.sku);
		let m = objs.map(key => this.storeInfo.sku[key]);
		this.minNum = Math.min.apply(Math, m.map(function(o) {
			return o.down_price
		}))
    if (this.$auth.loggedIn) {
      this.$axios.get(`/api/pc/care`,{
        params:{
          type: 10,
          id: this.mer_id
      }
      }).then(res => {
        this.storeDetail.care = res.data.care
      });
    }
    this.getCategory();
    this.getPriceRule();
  },
  beforeDestroy() {
    document.body.removeAttribute("style");
  },
  methods: {
    chatShow() {
      if(this.$auth.loggedIn){
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    //获取店铺分类
    getCategory(){
      this.$axios
        .get(`/api/store/merchant/category/lst/${this.mer_id}`)
        .then(res => {
          this.category = res.data
        });
    },
    showCategory(index){
      if(index == 1){
        this.seen = true
      }else{
        return
      }
    },
    goPage(menu, index) {
      this.menuCur = menu.key
      this.$router.push({
        path: `${menu.link}`,
        query: {id: this.mer_id}
      });
    },
    // 价格说明
    getPriceRule() {
      this.$axios
        .get(`/api/store/product/price_rule/${this.storeInfo.cate_id}`)
        .then(res => {
        this.priceRule = res.data
      });
    },
    //进店逛逛
    goStore(){
      this.$router.push({ path: `/store?id=${this.mer_id}` });
    },
    followToggle(id){
      if (!this.$auth.loggedIn) {
        this.$store.commit("isLogin", true);
      }
      this.storeDetail.care ? this.unfollow(id) : this.follow(id);
    },
    unfollow(id){
      let that = this;
      that.$axios.post('/api/user/relation/delete',{
        type: 10,
        type_id: id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = false;
          this.$message.success(res.message);
        }
      })
    },
    follow(id){
      let that = this;
      that.$axios.post('/api/user/relation/create',{
        type: 10,
        type_id: id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = true;
          this.$message.success(res.message);
        }
      })
    },
    leave() {
      this.seen = false;
    },
    submit() {
      if (this.search.trim() !== '') {
        this.$router.push({path: '/store/category', query: {title: this.search.trim(), id: this.mer_id}});
        this.search = '';
      } else {
        this.$message.error("请输入要搜索的内容");
      }
    },
    goCategoryGoods(cateId, indexn) {
      this.$router.replace({path: '/store/category', query: {id: this.mer_id, cateId: cateId}});
    },
    inputNum(){
      this.count = parseInt(this.count) >= this.stock?this.stock:this.count;
      this.count = parseInt(this.count) <=1?1:this.count;
    },
    swiperMouseover(index) {
      this.slideIndex = index;
    },
    callPaginate(num) {
      this.reply.page = num;
      this.getReply(num);
    },
    tab(type) {
      this.tabIndex = type;
    },
    replyTypeChange(type, count) {
      this.reply.type = type;
      this.reply.count = count;
      this.reply.page = 1;
      this.replyList = [];
      this.$axios
        .get(`/api/store/product/reply/lst/${this.storeInfo.product_id}`, {
          params: {
            page: this.reply.page,
            limit: this.reply.limit,
            type: count
          }
        })
        .then(res => {
          this.replyList = res.data.list;
          this.replyInfo = res.data;
          this.reply.count = res.data.count;
        });
    },
    // 加入购物车 | 立即购买
    buy(type, event) {
      if(this.buyDisabled){
        return
      }
      let btn = event.target;
      btn.disabled = true;
      this.$axios
        .post("/api/user/cart/create", {
          cart_num: this.count,
          is_new: 1,
          product_attr_unique: this.unique,
          product_type: 2,
          product_id: this.id,
        })
        .then(res => {
          btn.disabled = false;
          this.$cookies.remove('cart_checked');
          if (type) {
            this.$router.push({
              path: `/order_confirm?new=1&cartId=${res.data.cart_id}`
            });
          } else {
            this.$store.commit(
              "cartNum",
              this.count + this.$store.state.cartnumber
            );
            this.$message.success("加入购物车成功");
          }
        })
        .catch(err => {
          btn.disabled = false;
          this.$message.error(err)
        });
    },
    minus() {
      this.count--;
    },
    plus() {
      this.count++;
    },
    // 获取评论
    getReply(num) {
      this.$axios
        .get(`/api/store/product/reply/lst/${this.storeInfo.product_id}`, {
          params: {
            page: num,
            limit: this.reply.limit,
            type: this.reply.type
          }
        })
        .then(res => {
          this.replyList = res.data.list;
          this.replyCount = res.data.count
          this.replyInfo = res.data
        })
        .catch(err => {
          this.$message.error(err);
        });
    },
    // 收藏 | 取消收藏
    collect() {
      if (!this.$auth.loggedIn) {
        return this.$store.commit("isLogin", true);
      }
      if (this.storeInfo.isRelation) {
        this.$axios
          .post("/api/user/relation/delete", {
            type_id: this.presellInfo.product_presell_id,
            type: 2
          })
          .then(res => {
            this.$message.success("取消收藏成功");
          })
          .catch(err => {
            this.$message.error(err);
          });
      } else {
        this.$axios
          .post("/api/user/relation/create", {
            type_id: this.presellInfo.product_presell_id,
            type: 2
          })
          .then(res => {
            this.$message.success("收藏成功");
          })
          .catch(err => {
            this.$message.error(err);
          });
      }
      this.storeInfo.isRelation = !this.storeInfo.isRelation;
    }
  }
};
</script>
<style lang="scss" scoped>
.store-banner{
  width: 100%;
  height: 130px;
  img{
    object-fit: none;
    width: 100%;
    height: 100%;
  }
}
.store-name{
  display: inline-block;
  width: 120px;
  position: relative;
  top: 4px;
}
.menu-count{
  width: 100%;
  height: 40px;
  background: #DFDFDF;
}
.productSpecs{
  overflow: hidden;
  padding: 30px;
  .item{
    float: left;
    display: flex;
    width: 50%;
    margin-bottom: 20px;
    .name{
      color: #999999;
      min-width: 90px;
      max-width: 120px;
      text-align: right;
    }
    .val{
      color: #282828;
    }
  }
}
.user-menu{
  position: relative;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 1200px;
  margin: 0 auto;
  .category{
    position: absolute;
    top: 40px;
    left: 0;
    background-color: rgba(254,248,248,.96);
    width: 100%;
    padding: 40px 20px 20px;
    z-index: 10;
    .name{
      width: 130px;
      position: relative;
      padding-right: 20px;
      margin-right: 30px;
      cursor: pointer;
      .iconfont{
        font-size: 10px;
        position: absolute;
        right: 0;
        top: 3px;
        color: #282828;
      }
    }
    .sortCon{
      width: 1000px;
      .sub-item{
        margin: 0 15px 15px;
        color: #666666;
        cursor: pointer;
      }
    }
    .erSort{
      align-items: center;
    }
    .item{
      margin-bottom: 20px;
      align-items: baseline;
    }
    .moreBtn{
      color: #282828;
      font-size: 12px;
      width: 100px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      border-radius: 13px;
      border: 1px solid #666666;
    }
  }
  .menu-main{
    width: 300px;
    height: 40px;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    .menu-item{
      display: inline-block;
      height: 26px;
      line-height: 26px;
      color: #282828;
      padding: 0 10px;
      cursor: pointer;
      &.active{
        color: #fff;
        background: #282828;
        border-radius: 15px;
      }
    }
  }
  .menu-search{
    width: 220px;
    height: 24px;
    background-color: #fff;
    border-radius: 17px;
    .text{
      width: 175px;
    }
    input{
      border: none;
      height: 24px;
      line-height: 24px;
      color: #999999;
      padding: 0 15px;
      border-radius: 17px;
      &:focus{
        border: none;
        outline: none;;
      }
    }
    .bnt{
      width: 44px;
      background-color: #282828;
      color: #fff;
      border-radius: 0 17px 17px 0;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
    }
  }
}
.product_content .title{
  text-align: center;
  font-size: 18px;
  margin: 5px 0;
}
.user-info{
  width: 210px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 350px;
  background: #fff;
  color: #282828;
  font-size: 14px;
  border: 1px solid #efefef;
  border-radius: 4px;
  padding: 0 20px;
  .store-basis{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-bottom: 1px dashed #ECECEC;
    height: 130px;
    .trader{
      display: inline-block;
      width: 32px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      color: #fff;
      background: #E93323;
      border-radius: 2px;
      margin-right: 3px;
      font-size: 12px;
      font-family: 'PingFang SC';
    }
  }
  .store-logo{
    width: 61px;
    height: 61px;
    margin-bottom: 15px;
    img{
      width: 61px;
      height: 61px;
      border-radius: 50%;
    }
  }
  .name{
    margin-top: 10px;
    padding: 0 15px;
  }
}
.store-info{
  padding: 15px 0 0;
  position: relative;
  border-bottom: 1px dashed #ECECEC;
  .service{
    right:210px;
    position:absolute;
    top:0;
    .ewm{
      width:140px;
      border:1px solid #eeeeee;
      background-color:#fff;
      padding: 10px 15px;
      -webkit-justify-content: space-between;
      justify-content: space-between;
      align-items: center;
      color: #282828;
      .tip{
        font-size: 14px;
        color: #666666;
        margin-top: 10px;
      }
    }
  }
  .items {
    font-size: 12px;
    color: #7e7e7e;
    margin-bottom: 15px;
    .iconfont{
      cursor: pointer;
    }
    .cont {
      margin-left: 8px;
    }
    .star {
      font-size: 12px;
      color: #e6e6e6;
      ~ .star {
        margin-left: 5px;
      }
      &.on {
        color: #e93323;
      }
    }
    .titles{
      color: #999999;
      font-size: 12px;
      margin-right: 15px;
    }
    .desc{
      color:#333333;
      position: relative;
      .store_qualify{
        width: 16px;
        height: 16px;
        &:hover + .license{
          display: inline-block;
        }
      }
    }
    .license{
      width: 90px;
      line-height: 26px;
      color: #fff;
      text-align: center;
      background: #282828;
      border-radius: 5px;
      position: absolute;
      top: 26px;
      left: -10px;
      display: none;
      &:before{
        content: '';
        display: inline-block;
        border: 3px solid transparent;
        border-bottom-color: #282828;
        position: absolute;
        top: -6px;
        left: 15px;
      }
    }
  }
}
.goods-detail {
  padding-top: 40px;
  border-top: 1px solid #efefef;
  > div {
    > div {
      ~ div {
        margin-left: 40px;
      }
    }
  }
  .goods-main {
    flex: 1;
    min-width: 0;
  }
  .carousel {
    width: 380px;
    .preview {
      display: block;
      width: 380px;
      height: 380px;
    }
    .swiper-container {
      margin-top: 10px;
      margin-bottom: 20px;
      .swiper-button-prev,
      .swiper-button-next {
        top: 0;
        width: 25px;
        height: 100%;
        margin-top: 0;
        background-color: rgba(0, 0, 0, 0.3);
        background-size: 12px 22px;
      }
      .swiper-button-prev {
        left: 0;
      }
      .swiper-button-next {
        right: 0;
      }
      .swiper-slide {
        width: 70px;
        height: 70px;
        border: 2px solid transparent;
        overflow: hidden;
        cursor: pointer;
        &.on {
          border-color: #e93323;
        }
        img {
          display: block;
          width: 70px;
          height: 70px;
        }
      }
    }
    .btn {
      margin-right: 30px;
      font-size: 12px;
      color: #4b4b4b;
      cursor: pointer;
      position: relative;
      .qrcode1 {
        display: none;
        box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
        background: #fff;
        padding: 6px;
        position: relative;
        width: 100px;
        img{
          width: 100%;
        }
        &.contactService {
          position: absolute;
          left: 50%;
          top: 25px;
          z-index: 10;
          width: 100px;
          height: 100px;
          margin-left: -50px;
        }
      }
    }
    .contactBtn:hover {
      .qrcode1 {
        display: inline;
      }
    }
    .iconfont {
      margin-right: 6px;
      font-size: 14px;
      color: #e93323;
    }
  }
  .text-wrapper {
    flex: 1;
    min-width: 0;
    margin-left: 40px;
    .title {
      font-size: 20px;
      line-height: 26px;
      color: #333333;
    }
    .ship_date{
      margin-top: 15px;
      font-size: 14px;
      color: #999999;
      span{
        color: #FD6523;
      }
    }
    .money-wrapper {
      height: 64px;
      margin-top: 18px;
      background: url("~assets/images/presell-money-back.png") center/cover no-repeat;
      color: #ffffff;
      .price_count{
        display: flex;
      }
      .money-wrap {
        flex: 1;
        min-width: 0;
        padding-left: 32px;
      }
      del {
        font-size: 14px;
      }
      .price {
        font-size: 14px;
        .presell_price{
          font-weight: bold;
          font-size: 30px;
          position: relative;
          top: 2px;
        }
      }
      .vip {
        width: 100px;
        height: 25px;
        border-radius: 2px;
        margin-left: 14px;
        background: linear-gradient(205deg, #fdcaa4 0%, #fce3c3 100%);
        overflow: hidden;
        font-size: 12px;
        color: #0f0f0f;
        .iconfont {
          width: 32px;
          background: #0f0f0f;
          font-size: 8px;
          color: #fcdcbb;
        }
        .money {
          flex: 1;
          min-width: 0;
          span {
            font-size: 14px;
          }
        }
      }
      .sales {
        position: relative;
        height: 100%;
        padding-right: 20px;
        padding-left: 20px;
        font-size: 12px;
        &::before {
          content: "";
          position: absolute;
          top: 14px;
          bottom: 12px;
          left: 0;
          width: 1px;
          border-left: 1px solid rgba(255, 255, 255, 0.24);
        }
        .num {
          margin-bottom: 3px;
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
    .coupon-wrapper {
      background-color: #f7f7f7;
      .coupon-bd {
        padding-top: 18px;
        padding-bottom: 18px;
        font-size: 12px;
        color: #5a5a5a;
        .label {
          width: 80px;
          padding-left: 20px;
        }
        .list {
          flex: 1;
          min-width: 0;
          padding-right: 24px;
        }
        .item {
          margin-top: 11px;
          &:first-child {
            margin-top: 0;
          }
        }
        .cell {
          width: 165px;
          height: 24px;
          background: url("~assets/images/coupon-back.png") left top/100% 100%
            no-repeat;
          font-size: 13px;
          color: #ffffff;
        }
        .cell-left {
          width: 65px;
        }
        .cell-right {
          flex: 1;
          min-width: 0;
          color: #e93323;
          max-width: 94px;
          padding: 0 3px;
        }
        .time {
          flex: 1;
          min-width: 0;
          padding-right: 8px;
          padding-left: 8px;
          font-size: 12px;
          color: #727272;
        }
        button {
          border: none;
          border-bottom: 1px solid #e93323;
          background: none;
          font-size: 12px;
          color: #e93323;
          &:disabled {
            border-color: #bbb;
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
      .coupon-ft {
        height: 36px;
        padding-right: 24px;
        background-color: #f2f2f2;
        .button {
          font-size: 12px;
          color: #666666;
          cursor: pointer;
          .iconfont {
            margin-left: 7px;
            font-size: 10px;
            color: #666666;
          }
        }
      }
    }
    .size-wrapper {
      margin-top: 30px;
      .label {
        width: 80px;
        padding-left: 20px;
        font-size: 12px;
        color: #5a5a5a;
      }
      .list {
        flex: 1;
        min-width: 0;
      }
      .item {
        margin-right: 12px;
        margin-bottom: 12px;
        box-sizing: border-box;
        cursor: pointer;
        .cont {
          position: relative;
          height: 36px;
          border: 1px solid #d3d3d3;
        }
        &:hover {
          .cont {
            border-color: #e93323;
            color: #e93323;
          }
        }
        input:checked {
          + .cont {
            border-color: #e93323;
            color: #e93323;
            .iconfont {
              display: block;
            }
          }
        }
      }
      .image {
        width: 34px;
        height: 34px;
      }
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
      .name {
        padding-right: 20px;
        padding-left: 20px;
      }
      .iconfont {
        position: absolute;
        right: -3px;
        bottom: -4px;
        display: none;
        font-size: 22px;
      }
    }
    .number-wrapper {
      margin-top: 30px;
      position: relative;
      .guaranee_tel{
        position: absolute;
        top: 20px;
        left: 0;
        background: #ffffff;
        z-index: 10;
        padding: 0 24px 24px;
        display: none;
        box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
        .item{
          margin-top: 24px;
          .name{
            font-size: 16px;
            color: #000000;
          }
          .info{
            font-size: 12px;
            color: #969696;
            margin-top: 6px;
          }
        }
      }
      .icon-duoshanghupc-shuomingdanchuang{
        color: #E93323;
        font-size: 12px;
        position: relative;

      }
      .label {
        width: 80px;
        padding-left: 20px;
        font-size: 12px;
        color: #5a5a5a;
        cursor: pointer;
      }
      .guaranteeAttr{
        display: inline-block;
        width: 445px;
      }
      .atterTxt1{
        display: inline-block;
        margin: 0 20px 11px 0;
        .icon-duoshanghupc-baozhang{
          display: inline-block;
          font-size: 14px;
          color: #E93323;
          margin-right: 2px;
        }
      }
      .counter-wrap {
        flex: 1;
        min-width: 0;
        span {
          vertical-align: bottom;
          font-size: 14px;
          color: #5a5a5a;
        }
      }
      .counter {
        display: inline-block;
        border: 1px solid #d3d3d3;
        font-size: 0;
        button {
          width: 44px;
          height: 36px;
          border: none;
          background: none;
          outline: none;
          font-weight: inherit;
          font-size: 12px;
          font-family: inherit;
          color: #707070;
          vertical-align: middle;
          &:disabled {
            color: #d0d0d0;
            cursor: not-allowed;
          }
        }
        input {
          width: 64px;
          height: 36px;
          border: none;
          border-right: 1px solid #d3d3d3;
          border-left: 1px solid #d3d3d3;
          outline: none;
          font-weight: inherit;
          font-size: 18px;
          font-family: inherit;
          text-align: center;
          color: #5a5a5a;
          vertical-align: middle;
        }
      }
    }
    .button-wrapper {
      margin-top: 46px;
      font-size: 0;
      .btn {
        padding: 0 20px;
        height: 50px;
        border: 1px solid #FD6523;
        background: #FD6523;
        border-radius: 4px;
        font-size: 16px;
        color: #fff;
        ~ .btn {
          margin-left: 18px;
        }
      }
      button {
        background: none;
        outline: none;
        vertical-align: middle;
        &:disabled {
          border-color: #bbb;
          color: #fff;
          background: #bbb;
          cursor: not-allowed;
        }
        &.cart {
          background-color: #e93323;
          color: #ffffff;
          &:disabled {
            border-color: #fab6b6;
            background-color: #fab6b6;
            color: #ebeef5;
          }
        }
        ~ button {
          margin-left: 18px;
        }
      }
    a {
        display: inline-block;
        background-color: #e93323;
        vertical-align: middle;
        line-height: 50px;
        text-align: center;
        &.btn {
          color: #ffffff;
        }
      }
    }
  }
  .detail-wrapper {
    margin-top: 50px;
    .presell_process{
      display: flex;
      align-items: center;
      background: #F7F7F7;
      height: 74px;
      margin-bottom: 20px;
      .process_item{
        display: flex;
        align-items: center;
        width: 28%;
        &:first-child{
          width: 16%;
          display: block;
          text-align: center;
          position: relative;
          padding-right: 30px;
          &::after{
            content: "";
            display: inline-block;
            width: 1px;
            height: 50px;
            background: #D9D9D9;
            position: absolute;
            top: -12px;
            right: 30px;
          }
          span{
            font-size: 20px;
            font-weight: bold;
            color: #E93323;
          }
        }
        img{
          width: 34px;
          height: 34px;
        }
        .process_step{
          margin-left: 10px;
          .step_name{
            color: #282828;
            font-size: 14px;
            font-weight: bold;
          }
          .step_time{
            font-size: 12px;
            color: #999999;
            margin-top: 3px;
          }
        }
      }
    }
    .detail-hd {
      background-color: #f7f7f7;
      .tab {
        flex: 1;
        min-width: 0;
      }
      .item {
        position: relative;
        height: 56px;
        padding-right: 30px;
        padding-left: 30px;
        font-size: 14px;
        color: #333333;
        cursor: pointer;
        &.on {
          background: url("~assets/images/checked.png") center top/100% 7px
            no-repeat;
          color: #e93323;
        }
        &::before {
          content: "";
          position: absolute;
          top: 18px;
          bottom: 18px;
          left: 0;
          width: 1px;
          border-left: 1px solid #d9d9d9;
        }
        &:first-child::before {
          display: none;
        }
        &:hover {
          color: #e93323;
        }
      }
      .qrcode-button {
        position: relative;
        width: 160px;
        height: 56px;
        background-color: #ededed;
        font-size: 14px;
        color: #333333;
        cursor: pointer;
        &:hover {
          .qrcode {
            display: block;
          }
        }
        .icon-saoma {
          margin-right: 6px;
          font-size: 13px;
          line-height: 1;
          color: #000000;
        }
        .icon-xiangxia2,
        .icon-xiangshang1 {
          margin-left: 10px;
          font-size: 10px;
          line-height: 12px;
          color: #d0d0d0;
        }
        .qrcode {
          position: absolute;
          z-index: 99;
          display: none;
          padding: 6px;
          background-color: #ffffff;
          border: 1px solid #ededed;
          margin-top: 6px;
          width: 160px;
          height: 160px;
          top: 50px;
          left: 0px;
          box-sizing: border-box;
        }
      }
    }
    .comment {
      .comment-hd {
        padding-top: 30px;
        padding-bottom: 30px;
        .rate {
          font-size: 0;
          span {
            font-size: 14px;
            color: #e93323;
            ~ span {
              margin-left: 5px;
            }
          }
        }
        .score {
          font-size: 14px;
          color: #7e7e7e;
          .cont {
            margin-left: 8px;
          }
          .iconfont {
            font-size: 12px;
            color: #e6e6e6;
            ~ .iconfont {
              margin-left: 5px;
            }
            &.on {
              color: #e93323;
            }
          }
        }
        .menu {
          margin-top: 20px;
          font-size: 0;
          .item {
            display: inline-block;
            width: 86px;
            height: 34px;
            border-radius: 2px;
            background-color: #f7f7f7;
            font-size: 14px;
            line-height: 34px;
            text-align: center;
            color: #282828;
            cursor: pointer;
            &:hover {
              color: #e93323;
            }
            &.on {
              background-color: #e93323;
              color: #ffffff;
            }
            ~ .item {
              margin-left: 14px;
            }
          }
        }
      }
      .comment-bd {
        > img {
          width: 200px;
          margin: 50px auto;
        }
        .item {
          padding-top: 20px;
          padding-bottom: 20px;
          .item-hd {
            .image {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              overflow: hidden;
            }
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
            .text {
              flex: 1;
              margin-left: 12px;
              font-size: 14px;
              color: #868686;
            }
            .name {
              margin-bottom: 4px;
              font-size: 16px;
              color: #282828;
            }
            .star {
              margin-left: 12px;
              font-size: 0;
            }
            .iconfont {
              font-size: 12px;
              color: #e6e6e6;
              &.on {
                color: #e93323;
              }
              ~ .iconfont {
                margin-left: 5px;
              }
            }
          }
          .item-bd {
            padding-bottom: 20px;
            border-bottom: 1px solid #e3e3e3;
            margin-left: 52px;
            font-size: 14px;
            color: #282828;
            > div {
              margin-top: 15px;
            }
            .image-wrapper {
              font-size: 0;
            }
            .image {
              display: inline-block;
              width: 54px;
              height: 54px;
              ~ .image {
                margin-left: 8px;
              }
              img {
                display: block;
                width: 100%;
                height: 100%;
              }
            }
            .reply {
              background-color: #f7f7f7;
              .item {
                padding: 7px 12px;
                font-size: 14px;
                color: #282828;
                span {
                  color: #e93323;
                }
              }
            }
          }
        }
      }
    }
  }
  .recom-section {
    align-self: flex-start;
    min-width: 210px;
    margin-left: 40px;
    .store-recommend{
      margin-top: 10px;
      padding: 0 20px;
      border: 1px solid #efefef;
      border-radius: 4px;
    }
    .title {
      height: 60px;
      font-size: 16px;
      line-height: 60px;
      text-align: center;
      color: #5a5a5a;
      span {
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 50%;
          right: 100%;
          margin-right: 18px;
          width: 35px;
          height: 1px;
          border-top: 1px solid #efefef;
          transform: translateY(-50%);
        }
        &::after {
          content: "";
          position: absolute;
          top: 50%;
          left: 100%;
          margin-left: 18px;
          width: 35px;
          height: 1px;
          border-top: 1px solid #efefef;
          transform: translateY(-50%);
        }
      }
    }
    .item {
      display: block;
      width: 170px;
      margin-bottom: 20px;
      .image {
        width: 170px;
        height: 170px;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .text {
        .name {
          margin-top: 10px;
          margin-bottom: 10px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 13px;
          color: #666666;
        }
        .money {
          font-weight: bold;
          font-size: 14px;
          color: #e93323;
          span {
            font-size: 18px;
          }
        }
        .sales {
          font-size: 12px;
          color: #888888;
        }
      }
    }
  }
  .store-favorites{
    margin-top: 14px;
    .collection{
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #333333;
      border: 1px solid #C8C8C8;
      border-radius: 2px;
      background: #fff;
      &.care{
        color: #fff;
        background-color: #e93323;
        border-color: #e93323;
      }
    }
  }
  .el-pagination {
    padding: 0;
    border: 1px solid #cccccc;  
  }
  .nothing {
    margin-top: 100px;
    font-size: 16px;
    text-align: center;
    color: #999999;
    img {
      margin: 0 auto;
    }
  }
}
.detail-wrapper .el-pagination ::v-deep button {
  width: 78px;
  height: 38px;
  padding: 0;
  font-size: 15px;
  color: #707070;
}
.detail-wrapper .el-pagination ::v-deep button.btn-prev {
  border-right: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep button.btn-next {
  border-left: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep li{
  width: 38px;
  height: 38px;
  padding: 0;
  font-weight: normal;
  font-size: 15px;
  line-height: 38px;
  color: #707070;
}
.detail-wrapper .el-pagination ::v-deep li~li{
  border-left: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep li.active {
  background-color: #e93323;
  color: #ffffff;
}
.detail-wrapper .qrcode-button .qrcode ::v-deep img{
  display: block;
  width: 100%;
  height: 100%;
}
.detail-wrapper .detail-bd .detail-html ::v-deep div {
  width: 100% !important;
}
.detail-wrapper .detail-bd .detail-html ::v-deep img {
  display: block;
  width: 100% !important;
}
</style>
