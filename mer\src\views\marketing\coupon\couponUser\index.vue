<template>
  <div class="divBox">
    <div class="selCard">
      <el-form :model="tableFromIssue" ref="searchForm" size="small" label-width="85px"  :inline="true">
        <el-form-item :label="$t('使用状态：')" prop="status">
          <el-select v-model="tableFromIssue.status" :placeholder="$t('请选择状态')" @change="getIssueList" clearable class="selWidth">
            <el-option :label="$t('全部')" value="" />
            <el-option :label="$t('已使用')" value="1" />
            <el-option :label="$t('未使用')" value="0" />
            <el-option :label="$t('已过期')" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('领取人：')" prop="username">
          <el-input v-model="tableFromIssue.username" @keyup.enter.native="getIssueList" :placeholder="$t('请输入领取人')" clearable class="selWidth" />
        </el-form-item>
        <el-form-item :label="$t('优惠劵：')" prop="coupon_id">
          <el-input v-model="tableFromIssue.coupon_id" @keyup.enter.native="getIssueList" :placeholder="$t('请输入优惠劵ID')" clearable class="selWidth" />
        </el-form-item>
        <el-form-item :label="$t('获取方式：')" prop="type">
          <el-select v-model="tableFromIssue.type" :placeholder="$t('请选择状态')" @change="getIssueList" clearable class="selWidth">
            <el-option :label="$t('全部')" value="" />
            <el-option :label="$t('自己领取')" value="receive" />
            <el-option :label="$t('后台发送')" value="send" />
            <el-option :label="$t('新人')" value="new" />
            <el-option :label="$t('买赠送')" value="buy" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="getIssueList">{{ $t('搜索') }}</el-button>
          <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
        </el-form-item>
      </el-form>
    </div>
    <el-card class="mt14">
      <el-table
        v-loading="Loading"
        :data="issueData.data"
        size="small"
      >
        <el-table-column
          prop="coupon_id"
          label="ID"
          min-width="80"
        />
        <el-table-column
          prop="coupon_title"
          :label="$t('优惠券名称')"
          min-width="150"
        />
        <el-table-column
          :label="$t('领取人')"
          min-width="200"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.user">{{scope.row.user.nickname | filterEmpty}}</span>
            <span v-else>{{ $t('未知') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="coupon_price"
          :label="$t('面值')"
          min-width="100"
        />
        <el-table-column
          prop="use_min_price"
          :label="$t('最低消费额')"
          min-width="120"
        />
        <el-table-column
          prop="start_time"
          :label="$t('开始使用时间')"
          min-width="150"
        />
        <el-table-column
          prop="end_time"
          :label="$t('结束使用时间')"
          min-width="150"
        />
        <el-table-column
          :label="$t('获取方式')"
          min-width="150"
        >
          <template slot-scope="scope">
            <span>{{scope.row.type | failFilter}}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('状态')"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{scope.row.status | statusFilter}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFromIssue.limit"
          :current-page="tableFromIssue.page"
          layout="total, prev, pager, next, jumper"
          :total="issueData.total"
          @size-change="handleSizeChangeIssue"
          @current-change="pageChangeIssue"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { issueApi } from '@/api/marketing'
import { roterPre } from '@/settings'
export default {
  name: 'CouponUser',
  filters: {
    failFilter(status) {
      const statusMap = {
        'receive': '自己领取',
        'send': '后台发送',
        'give': '满赠',
        'new': '新人',
        'buy': '买赠送'
      }
      return statusMap[status]
    },
    statusFilter(status) {
      const statusMap = {
        0: this.$t(this.$t('未使用')),
        1: this.$t(this.$t('已使用')),
        2: this.$t(this.$t('已过期'))
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      Loading: false,
      roterPre: roterPre,
      tableFromIssue: {
        page: 1,
        limit: 10,
        coupon_id: '',
        status: '',
        username: '',
        type: ''
      },
      issueData: {
        data: [],
        total: 0
      }
    }
  },
  mounted() {
    this.getIssueList()
  },
  methods: {
    /**
     * 重置搜索表单
     */
    searchReset(){
      this.$refs.searchForm.resetFields()
      this.getIssueList(1)
    },
    // 获取优惠券发放列表
    getIssueList() {
      this.Loading = true
      issueApi(this.tableFromIssue).then(res => {
        this.issueData.data = res.data.list
        this.issueData.total = res.data.count
        this.Loading = false
      }).catch(res => {
        this.Loading = false
        this.$message.error(res.message)
      })
    },
    // 页码改变事件
    pageChangeIssue(page) {
      this.tableFromIssue.page = page
      this.getIssueList()
    },
    // 每页显示数量改变事件
    handleSizeChangeIssue(val) {
      this.tableFromIssue.limit = val
      this.getIssueList()
    }
  }
}
</script>
<style scoped lang="scss">
</style>
