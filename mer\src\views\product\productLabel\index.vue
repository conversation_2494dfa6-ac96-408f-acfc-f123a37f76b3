<template>
  <div class="divBox">
    <el-card class="box-card dataBox">
      <div class="mb20">
        <el-button size="small" type="primary" @click="onAdd">{{ $t('添加商品标签') }}</el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
      >
        <el-table-column prop="label_name" :label="$t('标签名称')" min-width="60" />
        <el-table-column prop="info" :label="$t('标签说明')" min-width="150" />
        <el-table-column prop="sort" :label="$t('排序')" min-width="50" />
        <el-table-column prop="status" :label="$t('是否显示')" min-width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              :width="55"
              active-text="显示"
              inactive-text="隐藏"
              @change="onchangeIsShow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="create_time" :label="$t('创建时间')" min-width="150" />
        <el-table-column :label="$t('操作')" min-width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="onEdit(scope.row.product_label_id)">{{ $t('编辑') }}</el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.product_label_id, scope.$index)"
            >{{ $t('删除') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {
  labelListApi,
  labelCreateApi,
  labelUpdateApi,
  labelDeleteApi,
  labelStatusApi } from '@/api/product'
export default {
  name: 'LabelList',
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20
      },
      tableData: {
        data: [],
        total: 0
      },
      listLoading: true
    }
  },
  mounted() {
    this.getList('')
  },
  methods: {
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableFrom.page = num || this.tableFrom.page
      labelListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList('')
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList('')
    },
    // 添加
    onAdd() {
      this.$modalForm(labelCreateApi()).then(() => this.getList(''))
    },
    // 编辑
    onEdit(id) {
      this.$modalForm(labelUpdateApi(id)).then(() => this.getList(''))
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure('删除该标签').then(() => {
        labelDeleteApi(id)
          .then(({ message }) => {
            this.$message.success(message)
            this.getList('')
          })
          .catch(({ message }) => {
            this.$message.error(message)
          })
      })
    },
    onchangeIsShow(row) {
      labelStatusApi(row.product_label_id, row.status)
        .then(({ message }) => {
          this.$message.success(message)
          this.getList('')
        })
        .catch(({ message }) => {
          this.$message.error(message)
        })
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/styles/form.scss';
</style>