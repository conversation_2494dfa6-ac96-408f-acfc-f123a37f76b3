<template>
    <div class="address-wrapper">
      <div class="user-com-title">
        地址管理
      </div>
      <div class="address-content">
        <ul class="clearfix">
          <li v-for="(item,index) in list" :index="index">
            <div class="name line1">{{item.real_name}}</div>
            <div class="phone">{{item.phone}}</div>
            <div class="text line4">{{item.province}}{{item.city}}{{item.country}}{{item.district ? item.district : ''}}{{item.detail}}</div>
            <div class="edit-box">
              <span @click="bindDefault(item)" v-if="item.is_default !=1">设为默认地址</span>
              <span @click="edit(item)">修改</span>
              <span @click="bingDelete(item,index)">删除</span>
            </div>
            <div class="moren" v-if="item.is_default ==1">默认</div>
          </li>
          <li class="addbox" @click="addAddress">
            <div class="box">
              <img src="~assets/images/add.png" alt="">
              <p>添加新地址</p>
            </div>
          </li>
        </ul>
      </div>
      <!-- 添加地址弹窗 -->
      <el-dialog
        title="添加收货地址"
        :visible.sync="dialogVisible"
        width="700px"
        :before-close="handleClose"
        >
          <div class="form-box">
            <div class="input-item" style="width: 48%;display:inline-block">
              <el-input v-model="formData.name" maxlength="25" placeholder="姓名"></el-input>
            </div>
            <div class="input-item" style="width: 48%;display:inline-block;margin-left: 3%">
              <el-input v-model="formData.phone" placeholder="手机号"></el-input>
            </div>
            <div class="input-item text-wrapper">
              <p @click="bindAdd(false)" v-if="!cityData.province.name">请选择省/市/区/街道</p>
              <p @click="bindAdd(true)" v-if="cityData.province.name" style="color: #333">
                <span v-if="cityData.province.name">{{cityData.province.name}}</span>
                <span v-if="cityData.city.name">/{{cityData.city.name}}</span>
                <span v-if="cityData.county.name">/{{cityData.county.name}}</span>
                <span v-if="cityData.district.name">/{{cityData.district.name}}</span>
              </p>
              <div class="select-wrapper" v-if="isShowSelect">
                <div class="title-box" v-if="!cityData.province.id">选择省/自治区</div>
                <div class="title-box" v-if="cityData.step == 2">
                  <span>{{cityData.province.name}}</span>选择市
                </div>
                <div class="title-box" v-if="cityData.step == 3">
                  <span>{{cityData.county.name}}</span>
                  <span>{{cityData.city.name}}</span>选择区县
                </div>
                <div class="title-box" v-if="cityData.step == 4 && !stepStop">
                  <span>{{cityData.city.name}}</span>
                  <span>{{cityData.county.name}}</span>请选择配送区域
                </div>
                <div class="label-txt">
                  <span v-for="(item,index) in cityData.list" :key="index" @click.stop="bindCity(item)" :class="{on:cityData.pid == item.id}">{{item.name}}</span>
                </div>
              </div>
            </div>
            <div class="input-item">
              <el-input type="textarea" rows="3" v-model="formData.con" placeholder="详细地址"></el-input>
            </div>
            <div class="input-item">
              <el-checkbox v-model="formData.checked">设为默认</el-checkbox>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="bindSubmit">确 定</el-button>
          </span>
      </el-dialog>
    </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message, MessageBox} from 'element-ui';
export default {
      name: "addressList",
      auth: "guest",
      data(){
        return{
          list:[],
          page:1,
          limit:20,
          isShowSelect:false,
          dialogVisible:false,
          stepStop: false,
          editId:0,
          formData:{
            name:'',
            phone:'',
            con:'',
            checked:false
          },
          cityData:{
            pid:0,
            step:1,
            list:[],
            con:'',
            province:{},
            city:{},
            county: {},
            district:{}
          },
          selectedIndex: -1,
          selectedArr: []
        }
      },
  computed:{
    activeId(){
      return this.selectedIndex == -1 ? 0 : this.selectedArr[this.selectedIndex].id
    },
  },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "地址管理-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
      },
      mounted() {
        this.getList()
      },
      methods:{
        getList(){
          this.$axios.get('/api/user/address/lst').then(res=>{
            this.list = res.data.list
          })
        },
        addAddress(){
          this.editId = 0
          this.dialogVisible = true
          if(this.cityData.step !== 3) this.getCityList(0,null)
        },
        handleClose(){
          this.formReset()
          this.dialogVisible = false
          this.isShowSelect = false
        },
        bindAdd(isReset){
          if(isReset){this.cityData.step = 1;this.stepStop = false}
          this.isShowSelect = !this.isShowSelect
          if(this.cityData.step == 4 || this.stepStop){
            return
          }else{
            this.cityData.city = {}
            this.cityData.district ={}
            this.cityData.province ={}
            this.cityData.county ={}
            this.getCityList(0,null)
          }
        },
        getCityList(pid,fun){
          this.cityData.list = []
          this.$axios.get('/api/v2/system/city/lst/'+pid).then(res=>{
            this.cityData.list= res.data
            fun && fun()
          })
        },
        // 选择城市
        bindCity(item){
          let that = this;
          if(that.cityData.step == 4){
            that.cityData.district = item
            that.selectedArr.push(item);
            that.isShowSelect = false
          }else {
            if(that.cityData.step == 1){
              that.cityData.province = item
              that.getCityList(item.id,null)
              that.selectedArr = [item];
              that.cityData.step++
              return
            }else{
              if(that.cityData.step == 2){
                that.cityData.city = item
                that.getCityList(item.id,null)
                that.cityData.step++
                that.selectedArr.push(item);
                return
              }
              if(that.cityData.step == 3){
                that.cityData.county = item
                that.selectedArr.push(item);
                that.cityData.step++
                that.getCityList(item.id,function(){
                  if(that.cityData.list && that.cityData.list.length){
                    that.stepStop = false
                    return
                  }else{
                    that.stepStop = true
                    that.isShowSelect = false
                    return
                  }
                })
              }
            }
          }
        },
        bindSubmit(){
          if(!this.formData.name){
            return Message.error('请填写姓名')
          }
          if(!this.formData.phone || !/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formData.phone)){
            return Message.error('请填写正确的手机号码')
          }
          if(!this.cityData.province.name){
            return Message.error('请选择省市区')
          }
          if(!this.formData.con){
            return Message.error('请填写详细地址')
          }
          this.$axios.post('/api/user/address/create',{
            area: this.selectedArr,
            is_default:this.formData.checked?1:0,
            real_name:this.formData.name,
            phone:this.formData.phone,
            detail:this.formData.con,
            address_id:this.editId
          }).then(res=>{
            this.dialogVisible = false
            this.getList()
            this.formReset()
            return Message.success('添加成功')
          }).catch(err=>{
            return Message.error(err);
          })
        },
        // 设为默认
        bindDefault(item){
          this.$axios.post('/api/user/address/update/'+item.address_id).then(res=>{
            this.getList()
            return Message.success('设置成功')
          })
        },
        // 删除
        bingDelete(item,index){
          MessageBox.confirm('确定删除该地址吗','提示').then(res=>{
            this.$axios.post('/api/user/address/delete/'+item.address_id).then(data=>{
              this.getList()
              return Message.success('删除成功')
            }).catch(err=>{
              return Message.error(err);
            })
          })
        },
        // 编辑
        edit(item){
          this.dialogVisible = true
          this.$axios.get('/api/user/address/detail/'+item.address_id).then(res=>{
            let data = res.data
            this.formData.name = data.real_name
            this.formData.phone = data.phone
            this.formData.con = data.detail
            this.selectedArr = data.area
            this.formData.checked = data.is_default?true:false
            this.cityData.province.name = data.area && data.area.length && data.area[0] ? data.area[0].name : ''
            this.cityData.city.name = data.area && data.area.length && data.area[1] ? data.area[1].name : ''
            this.cityData.county.name = data.area && data.area.length && data.area[2] ? data.area[2].name : ''
            this.cityData.district.name = data.area && data.area.length && data.area[3] ? data.area[3].name : ''
            this.editId = data.address_id
            this.cityData.city.city_id = data.city_id
          })
        },
        // 表单重置
        formReset(){
          this.formData.name = ''
          this.formData.phone = ''
          this.formData.con = ''
          this.formData.area = ''
          this.formData.checked = false
          this.cityData.province = {}
          this.cityData.city = {}
          this.cityData.district = {}
          this.cityData.step = 1
          this.cityData.pid = 0
        }
      }
    }
</script>

<style lang="scss" scoped>
.address-wrapper{
  .address-content{
    li{
      position: relative;
      float: left;
      width: 280px;
      min-height: 200px;
      margin-top: 30px;
      margin-right: 30px;
      padding: 22px 27px;
      border: 1px solid #EAEAEA;
      &:nth-child(3n){
        margin-right: 0;
      }
      .moren{
        position: absolute;
        right: 0;
        top: 0;
        width: 56px;
        height: 23px;
        line-height: 23px;
        text-align: center;
        color: #fff;
        background: #E93323;
      }
      .name{
        color: #282828;
        font-size: 16px;
      }
      .phone{
        margin-top: 14px;
        margin-bottom: 4px;
        font-size: 12px;
      }
      .text{
        color: #999999;
        font-size: 14px;
        height: 75px;
      }
      .edit-box{
        opacity: 0;
        position: absolute;
        right: 14px;
        bottom: 12px;
        color: #E93323;
        span{
          margin-left: 5px;
          cursor: pointer;
        }
      }
      &.addbox{
        .box{
          position: absolute;
          left: 0;
          top: 50%;
          margin-top: -25px;
          text-align: center;
          width: 100%;
          color: #C8C8C8;
          font-size: 14px;
          img{
            display: inline-block;
            width: 27px;
            height: 27px;
          }
        }
      }
      &:hover{
        .edit-box{
          opacity: 1;
          transition: all .6s ease;
        }
      }
    }
  }
}
.input-item{
  margin-bottom: 20px;
}
.text-wrapper{
  position: relative;
  height: 40px;
  line-height: 40px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  box-sizing: border-box;
  border-radius: 4px;
  color: #cfcfcf;
  .select-wrapper{
    z-index: 10;
    position: absolute;
    left: 0;
    top: 45px;
    width: 100%;
    padding:0 15px;
    background: #fff;
    border: 1px solid #E93323;
    border-radius: 4px;
    line-height: 2;
    .title-box{
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #EFEFEF;
      color: #E93323;
      font-size: 14px;
      span{
        margin-right: 8px;
        color: #666666;
      }
    }
    .label-txt{
      margin: 8px 0 18px;
      color: #666666;
      font-size: 14px;
      span{
        margin-right: 10px;
        cursor: pointer;
        &.on{
          color: #E93323;
        }
      }
    }
  }
}
</style>
