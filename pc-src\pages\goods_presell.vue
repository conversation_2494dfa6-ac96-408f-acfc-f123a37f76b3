<template>
  <div class="presell">
    <div class="title-section">
      <img src="@/assets/images/presell-title.png" />
    </div>
    <div v-swiper:navSwiper="swiperOption">
      <div class="swiper-wrapper">
        <div
          v-for="item in presellStatus"
          :key="item.type"
          :class="item.type == type ? 'on' : 'slide'+item.type"
          class="acea-row row-center-wrapper swiper-slide"
          @click="getCurrentGoods(item.type)">
          {{item.state}}
        </div>
      </div>
    </div>
    <div class="goods-section">
      <ul v-if="goodsList.length">
        <li v-for="item in goodsList" :key="item.id">
          <nuxt-link
              :to="{
              path: '/goods_presell_detail/'+item.product_presell_id
            }"
          >
            <div class="image">
              <img :src="item.product.image" />
            </div>
            <div class="text">
              <div class="name">{{ item.store_name }}</div>
              <div class="group">
                <div class="progress">
									<div class='presell_price'>
										<span class="presell_text">预售价</span>
										<span class="price">¥ <span>{{ item.price }}</span></span>
									</div>
									<div v-if="type != 0" class='order_btn'>{{ type === 1  ? '立即预定' : '已结束' }}</div>
									<div v-else class="unStartBtn">
										<span>开售时间</span>
										<div>{{ new Date(item.start_time.replace(/-/g,"/")).getMonth()+1 }}月{{ new Date(item.start_time.replace(/-/g,"/")).getDate() }}日{{ new Date(item.start_time.replace(/-/g,"/")).getHours()<10?'0'+ 
										new Date(item.start_time.replace(/-/g,"/")).getHours():new Date(item.start_time.replace(/-/g,"/")).getHours() || '00'}}:{{ new Date(item.start_time.replace(/-/g,"/")).getMinutes()<10?"0" + new Date(item.start_time.replace(/-/g,"/")).getMinutes():
										new Date(item.start_time.replace(/-/g,"/")).getMinutes() || '00'}}</div>
									</div>
								</div>
              </div>
            </div>
          </nuxt-link>
        </li>
      </ul>
      <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
      <div v-else class="nothing">
        <img src="@/assets/images/noGoods.png" />
        暂无预售商品，去看点别的吧
      </div>
    </div>

  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
export default {
  auth: false,
  components: { countDown },
  data() {
    return {
      swiperOption: {
        slidesPerView: "auto"
      },
      page: 1,
      limit: 16,
      type: 1,
      total: '',
      goodsList: [],
      presellStatus: [
        {state: '未开始', type: 0},
        {state: '进行中', type: 1},
        {state: '已结束', type: 2}
      ],
      finished: false,
      currentTimeId: "",
      currentStopTime: "",
      currentTimeState: "",
      currentTimeStatus: "",
      seckillTime: [],
      start_time: '',
      stop_time: ''
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "预售列表-"+this.$store.state.titleCon
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getGoodsList()
    });
  },
  methods: {
    getGoodsList() {
      this.$axios
        .get(`/api/store/product/presell/lst`, {
          params: {
            page: this.page,
            limit: this.limit,
            type: this.type
          }
        })
        .then(res => {
          let data = res.data.list;
          this.total = res.data.count;
          this.goodsList = data;
          this.finished = data.length < this.page.limit;
          this.page++;
        })
        .catch(err => {
          this.$message.error(err);
        });
    },
    // 顶部筛选
    getCurrentGoods(type){
      this.page = 1;
      this.type = type;
      this.getGoodsList();
    },
    // 分页点击
    bindPageCur(data){
      this.page = data
      this.getGoodsList();
    }
  }
};
</script>

<style lang="scss" scoped>
.presell {
  min-height: 330px;
  background: url("~assets/images/presell-back.png") center top/100% no-repeat;
  .title-section {
    padding-top: 40px;
    padding-bottom: 40px;
    text-align: center;
    img {
      display: inline-block;
      width: 200px;
      height: 48px;
      vertical-align: middle;
    }
  }
  .goods-section {
    width: 1200px;
    margin: 20px auto;
    ul {
      margin-bottom: -16px;
      margin-left: -16px;
      &::after {
        content: "";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      li {
        float: left;
        width: 288px;
        margin-bottom: 16px;
        margin-left: 16px;
        background: #ffffff;
      }
      a {
        display: block;
        padding: 24px;
      }
      .image {
        width: 240px;
        height: 240px;
        overflow: hidden;
        img {
          display: block;
          width: 100%;
          height: 100%;
          transition: 1s;
        }
      }
      .text {
        margin-top: 18px;
        .name {
          height: 40px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          font-size: 14px;
          line-height: 20px;
          color: #282828;
        }
        .group {
          display: flex;
          align-items: center;
        }
        .progress {
          width: 240px;
          height: 47px;
          background-image: url("~assets/images/presell_price_bg.png");
          background-size: 100%;
          margin-top: 16px;
          .presell_price{
            float: left;
            width: 50%;
            text-align: center;
            line-height: 15px;
            padding: 8px 0;
            .presell_text{
              display: block;
              color: #E93323;
              font-size: 12px;
            }
            .price{
              font-size: 12px;
              color: #E93323;
              margin-top: 5px;
              display: block;
              span{
                font-size: 20px;
                font-weight: bold;
              }
            }
          }
          .order_btn{
            float: left;
            width: 50%;
            text-align: center;
            color: #FFFFFF;
            font-size: 16px;
            line-height: 47px;

          }
          .unStartBtn{
            float: left;
            width: 50%;
            text-align: center;
            color: #FFFFFF;
            font-size: 14px;
            padding: 6px 0;
            span{
              font-size: 12px;
            }
          }
        } 
      }
    }
  }
  .swiper-container {
    width: 1200px;
    background: #ffffff;
    .swiper-slide {
      width: 417px;
      height: 80px;
      padding-right: 18px;
      margin-right: -18px;
      font-size: 24px;
      color: #282828;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &.row-column {
        div {
          &:first-child {
            width: auto;
            height: auto;
            margin-right: 0;
            margin-bottom: 4px;
            background: none;
            font-weight: bold;
            font-size: 18px;
            line-height: normal;
            text-align: inherit;
          }
        }
      }
       &.slide0 {
        color: #666666;
      }
      &.on {
        background: url("~assets/images/presell-slide-back.png") center/100%
          100% no-repeat;
        color: #ffffff;
      }
      > div {
        &:first-child {
          width: 76px;
          height: 60px;
          margin-right: 16px;
          background: url("~assets/images/seckill-slide-clock.png") center/60px
            60px no-repeat;
          font-weight: bold;
          font-size: 24px;
          line-height: 60px;
          text-align: center;
        }
      }
    }
  }
  .nothing {
    padding-top: 250px;
    padding-bottom: 150px;
    font-size: 16px;
    text-align: center;
    color: #969696;
    img {
      margin: 0 auto;
    }
  }
}
</style>
