<template>
  <div class="user-order-list">
    <div class="user-com-tab">
      <span class="item" :class='orderStatus==="" ? "on": ""' @click="bindTab('')">全部发票</span>
      <span class="item" :class='orderStatus===1 ? "on": ""' @click="bindTab(1)">已开票</span>
      <span class="item" :class='orderStatus===0 ? "on": ""' @click="bindTab(0)">未开票</span>
    </div>
    <div v-if="orderList.length > 0" class="order-list">
     <div class="invoice_header bg_gray">
        <div class="invoice_list">商品信息</div>
        <div class="invoice_list">发票类型</div>
        <div class="invoice_list">状态</div>
        <div class="invoice_list">操作</div>
     </div>
      <ul>
        <li v-for="(item,index) in orderList" :key="index">
          <div class="bd">
            <div class="order-txt">
              订单日期: {{item.create_time}}
            </div>
            <div class="content">
              <div class="goods-item">
                <div class="invoice_list acea-row">
                  <div class="invoice_img">
                    <div class="img-box" v-if="item.storeOrder.orderProduct[0].cart_info.productAttr.image"><img :src='item.storeOrder.orderProduct[0].cart_info.productAttr.image' alt=""></div>
                    <div class="img-box" v-else><img :src="item.storeOrder.orderProduct[0].cart_info.product.image" alt=""></div>
                      <div class="info-txt">
                        <div class="title line2">{{item.storeOrder.orderProduct[0].cart_info.product.store_name}}</div>                     
                        <div class="price">
                        ¥ {{ item.storeOrder.orderProduct[0].cart_info.productAttr.price || item.storeOrder.orderProduct[0].cart_info.product.price }}
                        <span class="num">x{{ item.storeOrder.orderProduct[0].product_num }}</span>
                        </div>
                        
                      </div>                      
                    </div>
                  </div>
                  <div class="invoice_list">
                    <div class="invoice_item colorE">{{ item.receipt_info.receipt_type == 1 ? '普通发票' : '专用发票' }}</div>
                  </div>
                  <div class="invoice_list">
                    <div class="invoice_item" :class="'color'+item.status">{{item.status | filterTxt}}</div>
                  </div>
                  <div class="invoice_list">
                    <div class="invoice_item el_btn" @click="goDetail(item)">发票详情</div>
                  </div>            
              </div>
              
            </div>
          </div>
          
        </li>
      </ul>
    </div>
    <div class="pages-box" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="bindPageCur"
          :total="total">
        </el-pagination>
      </div>
      <div class="empty-box" v-if="orderList.length == 0">
        <img src="~assets/images/noorder.png" alt="">
        <p>亲，暂无发票信息哟~</p>
      </div>
        <!-- 发票信息弹窗 -->
    <el-dialog
      title="发票信息"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose">
      <div class="invoice_data_container">
       <div class="demo-ruleForm">
        <div class="list">
          <div class="demo_item">
          <label>开票状态：</label>
          <span :class="'color'+orderData.status">{{orderData.status == 1 ? '已开票' : '未开票'}}</span>
          </div>
          <div v-if="orderData.status == 1" class="demo_item">
            <label>开票时间：</label>
            <span>{{orderData.status_time}}</span>
          </div>
          <div class="demo_item">
            <label>申请时间：</label>
            <span>{{orderData.create_time}}</span>
          </div>
        </div>
        
        <div class="list">
          <div class="demo_item">
            <label>发票类型：</label>
            <span>{{receipt_info.receipt_type == 1 ? '普通发票' : '专用发票'}}</span>
          </div>
          <div class="demo_item">
            <label>发票抬头类型：</label> 
            <span>{{receipt_info.receipt_title_type == 1 ? '个人' : '企业'}}</span>        
          </div>
          <div class="demo_item" v-if="receipt_info.receipt_title_type == 2">
            <label>单位税号：</label>
            <span>{{receipt_info.duty_paragraph}}</span>
          </div> 
          <div class="demo_item">
            <label>发票抬头：</label>
            <span>{{receipt_info.receipt_title}}</span>
          </div>
          <div v-show="receipt_info.receipt_title_type == '2' && receipt_info.receipt_type == '2'">
            <div class="demo_item">
              <label>开户银行：</label>
              <span>{{receipt_info.bank_name}}</span>
            </div> 
            <div class="demo_item">
              <label>银行账号：</label>
              <span>{{receipt_info.bank_code}}</span>
            </div>
            <div class="demo_item">
              <label>企业地址：</label>
              <span>{{receipt_info.address}}</span>
            </div> 
            <div class="demo_item">
              <label>企业电话：</label>
              <span>{{receipt_info.tel}}</span>
            </div>
        </div>
        
        </div>    
      </div>
      </div>
       <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="width: 180px;" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message, MessageBox} from "element-ui";
export default {
    name: "orderList",
    auth: "guest",
    scrollToTop: true,
    filters:{
			filterTxt(val){
				const obj = {
					0:'未开票',
					1:'已开票',
					10:'未寄出'
				}
				return obj[val]
			}
		},
    data() {
      return {
        dialogVisible: false,
        tabCur: '',
        orderList: [
        ],
        orderData: {},
        receipt_info: {},
        orderStatus: "",
        total:0,
        page: 1,
        limit: 10,
      }
    },
    fetch({ store }) {
      store.commit("isBanner", false);
      store.commit("isHeader", true);
      store.commit("isFooter", true);
    },
    head() {
      return {
        title: "我的发票-"+this.$store.state.titleCon
      }
    },
    beforeMount(){
    },
    mounted() {
      Promise.all([this.getList()])
    },
    methods: {
      // 选项卡
      bindTab(status){
        this.orderStatus = status;
        this.page = 1
        this.$set(this, 'orderList', []);
        this.getList();   
      },
      // 获取订单发票列表
      getList() {
        this.$axios.get('/api/user/receipt/order',{
          params:{
            page:this.page,
            limit:this.limit,
            status: this.orderStatus
          }
        }).then(res => {
          this.orderList = res.data.list
          this.total = res.data.count
        })
      },
      // 查看详情
      goDetail(item){
        this.dialogVisible = true;
         this.$axios.get('/api/user/receipt/order/'+item.order_receipt_id).then(res => {
          this.orderData = res.data
          this.$set(this, 'receipt_info', res.data.receipt_info);

        })
      },
      handleClose() {
        this.dialogVisible = false;
      },
      // 分页点击
      bindPageCur(data){
        this.page = data
        this.getList()  
      }
     
    }
  }
</script>

<style lang="scss" scoped>
  .user-com-tab {
    .item {
      padding: 0 10px;
    }
  }
  .user-order-list {
    ul{
       margin-top: 20px;
    }
   
    li {
      position: relative;
      .refund-icon{
        position: absolute;
        right: 50px;
        top: 40px;
      }
      .bd {
        .order-txt {
          color: #282828;
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          background-color: #EFEFEF;
          padding: 0 20px
        }
        .content {   
          .invoice_list{
              position: relative;
              &::after{
                content: "";
                display: block;
                height: 112px;
                width: 1px;
                background: #EFEFEF;
                position: absolute;
                top: -48px;
                right: 0;
              }
              &:first-child{
                &::after{
                  top: -20px;
                }
              }
              &:last-child{
                &::after{
                  display: none;
                }
              }
              .colorE{
                color: #999999;
              }
              .color0{
                color: #E93323;
              }
              .color1{
                color: #71B247;
              }
              .el_btn{
                display: inline-block;
                color: #E93323;
                line-height: 28px;
                text-align: center;
                border: 1px solid #E93323;
                width: 98px;
                cursor: pointer;
              }
            }
            
          .goods-item {
            display: flex;
            position: relative;
            margin-bottom: 20px;
            align-items: center;
            padding: 20px 0;
            border: 1px solid #EFEFEF;
            
            .invoice_img{
              padding-left: 20px;
              display: flex;
            }
            .img-box {
              width: 70px;
              height: 70px;
              img {
                display: block;
                width: 100%;
                height: 100%;
              }
            }
            .info-txt {
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: center;
              max-width: 200px;
              margin-left: 24px;
              font-size: 14px;

              .price {
                margin-top: 15px;
                font-size: 14px;
              }
              .num {
               font-size: 12px;
               margin-left: 5px;
               display: inline-block;
                color: #999999;
              }
            }
          }
        }
      }
    }
  }
  .bg_gray{
    background-color: #EFEFEF;
  }
  .order-list{
    margin-top: 30px;
    padding-right: 20px;
;
  }
  .invoice_header{
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
     text-align: center;
  }
  .invoice_list{
    width: 15%;
   text-align: center;
    &:first-child{
      width: 55%;
    }
  }
  .pages-box{
    margin-top: 30px;
    text-align: right;
  }
  .invoice_data_container{
    padding: 0 20px;
    .list{
      margin-bottom: 30px;
      &:last-child{
        margin-bottom: 0;
      }
    }
    .demo_item{
      color: #282828;
      margin-bottom: 10px;
      label{
        width: 80px;
        margin-right: 10px;
      }
      .color0{
        color: #E93323;
      }
      .color1{
        color: #71B247;
      }
    }
  }
  .dialog-footer{
    display: block;
    text-align: center;
    margin-top: 20px;
  }
  ::v-deep .el-dialog__body{
    border-bottom: 1px solid #EFEFEF;
  }
</style>
