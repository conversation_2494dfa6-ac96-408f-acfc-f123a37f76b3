// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const orderRouter =
{
  path: `${roterPre}/order`,
  name: 'order',
  meta: {
    icon: 'dashboard',
    title: leaveuKeyTerms['订单']
  },
  alwaysShow: true,
  component: Layout,
  redirect: 'noRedirect',
  children: [
    {
      path: 'list',
      name: 'OrderList',
      meta: {
        title: leaveuKeyTerms['订单管理']
      },
      component: () => import('@/views/order/index')
    },
    {
      path: 'refund',
      name: 'OrderRefund',
      meta: {
        title: leaveuKeyTerms['退款单']
      },
      component: () => import('@/views/order/orderRefund/index')
    },
    {
      path: 'invoice',
      name: 'OrderInvoice ',
      meta: {
        title: leaveuKeyTerms['发票管理']
      },
      component: () => import('@/views/order/orderInvoice/index')
    },
    {
      path: 'cancellation',
      name: 'OrderCancellation',
      meta: {
        title: leaveuKeyTerms['核销订单']
      },
      component: () => import('@/views/order/orderCancellate/index')
    },
    {
      path: 'print',
      name: `OrderPrint`,
      meta: {
        title: leaveuKeyTerms['配货单打印'],
        fullScreen: true,
      },
      component: () => import('@/views/order/print/index'),
    },
    {
      path: 'customer',
      name: `CustomerOrder`,
      meta: {
        title: leaveuKeyTerms['代客下单'],
        fullScreen: true,
      },
      component: () => import('@/views/customerOrder/index'),
    },
    {
      path: 'reservation',
      name: `Reservation`,
      meta: {
        title: leaveuKeyTerms['预约服务'],
        
      },
      component: () => import('@/views/order/orderReservation/index'),
    },
  ]
}

export default orderRouter
