<template>
    <div class="upload_img" v-if="configData">
        <div class="header">{{configData.header}}</div>
        <div class="title">{{ $t(configData.title) }}</div>
        <div class="box" @click="modalPicTap">
            <img :src="configData.url" alt="" v-if="configData.url">
            <div class="upload-box" v-else><i class="iconfont iconjiahao" style="font-size: 30px" />{{ $t('添加图片') }}</div>
            <span class="iconfont-diy icondel_1" @click.stop="bindDelete" v-if="configData.url && configData.type"></span>
        </div>
        <div>
            <el-dialog :visible.sync="modalPic" width="950px" :title="configData.header?configData.header:'上传图片'">
                <uploadPictures :isChoice="isChoice" @getPic="getPic" :gridBtn="gridBtn" :gridPic="gridPic" v-if="modalPic"></uploadPictures>
            </el-dialog>
        </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { mapState } from 'vuex'
import uploadPictures from '@/components/uploadPicture';
export default {
    name: 'c_upload_img',
    components: {
        uploadPictures
    },
    computed: {
        ...mapState({
            tabVal: state => state.mobildConfig.searchConfig.data.tabVal
        })
    },
    props: {
        configObj: {
            type: Object
        },
        configNme: {
            type: String
        }
    },
    data () {
        return {
            defaultList: [
                {
                    'name': 'a42bdcc1178e62b4694c830f028db5c0',
                    'url': 'https://o5wwk8baw.qnssl.com/a42bdcc1178e62b4694c830f028db5c0/avatar'
                },
                {
                    'name': 'bc7521e033abdd1e92222d733590f104',
                    'url': 'https://o5wwk8baw.qnssl.com/bc7521e033abdd1e92222d733590f104/avatar'
                }
            ],
            defaults: {},
            configData: {},
            modalPic: false,
            isChoice: '单选',
            gridBtn: {
                xl: 4,
                lg: 8,
                md: 8,
                sm: 8,
                xs: 8
            },
            gridPic: {
                xl: 6,
                lg: 8,
                md: 12,
                sm: 12,
                xs: 12
            },
            activeIndex: 0
        }
    },
    watch: {
        configObj: {
            handler (nVal, oVal) {
                this.defaults = nVal
                this.configData = nVal[this.configNme]
            },
            immediate: true,
            deep: true
        }
    },
    created () {
        this.defaults = this.configObj
        this.configData = this.configObj[this.configNme]
    },
    methods: {
        bindDelete () {
            this.configData.url = '';
        },
        // 点击图文封面
        modalPicTap () {
            const _this = this;
            this.$modalUpload(function (img) {
                _this.configData.url = img[0];
            });
        },
        // 添加自定义弹窗
        addCustomDialog (editorId) {
            window.UE.registerUI('test-dialog', function (editor, uiName) {
                let dialog = new window.UE.ui.Dialog({
                    iframeUrl: '/admin/widget.images/index.html?fodder=dialog',
                    editor: editor,
                    name: uiName,
                    title: this.$t(this.$t('上传图片')),
                    cssRules: 'width:1200px;height:500px;padding:20px;'
                });
                this.dialog = dialog;
                // 参考上面的自定义按钮
                var btn = new window.UE.ui.Button({
                    name: 'dialog-button',
                    title: this.$t(this.$t('上传图片')),
                    cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,
                    onclick: function () {
                        // 渲染dialog
                        dialog.render();
                        dialog.open();
                    }
                });

                return btn;
            }, 37);
        },
        // 获取图片信息
        getPic (pc) {
            this.$nextTick(() => {
                this.configData.url = pc.att_dir;
                this.modalPic = false;
            })
        }
    }
}
</script>

<style scoped lang="scss">
.header{
    font-size: 14px;
    color: #000;
}
.title{
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom:1px solid rgba(0,0,0,0.05);
    font-size:12px;
    color:#999;
}
.box{
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
    position: relative;
    .iconfont-diy{
        position: absolute;
        top: -15px;
        right: -8px;
        font-size: 25px;
        color: #999;
    }
        
    img{
        width: 100%;
        height: 100%;
    }
       
}
   

.upload-box{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 80px;
    height: 80px;
    background: #f7f7f7;
    font-size: 12px;
    color: #cccccc;
    .iconfont{
        font-size: 16px;
    }
}
   
</style>
