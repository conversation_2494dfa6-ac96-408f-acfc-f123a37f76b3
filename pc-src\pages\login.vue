<template>
    <div class="login">
      <div class="wrapper_1200">
        <div class="header acea-row row-between-wrapper" v-show="isShow">
          <div class="acea-row row-middle">
            <div class="icon" @click="goHome">
              <img :src="info.logoUrl">
            </div>
            <div class="name" @click="goHome">官方商城</div>
          </div>
          <div class="acea-row row-middle">
            <div class="item"><span class="iconfont icon-pinzhongqiquan font-color"></span>品种齐全</div>
            <div class="item"><span class="iconfont icon-dijiachangxuan font-color"></span>低价畅选</div>
            <div class="item"><span class="iconfont icon-zhengpinhanghuo font-color"></span>正品行货</div>
          </div>
        </div>
      </div>
      <div class="loginBg min_wrapper_1200">
        <div class="wrapper" v-show="current === 1">
          <div class="title">快速登录/注册
            <!--@click="current = 3"-->
            <a :href="`https://open.weixin.qq.com/connect/qrconnect?appid=${appidNum}&redirect_uri=${hosts}&response_type=code&scope=snsapi_login&state=EqMkUDWh8F3euWlt23jHJ8ZJuaTAVPZyiKEoq5U0`" v-if="appidNum" class="iconfont icon-weixindenglu1"></a>
          </div>
          <div class="item phone acea-row row-middle">
            <div class="number">+86</div>
            <input type="text" placeholder="请输入手机号" v-model="account">
          </div>
          <div class="item verificat acea-row row-between-wrapper">
            <input type="text" placeholder="请输入验证码" v-model="captcha">
            <button class="code font-color" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="getVerify">
              {{ text }}
            </button>
          </div>
          <div class="signIn bg-color" @click="loginMobile">登录/注册</div>
          <div class="fastLogin font-color" @click="current = 2">账号登录</div>
        </div>
        <div class="wrapper" v-show="current === 2">
          <div class="title">账号登录
            <!--@click="current = 3"-->
            <a :href="`https://open.weixin.qq.com/connect/qrconnect?appid=${appidNum}&redirect_uri=${hosts}&response_type=code&scope=snsapi_login&state=EqMkUDWh8F3euWlt23jHJ8ZJuaTAVPZyiKEoq5U0`" v-if="appidNum" class="iconfont icon-weixindenglu1"></a>
          </div>
          <div class="item phone acea-row row-middle">
            <div class="number">+86</div>
            <input type="text" placeholder="请输入手机号" v-model="account">
          </div>
          <div class="item pwd">
            <input type="password" placeholder="请输入密码" v-model="password">
          </div>
          <div class="signIn bg-color" @click="loginH5">登录</div>
          <div class="fastLogin font-color" @click="current = 1">快速登录/注册</div>
        </div>
      </div>
      <div class="footer wrapper_1200">
        <div>
          <span>联系电话：{{info.contact_number}}</span>
          <span class="adress">地址：{{info.company_address}}</span>
        </div>
        <div class="record">{{info.copyright}}<a href="https://beian.miit.gov.cn/" target="_blank" class="num">{{info.record_No}}</a></div>
      </div>
      <Verify @success="verifySuccess" captchaType="clickWord" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import sendVerifyCode from "@/mixins/SendVerifyCode";
import Verify from '@/components/verifition/Verify';
export default {
      name: "login",
      auth: false,
      components: {Verify},
      mixins: [sendVerifyCode],
      data(){
        return {
          current: 1,
          account: "",
          password: "",
          captcha: "",
          keyCode: "",
          info:'',
          isShow:true,
          appidNum:'',
          hosts:'',
          codes:'',
          fromPath:''
        }
      },
      fetch({ store}) {
        store.commit('isHeader', false);
        store.commit('isFooter', false);
      },
      head() {
        return {
          title: this.$store.state.titleCon
        }
      },
      beforeMount(){
        if (this.$auth.loggedIn) { this.$router.push('/') }
      },
      mounted(){
        window.addEventListener('keydown',this.keyDown);
        this.hosts = location.origin + location.pathname;
        this.fromPath = this.$cookies.get("fromPath");
        if(this.codes){
          this.loginCode();
        }
      },
      destroyed(){
        window.removeEventListener('keydown',this.keyDown,false);
      },
      methods: {
        keyDown(e){
          if(e.keyCode === 13){
            if(this.current === 1){
              this.loginMobile();
            }else if(this.current === 2){
              this.loginH5();
            }
          }
        },
        goHome(){
          this.$router.push({path: '/'});
        },
        async loginCode(){
          let that = this;
          await that.$auth.loginWith('local3', {params:{ code: this.codes }}).then(()=>{
            that.isShow = false;
            if(this.fromPath){
              let path = this.fromPath.split(that.$router.history.base);
              let fromPath = path.join('');
              that.$router.push(fromPath);
            }else {
              that.$router.push('/');
            }
            that.$cookies.remove("fromPath");
          }).catch(err=>{
            // that.$layer.msg('登录失败');
          })
        },
        async loginH5 () {
          let that = this;
          if (!that.account) return that.$message.error('请填写手机号码');
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
          if (!that.password) return that.$message.error("请填写密码");
          let userInfo = {
            account: that.account,
            password: that.password
          };
          await that.$auth.loginWith('local1', { data: userInfo }).then(()=>{
            that.isShow = false;
            if(this.fromPath){
              let path = this.fromPath.split(that.$router.history.base);
              let fromPath = path.join('');
              that.$router.push(fromPath);
            }else {
              that.$router.push('/');
            }
            that.$cookies.remove("fromPath");
          }).catch(err=>{
            that.$message.error(err);
          })
        },
        async loginMobile(){
          let that = this;
          if (!that.account) return that.$message.error("请填写手机号码");
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error("请输入正确的手机号码");
          if (!that.captcha) return that.$message.error("请填写验证码");
          if (!/^[\w\d]+$/i.test(that.captcha)) return that.$message.error("请输入正确的验证码");
          let userInfo = {
            phone: that.account,
            captcha: that.captcha,
            user_type: 'pc'
          };
          await that.$auth.loginWith('local2', { data: userInfo }).then(()=>{
            that.isShow = false;
            if(this.fromPath){
              let path = this.fromPath.split(that.$router.history.base);
              let fromPath = path.join('');
              that.$router.push(fromPath);
            }else {
              that.$router.push('/');
            }
            that.$cookies.remove("fromPath");
          }).catch(err=>{
            that.$message.error('验证码错误');
          })
        },
        async code() {
          let that = this;
          if (!that.account) return that.$message.error('请填写手机号码');
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error('请输入正确的手机号码');
          await this.$axios.post("/register/verify",{
            phone: that.account,
            type: 'mobile',
            key: that.keyCode,
            captchaType: "clickWord",
            captchaVerification: data.captchaVerification
          }).then(res=>{
            that.$message.success(res.msg);
            that.sendCode();
          }).catch(err => {
            that.$message.error(err);
          });
        },
        getVerify() {
            let that = this;
            if (!that.account) return that.$message.error('请填写手机号码');
            if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account)) return that.$message.error('请输入正确的手机号码');
            this.$refs.verify.show();
        },
        verifySuccess(params) {
            this.closeModel(params);
        },
        // 关闭模态框
        closeModel(params) {
            this.isShow = false;
            this.code(params);
        },
      }
    }
</script>

<style scoped lang="scss">
  .login{
    .header{
      height: 110px;
      .icon{
        cursor: pointer;
        width: 112px;
        height: 40px;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 28px;
        margin-left: 15px;
        cursor: pointer;
      }
      .item{
        margin-left: 40px;
        font-size: 16px;
        color: #666666;
        .iconfont{
          margin-right: 6px;
          font-size: 20px;
        }
      }
    }
    .loginBg{
      width: 100%;
      height: 608px;
      background: url(../assets/images/loginBg.jpg) no-repeat;
      background-size: 100% 100%;
      position: relative;
      .wxLogin{
        width: 450px;
        height: 427px;
        background: #FFFFFF;
        position: absolute;
        right: 360px;
        top:91px;
        padding-top: 34px;
        .title{
          font-weight: 400;
          font-size: 20px;
          padding-left: 30px;
          position: relative;
          .iconfont{
            font-size: 60px;
            position: absolute;
            right: 0;
            top: -35px;
          }
        }
        .wxCode{
          width: 220px;
          margin: 38px auto 0 auto;
          .iconfont{
            font-size: 30px;
            color: #CBCBCB;
            &.right{
              transform:rotateY(180deg);
            }
            &.bottomL{
              transform:rotateX(180deg);
            }
            &.bottomR{
              transform:rotateX(180deg);
            }
          }
          .pictrue{
            width: 190px;
            height: 190px;
            margin: -15px auto;
            img{
              width: 100%;
              height: 100%;
            }
          }
        }
        .tip{
          color: #666;
          font-size: 16px;
          margin-top: 20px;
          text-align: center;
        }
      }
      .wrapper{
        width: 450px;
        height: 427px;
        background-color: #fff;
        position: absolute;
        top:91px;
        right: 360px;
        text-align: center;
        padding: 70px 0;
        .title{
          font-size: 20px;
          font-weight: 400;
          position: relative;
          .iconfont{
            position: absolute;
            top: -71px;
            right: 0;
            font-size: 60px;
            cursor: pointer;
          }
        }
        .item{
          width: 358px;
          height: 50px;
          border: 1px solid #DBDBDB;
          margin: 0 auto;
          &.phone{
            margin-top: 34px;
            .number{
              width: 65px;
              height: 100%;
              line-height: 50px;
              color: #666666;
              border-right: 1px solid #DBDBDB;
            }
            input{
              width: 291px;
            }
          }
          &.pwd{
            margin-top: 20px;
            input{
              width: 100%;
            }
          }
          &.verificat{
            margin-top: 20px;
            input{
              width: 246px;
            }
            .code{
              width: 110px;
              height: 100%;
              border: 0;
              background-color: #fff;
              border-left: 1px solid #DBDBDB;
              &.on{
                color: #CCC !important;
              }
            }
          }
          input{
            padding-left: 15px;
            height: 100%;
            border: 0;
            outline: none;
          }
        }
        .signIn{
          width: 358px;
          height: 50px;
          text-align: center;
          line-height: 50px;
          margin: 24px auto 0 auto;
          color: #fff;
          cursor: pointer;
        }
        .fastLogin{
          margin-top: 14px;
          cursor: pointer;
        }
      }
    }
    .footer{
      text-align: center;
      font-size: 12px;
      color: #555;
      margin-top: 100px;
      .adress{
        margin-left: 40px;
      }
      .record{
        margin-top: 6px;
        .num{
          margin-left: 10px;
          &:hover{
            color: #E93323
          }
        }
      }
    }
  }

</style>
