<template>
  <div>
    <div class="footer">
      <div class="wrapper_1200">
        <ul class="acea-row row-around row-middle">
          <li class="acea-row row-middle">
            <div class="picture"><span class="iconfont icon-pinzhong"></span></div>
            <div>品种齐全，购物轻松</div>
          </li>
          <li class="acea-row row-middle">
            <div class="picture"><span class="iconfont icon-zhifa"></span></div>
            <div>多仓直发，极速配送</div>
          </li>
          <li class="acea-row row-middle">
            <div class="picture"><span class="iconfont icon-hanghuo"></span></div>
            <div>正品行货，精致服务</div>
          </li>
          <li class="acea-row row-middle">
            <div class="picture"><span class="iconfont icon-dijia"></span></div>
            <div>天天低价，畅选无忧</div>
          </li>
        </ul>
        <div class="recordNum">
          <!-- 公司其他版权信息 -->
          <div v-if="companyInfo.copyright && companyInfo.copyright.length > 0" class="company-links">
            <span v-for="(item, index) in companyInfo.copyright" :key="index">
              <a :href="item.url" class="copyright-link" v-if="item.url !== '#'" target="_blank">
                <img v-if="item.img" :src="item.img" alt="">{{item.name}}
              </a>
              <span class="copyright-text" v-else>
                <img v-if="item.img" :src="item.img" alt="">{{item.name}}
              </span>
            </span>
          </div>
          
          <!-- 主要版权信息 - 统一格式 -->
          <div class="main-copyright">
            <div class="copyright-line">
              Copyright © {{customConfig.copyrightYear}} {{customConfig.companyName}} All Rights Reserved
            </div>
            <div class="beian-line" v-if="processedRecordNumber">
              <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                {{processedRecordNumber}}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "footers",
      components: {},
      mixins: [],
      data(){
        return{
          companyInfo:{},
          codeUrl:'',
          iScode:false,
          version: {},
          // 🎯 自定义配置区域 - 在这里修改您想要的文本
          customConfig: {
            companyName: 'LeaveuKey 留游记',        // 替换CRMEB的公司名称
            recordNumber: '京ICP备12345678号',         // 替换备案号 (请替换为您的真实备案号)
            copyrightYear: new Date().getFullYear(), // 版权年份
            // 可以添加更多自定义配置
            enableReplace: true,              // 是否启用替换功能
          }
        }
      },
      computed: {
        // 处理后的备案号
        processedRecordNumber() {
          if (!this.customConfig.enableReplace) {
            return this.version.beian_sn;
          }
          // 如果配置了自定义备案号，使用自定义的
          if (this.customConfig.recordNumber && this.customConfig.recordNumber !== '您的备案号') {
            return this.customConfig.recordNumber;
          }
          // 否则使用API返回的备案号
          return this.version.beian_sn;
        }
      },
      head() {
        return {
          meta: [
            {
              hid:'keywords',name:'keywords',content:this.companyInfo.site_keywords
            },
            {
              hid:'description',name:'description',content:this.companyInfo.site_description
            },
          ]
        }
      },
      beforeMount(){
        this.getCompanyInfo();
        this.getVersion();
      },
      mounted(){

      },
      methods:{
        goTop(){
          (function n() {
            var t = document.documentElement.scrollTop || document.body.scrollTop;
            if (t > 0) {
              window.requestAnimationFrame(n);
              window.scrollTo(0, t - t / 5)
            }
          })()
        },
        goRecommend(){

        },
        wxCode(){
          this.iScode = true
        },
        wxCodeHide(){
          this.iScode = false
        },
        goCart(){
          this.$router.push({path:'/shopping_cart'});
        },
        getCompanyInfo(){
          this.$axios.get("/api/pc/config").then(res=>{
            // 对API返回的数据进行嵌套替换
            this.companyInfo = this.processCompanyData(res.data);
            this.$store.commit('logo', res.data.site_logo);
            this.$cookies.set('logo',res.data.site_logo);
            this.$store.commit('titles', res.data.site_name);
            this.$cookies.set('titles',res.data.site_name);
            this.$store.commit('qrCode', res.data.wechat_qrcode);
            this.$store.commit('consumerHotline', res.data.sys_phone);
            this.$cookies.set('consumerHotline',res.data.sys_phone);
            this.$store.commit('domainName', res.data.site_url);
            this.$cookies.set('domainName',res.data.site_url);
            this.$store.commit('openService', res.data.is_open_service);
            this.$cookies.set('openService', res.data.is_open_service);
            this.$store.commit('openSettled',res.data.mer_intention_open);
          })
        },
        processCompanyData(data) {
          // 如果禁用替换功能，直接返回原数据
          if (!this.customConfig.enableReplace) {
            return data;
          }
          
          // 创建数据副本
          const processedData = { ...data };
          
          // 处理版权数组信息
          if (processedData.copyright && Array.isArray(processedData.copyright)) {
            processedData.copyright = processedData.copyright.map(item => {
              const processedItem = { ...item };
              if (processedItem.name) {
                processedItem.name = processedItem.name
                  .replace(/CRMEB/g, this.customConfig.companyName)
                  .replace(/陕ICP备14011498号-3/g, this.customConfig.recordNumber)
                  .replace(/陕ICP备\d+号-\d+/g, this.customConfig.recordNumber)
                  .replace(/2025/g, this.customConfig.copyrightYear);
              }
              return processedItem;
            });
          }
          
          return processedData;
        },
        getVersion(){
          this.$axios.get("/api/version").then(res=>{
            // 对API返回的数据进行嵌套替换
            this.version = this.processVersionData(res.data);
          })
        },
        processVersionData(data) {
          // 如果禁用替换功能，直接返回原数据
          if (!this.customConfig.enableReplace) {
            return data;
          }
          
          // 创建数据副本以避免直接修改原数据
          const processedData = { ...data };
          
          // 替换版权信息
          if (processedData.Copyright) {
            processedData.Copyright = processedData.Copyright
              .replace(/CRMEB/g, this.customConfig.companyName)  // 使用配置的公司名称
              .replace(/2025/g, this.customConfig.copyrightYear); // 使用配置的年份
          }
          
          // 替换备案号
          if (processedData.beian_sn) {
            processedData.beian_sn = processedData.beian_sn
              .replace(/陕ICP备14011498号-3/g, this.customConfig.recordNumber)  // 使用配置的备案号
              .replace(/陕ICP备\d+号-\d+/g, this.customConfig.recordNumber);  // 使用正则匹配任何备案号格式
          }
          
          return processedData;
        }
      }
    }
</script>

<style scoped lang="scss">
  .footer{
    margin-top: 50px;
    background-color: #f2f2f2;
    ul{
      height: 104px;
      border-bottom: 1px solid #E2E2E2;
      li{
        .picture{
          width: 40px;
          height: 40px;
          border: 1px solid #707070;
          border-radius: 50%;
          text-align: center;
          line-height: 40px;
          margin-right: 14px;
          .iconfont{
            font-size: 23px;
            color: #707070;
          }
        }
      }
    }
    .recordNum{
      text-align: center;
      padding: 30px 0 48px 0;
      
      // 公司其他链接信息
      .company-links {
        margin-bottom: 15px;
        
        img{
          width: 14px;
          height: 14px;
          display: inline-block;
          margin-right: 1px;
          position: relative;
          top: 2px;
        }
        
        .copyright-link, .copyright-text{
          color: #999;
          text-decoration: none;
          
          &:after{
            content: '|';
            display: inline-block;
            color: #ccc;
            font-size: 16px;
            margin: 0 10px;
          }
          &:last-child{
            &:after{
              display: none;
            }
          }
          
          &:hover {
            color: #E93323;
          }
        }
        
        .copyright-text {
          cursor: default;
        }
      }
      
      // 主要版权信息
      .main-copyright {
        .copyright-line {
          color: #666;
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 8px;
          font-weight: 400;
        }
        
        .beian-line {
          .beian-link {
            color: #999;
            text-decoration: none;
            font-size: 13px;
            
            &:hover {
              color: #E93323;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
</style>
