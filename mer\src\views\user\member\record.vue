<template>
  <div class="divBox">
    <div class="selCard">
      <el-form inline size="small" label-width="85px">
        <el-form-item :label="$t('时间选择：')" class="width100">
          <el-date-picker
            class="selWidth"
            v-model="timeVal"
            value-format="yyyy/MM/dd"
            format="yyyy/MM/dd"
            size="small"
            type="daterange"
            placement="bottom-end"
            :placeholder="$t('自定义时间')"
            @change="onchangeTime"
          />
        </el-form-item>
        <el-form-item :label="$t('会员类别：')">
          <el-select v-model="tableFrom.svip_type" clearable :placeholder="$t('请选择')" class="selWidth" @change="getList()">
            <el-option :label="$t('试用期')" value="1" />
            <el-option :label="$t('有限期')" value="2" />
            <el-option :label="$t('用久期')" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('支付方式：')">
          <el-select v-model="tableFrom.pay_type" clearable :placeholder="$t('请选择')" class="selWidth" @change="getList()">
            <el-option :label="$t('微信')" value="weixin" />
            <el-option :label="$t('支付宝')" value="alipay" />
            <el-option :label="$t('小程序')" value="routine" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('会员卡名称：')">
          <el-input v-model="tableFrom.title" @keyup.enter.native="getList()" :placeholder="$t('请输入会员卡名称')" class="selWidth" />
        </el-form-item>             
        <el-form-item :label="$t('搜索：')">
          <el-input v-model="tableFrom.keyword" @keyup.enter.native="getList(1)" :placeholder="$t('请输入用户名称搜索')" class="selWidth" />
        </el-form-item>
          
      </el-form>
    </div>
    <el-card class="box-card">
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
      >
        <el-table-column prop="order_sn" :label="$t('订单号')" min-width="60" />
        <el-table-column prop="user.nickname" :label="$t('用户名')" min-width="60" />
        <el-table-column prop="user.phone" :label="$t('手机号码')" min-width="60" />
        <el-table-column prop="title" :label="$t('会员卡名称')" min-width="60" />
        <el-table-column prop="pay_price" :label="$t('支付金额(元)')" min-width="60" />
        <el-table-column prop="price" :label="$t('支付方式')" min-width="60">
          <template slot-scope="scope">
            <span></span>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" :label="$t('购买时间')" min-width="60" />
        <el-table-column prop="user.svip_endtime" :label="$t('到期时间')" min-width="60" />
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {
  memberRecordListApi
} from "@/api/user";
export default {
  name: "LabelList",
  data() {
    return {
      listLoading: true,
      timeVal: [],
      tableData: {
        data: [],
        total: 0,
      },
      tableFrom: {
        page: 1,
        limit: 20,
      },
    };
  },
  mounted() {
    this.getList('');
  },
  methods: {  
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.tableFrom.date = e ? this.timeVal.join("-") : "";
      this.tableFrom.page = 1;
      this.getList(1);
    },
    // 列表
    getList(num) {
      this.listLoading = true;
      this.tableFrom.page = num ? num : this.tableFrom.page;
      memberRecordListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list;
          this.tableData.total = res.data.count;  
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList('');
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList('');
    },
  },
};
</script>

<style scoped lang="scss">
</style>
