<template>
  <div class="order-detail wrapper_1200">
    <div class="header">
      <nuxt-link to="/">首页></nuxt-link>
      <nuxt-link :to="{path:'/user',query:{type:0}}">个人中心></nuxt-link>
      <span>退款单详情</span>
    </div>
    <div class="section reject">
      <div class="iconfont icon-shenhezhong1 red-color" v-if="type == 0"></div>
      <div class="iconfont icon-daituihuo" v-if="type == 1"></div>
      <div class="iconfont icon-tuihuozhong" v-if="type == 2"></div>
      <div class="iconfont icon-yituikuan" v-if="type == 3"></div>
      <div class="iconfont icon-yijujue1" v-if="type == -1"></div>
      <div v-if="type == -1">
        <div class="section-hd">退款失败</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    申请时间：{{ orderData.status_time}}
                </li>
            </ul>
        </div>
      </div>
      <div v-if="type == 0">
        <div class="section-hd">审核中</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    申请时间：{{ orderData.status_time}}
                </li>
            </ul>
        </div>
      </div>
      <div v-if="type == 1">
        <div class="section-hd">请退货并填写物流信息</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    申请时间：{{ orderData.status_time}}
                </li>
            </ul>
        </div>
      </div>
      <div v-if="type == -2">
        <div class="section-hd">已取消退货申请</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    申请时间：{{ orderData.status_time}}
                </li>
            </ul>
        </div>
      </div>
      <div v-if="type == 2">
        <div class="section-hd">请等待商家收货并退款</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    还剩：
                    <countDown
                    :is-day="false"
                    :tip-text="' '"
                    :day-text="' '"
                    :hour-text="' : '"
                    :minute-text="' : '"
                    :second-text="' '"
                    :datatime="datatime"
                  ></countDown>
                </li>
            </ul>
        </div>
      </div>
      <div v-if="type == 3">
        <div class="section-hd">退款成功，金额 ¥{{orderData.refund_price}}</div>
        <div class="section-bd">
            <ul>
                <li class="acea-row row-middle">
                    申请时间：{{ orderData.status_time}}
                </li>
            </ul>
        </div>
      </div>
    </div>
    <!-- 拒绝 -->
    <div v-if="type == -1" class="section reason">
      <div class="section-hd">
        <span class="iconfont icon-tuikuantishi"></span>商家拒绝退款
      </div>
      <div class="section-bd">
        <ul>
          <li class="acea-row">
            <div>拒绝原因：</div>
            <div>
              {{ orderData.fail_message }}
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 待退货 -->
    <div v-if="type == 1" class="section">
      <div class="red-txt"><span class="iconfont icon-zhuyi-copy"></span>请按以下收货信息将商品退回</div>
      <div class="reason">
          <div class="section-bd">
            <ul>
            <li class="acea-row">
                <div>收货人姓名：</div>
                <div>{{ orderData.mer_delivery_user }}</div>
            </li>
            <li class="acea-row">
                <div>收货人联系方式：</div>
                <div>{{ orderData.phone }}</div>
            </li>
            <li class="acea-row">
                <div>收货人地址：</div>
                <div>{{ orderData.mer_delivery_address }}</div>
            </li>
            </ul>
        </div>
      </div>
    </div>
      <!-- 待收货 -->
    <div v-if="type == 2" class="section">
        <div class="red-txt"><span class="iconfont icon-zhuyi-copy"></span>请按以下收货信息将商品退回</div>
        <div class="reason">
            <div class="section-hd" style="color:#E93323">
                <span class="iconfont icon-tuikuantishi"></span>商家收货并验货无误，将操作退款给您
            </div>
            <div class="section-bd">
                <ul>
                <li class="acea-row">
                    <div>收货人姓名：</div>
                    <div>{{ orderData.mer_delivery_user }}</div>
                </li>
                <li class="acea-row">
                    <div>收货人联系方式：</div>
                    <div>{{ orderData.phone }}</div>
                </li>
                <li class="acea-row">
                    <div>收货人地址：</div>
                    <div>{{ orderData.mer_delivery_address }}</div>
                </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="section">
      <div class="section-hd">退款信息</div>
      <div class="section-bd">
        <ul>
          <li class="acea-row row-middle">
            <div>订单编号：</div>
            <div>{{ orderData.refund_order_sn }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>退款金额：</div>
            <div>{{ orderData.refund_price }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>申请件数：</div>
            <div>{{ orderData.refund_num }}</div>
          </li>
          <li class="acea-row row-middle">
            <div>申请时间：</div>
            <div>{{ orderData.create_time }}</div>
          </li>
          <li v-if="orderData.mark" class="acea-row">
            <div>备注说明：</div>
            <div>{{ orderData.mark ? orderData.mark : "" }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="section">
      <div class="section-bd">
        <ul class="goods">
          <li
            v-for="(item,index) in orderData.refundProduct"
            :key="index"
            class="acea-row row-middle">
            <div>
              <img :src="(item.product.cart_info.productAttr && item.product.cart_info.productAttr.product && item.product.cart_info.productAttr.product.image) || item.product.cart_info.product.image" />
            </div>
            <div>
              <div>{{ item.product.cart_info.product.store_name }}</div>
              <div class="info" v-if="item.product.cart_info && item.product.cart_info.productAttr && item.product.cart_info.productAttr.sku">
                {{item.product.cart_info.productAttr.sku}}
              </div>
              <div>
                <span class="money" v-if="item.product.cart_info && item.product.cart_info.productAttr">￥{{item.product.cart_info.productAttr.price}}</span>
                <span>x{{ item.refund_num }}</span>
              </div>
            </div>
            <div class="refund_btn" v-if="item.is_refund ==0 && orderData.status != 9">
              <el-button size="mini" @click="refund(item)">申请退款</el-button>
            </div>
          </li>
        </ul>
        <ul class="total">
          <li class="acea-row row-middle row-between">
            <div class="price-total">
              共{{ getRefundTotal(orderData) }}件商品，订单退款金额为：
              <span class="money">￥<b>{{ orderData.refund_price }}</b></span>
            </div>
            <div class="footerState acea-row row-middle">
              <div v-if="type==-1" class="pay" @click="applyAgain">再次申请</div>
              <template v-if="type==0 || type==1">
                <div class="btn" @click="cancelSales">取消申请</div>
                <template v-if="type==1">
                  <div class="pay" @click="goPage">退回商品</div>
                </template>   
              </template>
              <nuxt-link :to="{path: '/refund_logistics',query: { orderId: orderData.refund_order_id }}" v-else-if="type==2" class="pay">查看物流</nuxt-link>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
import {Message, MessageBox} from "element-ui";
export default {
  auth: "guest",
  components: {countDown },
  data() {
    return {
      orderData: {},
      goodsNum: 0,
      refundNum: [],
      cartInfo: [],
      type: 0,
      datatime: 0,
      refund_order_id: 0,
    };
  },
  async asyncData({ app, query }) {
    let [ orderData ] = await Promise.all([
            app.$axios.get(`/api/refund/detail/${query.id}`)
        ]);
    return {
      type: orderData.data.status,
      datatime: orderData.data.auto_refund_time,
      orderData: orderData.data,
      refund_order_id: query.id,
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "订单详情-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    this.getOrderInfo()
  },

  methods: {
    getRefundTotal(refund){
      return refund.refundProduct.reduce((i,pro)=>{
        return i + pro.refund_num;
      }, 0)
    },
    getOrderInfo() {
      this.$axios.get(`/api/refund/detail/${this.refund_order_id}`).then(res => {
        this.orderData = res.data;
        this.type = res.data.status;
        this.datatime = res.data.auto_refund_time
      });
    },
    // 再次申请
    applyAgain(){
      this.$router.push({
        path: `/order_detail`,
        query:{
          orderId:this.orderData.refundProduct[0].product.order_id
        }
      })
    },
    // 取消申请
    cancelSales() {
      let that = this;
      MessageBox.confirm("确定取消售后吗？", "提示").then(res => {
        that.$axios
          .post(`/api/refund/cancel/${this.orderData.refund_order_id}`)
          .then(data => {
            Message.success(data.message);
            that.getOrderInfo();
          });
      });
    },
    // 退回商品
    goPage(){
      this.$router.push({
        path: `/refund_goods`,
        query:{
          id:this.orderData.refund_order_id
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.order-detail {
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    background-color: unset;
    .home {
      color: #282828;
    }
  }

  > div {
    background-color: #ffffff;

    &.order-number {
      li {
        div {
          &:nth-child(2) {
            flex: none;
          }
        }

        a {
          margin-left: 30px;
          font-size: 16px;
          color: #236fe9;

          .iconfont {
            font-size: 12px;
          }
        }
      }
    }

    ~ div {
      margin-top: 14px;
    }

    > div {
      ~ div {
        border-top: 1px dashed #cecece;
      }
    }

    &.process {
      margin-top: 0;
      div {
        border-top: none;

        &.section-hd {
          padding: 26px 22px 0;
        }

        ul {
          padding: 27px 0 94px;

          &::after {
            content: "";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
          }
        }
        li {
          position: relative;
          float: left;
          margin-top: 0;
          margin-left: 222px;
          &:first-child {
            margin-left: 111px;
          }
          .line {
            position: absolute;
            top: 50%;
            left: 16px;
            width: 226px;
            height: 4px;
            background: #c7c7c7;
            transform: translateY(-50%);
          }
          .iconfont {
            position: relative;
            width: auto;
            font-size: 18px;
            line-height: 1;
            color: #c7c7c7;
            + .iconfont {
              position: absolute;
              top: 50%;
              left: 50%;
              display: none;
              width: 40px;
              height: 40px;
              border: 4px solid #e93323;
              border-radius: 50%;
              background: #ffffff;
              transform: translate(-50%, -50%);
              font-size: 20px;
              line-height: 32px;
              text-align: center;
              color: #e93323;
            }
          }
          .arrow {
            position: absolute;
            top: 50%;
            left: 100%;
            display: none;
            width: 80px;
            height: 16px;
            background: #e93323;
            transform: translateY(-50%);
            &::after {
              content: "";
              position: absolute;
              top: 0;
              left: 100%;
              border-width: 8px;
              border-style: solid;
              border-color: transparent transparent transparent #e93323;
            }
          }
          .info {
            position: absolute;
            top: 42px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            text-align: center;
            color: #9a9a9a;
            width: 100px;
            div {
              &:first-child {
                margin-bottom: 4px;
                font-size: 16px;
                color: #282828;
              }
            }
          }
          &.past {
            .line {
              background: rgba(233, 51, 35, 0.6);
            }
            .iconfont {
              color: #e93323;
            }
          }
          &.now {
            .info {
              div {
                &:first-child {
                  color: #e93323;
                }
              }
            }

            .iconfont {
              + .iconfont {
                display: block;
              }
            }

            .arrow {
              display: block;
            }
          }
        }
      }
    }

    &.reject {
      position: relative;
      padding: 30px 22px;
      background: #666666;
      overflow: hidden;
      margin-top: 0;

      .iconfont {
        position: absolute;
        top: -20px;
        right: 28px;
        font-size: 72px;
        color: #818181;
        opacity: .6;
      }
      .red-color{
        color: #E93323;
        opacity: .6;
      }
      div {
        border-top: none;

        &.section-hd {
          padding: 0;
          font-weight: bold;
          color: #ffffff;
        }

        ul {
          padding: 0;
          margin-top: 8px;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }

    &.reason {
      padding: 26px 22px;

      div {
        border-top: none;

        &.section-hd {
          padding: 0;
          .iconfont {
            margin-right: 8px;
          }
        }

        ul {
          padding: 0;
          margin-top: 15px;
          color: #7e7e7e;
        }
      }
    }
  }
  .section-hd {
    padding: 18px 22px;
    font-size: 18px;
    color: #282828;
    position: relative;
    .orderBnt{
        color: #E93323;
        font-size: 18px;
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        .iconfont{
            font-size: 20px;
        }
    }
  }
  .red-txt{
    color: #E93323;
    line-height: 50px;
    padding: 0 22px;
   }
  .section-bd {
    ul {
      padding: 22px;
      font-size: 16px;
      color: #282828;

      ~ ul {
        border-top: 1px dashed #cecece;
      }
    }
    li {
     ~ li {
        margin-top: 20px;
      }
      > div {
        &:first-child {
          width: 120px;
          color: #282828;
        }
        &:nth-child(2) {
          flex: 1;
           color: #282828;
        }
        &.time {
        margin-left: 10px;
        color: #fff;
        width: auto;
      }
      }

      &.coupon {
        span {
          ~ span {
            margin-left: 18px;
          }
        }
      }
    }

    .money {
      color: #e93323;

      b {
        font-weight: normal;
        font-size: 22px;
      }
    }

    .goods {
      .info {
        font-size: 12px;
        color: #aaa;
        margin-top: 4px;
      }

      li {
        position: relative;
        ~ li {
          margin-top: 22px;
        }

        > div {
          &:nth-child(1) {
            width: 86px;
            height: 86px;
            overflow: hidden;

            img {
              display: block;
              width: 100%;
              height: 100%;
            }
          }

          &:nth-child(2) {
            margin-right: 56px;
            margin-left: 26px;

            > div {
              &:first-child {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
              }

              &:last-child {
                margin-top: 10px;

                del {
                  margin-left: 12px;
                  font-size: 14px;
                  color: #919191;
                }
              }
            }
          }

          &:nth-child(3) {
            font-size: 14px;
            color: #b1b1b1;
          }
        }
      }
    }

    .total {
      padding: 28px 22px;
      .footerState {
        cursor: pointer;
      }
      .pay{
        width: 110px;
        height: 36px;
        text-align: center;
        line-height: 34px;
        margin-left: 20px;
        border: 1px solid #E93323;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        background: #E93323;
        color: #fff;
       
      }
      .btn{
        width: 110px;
        height: 36px;
        text-align: center;
        line-height: 34px;
        margin-left: 20px;
        border-radius: 4px;
        font-size: 14px;
        border: 1px solid #999999;
				color:  #666666;
      }
      .service {
        width: 114px;
        height: 40px;
        margin-left: 18px;
        background: #e93323;
        color: #fff;
        font-size: 16px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
      }
      .price-total {
        width: auto;
      }
      div {

        &:last-child {
          flex: none;
          .orderBnt {
            width: 114px;
            height: 40px;
            padding: 0;
            border: 1px solid #999999;
            border-radius: 2px;
            background: none;
            outline: none;
            font-size: 16px;
            line-height: 40px;
            text-align: center;
            color: #282828;
            cursor: pointer;
            ~ .orderBnt {
              margin-left: 18px;
            }

            &.on {
              border-color: #e93323;
              background: #e93323;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
.evaluate_btn {
 margin-left: 15px;
}
</style>
