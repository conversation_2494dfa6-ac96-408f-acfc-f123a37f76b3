// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import Layout from '@/layout'
import { roterPre } from '@/settings'
import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';
const systemFormRouter =
{
  path: `${roterPre}/systemForm`,
  name: 'system',
  meta: {
    icon: 'dashboard',
    title: leaveuKeyTerms['设置']
  },
  
  alwaysShow: true,
  component: Layout,
  children: [
    {
      path: 'Basics/:key?',
      component: () => import('@/views/systemForm/setSystem/index'),
      name: 'Basics',
      meta: { title: leaveuKeyTerms['店铺配置'], noCache: true }
    },
    {
      path: 'modifyStoreInfo',
      component: () => import('@/views/systemForm/setSystem/modifyStoreInfo'),
      name: 'ModifyStoreInfo',
      meta: { title: leaveuKeyTerms[' 商户基本信息'], noCache: true }
    },
    {
      path: 'systemStore',
      name: 'setting_systemStore',
      meta: {
        title: leaveuKeyTerms['提货点设置']
      },
      component: () => import('@/views/setting/systemStore/index')
    },
    {
      path: 'applyMents',
      name: 'ApplyMents',
      meta: {
        title: leaveuKeyTerms['申请分账商户']
      },
      component: () => import('@/views/systemForm/applyMents/index')
    },
    {
      path: 'applyList',
      name: 'ApplyList',
      meta: {
        title: leaveuKeyTerms['分账商户列表']
      },
      component: () => import('@/views/systemForm/applyMents/list')
    },
    {
      path: 'customer_keyword',
      component: () => import('@/views/system/customer_keyword/index'),
      name: 'CustomerKeyword',
      meta: { title: leaveuKeyTerms['自动回复'] }
    },
    {
      path: 'openAuth/list',
      component: () => import('@/views/setting/openAuth/list'),
      name: 'OpenList',
      meta: { title: leaveuKeyTerms['账户管理']},
    },
    {
      path: 'form_list',
      component: () => import('@/views/systemForm/form/index'),
      name: 'FormList',
      meta: {
        title: leaveuKeyTerms['系统表单'],
        noCache: true,

      }
    },
    {
      path: 'form_create',
      component: () => import('@/views/systemForm/form/create'),
      name: 'CreateForm',
      meta: { 
        title: leaveuKeyTerms['添加系统表单'],
        noCache: true,
        activeMenu: `${roterPre}/systemForm/form_list`
      }
    },
    {
      path: 'form_detail/:id?',
      component: () => import('@/views/systemForm/form/details'),
      name: 'FormDetail',
      meta: { 
        title: leaveuKeyTerms['表单详情'],
        noCache: true,
        activeMenu: `${roterPre}/devise/diy/list`
      }
    },
    
  ]
}

export default systemFormRouter
