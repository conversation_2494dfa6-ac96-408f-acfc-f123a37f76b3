<template>
  <div class="layout-search-dialog">
    <el-dialog
      :visible.sync="isShowSearch"
      width="540px"
      destroy-on-close
      :modal="false"
      fullscreen
      :show-close="true"
    >
      <el-autocomplete
        v-model="menuQuery"
        :fetch-suggestions="menuSearch"
        :placeholder="$t('菜单搜索：支持中文、路由路径')"
        prefix-icon="el-icon-search"
        ref="layoutMenuAutocompleteRef"
        @select="onHandleSelect"
        @blur="onSearchBlur"
      >
        <template slot-scope="{ item }">
          <div><i :class="item.icon" class="mr10"></i>{{ $t(item.title) }}</div>
        </template>
      </el-autocomplete>
    </el-dialog>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { getAllSiderMenu } from "@/utils/system.js";
import { getMenuSearchList } from "@/api/system";
import { roterPre } from "@/settings";
export default {
  name: "layoutBreadcrumbSearch",
  data() {
    return {
      isShowSearch: false,
      menuQuery: "",
      tagsViewList: [],
      roterPre: roterPre
    };
  },
  methods: {
    // 搜索弹窗打开
    openSearch() {
      this.menuQuery = "";
      this.isShowSearch = true;
      this.initTageView();
      this.$nextTick(() => {
        this.$refs.layoutMenuAutocompleteRef.focus();
      });
    },
    // 搜索弹窗关闭
    closeSearch() {
      setTimeout(() => {
        this.isShowSearch = false;
      }, 150);
    },
    // 菜单搜索数据过滤
    menuSearch(queryString, cb) {   
      if(queryString){
        getMenuSearchList({is_mer: 1,keyword: queryString})
        .then((res) => {
          this.tagsViewList = res.data.menus 
          let results = this.tagsViewList;
          cb(results);
        })
        .catch((err) => {
          this.$message.error(err.message);
        });
      }else{
        this.initTageView()
        let results =  this.tagsViewList;
        cb(results);
      }
      
    },
    // 菜单搜索过滤
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.path.toLowerCase().indexOf(queryString.toLowerCase()) >
            -1 ||
          restaurant.title.toLowerCase().indexOf(queryString.toLowerCase()) >
            -1 ||
          restaurant.title
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) > -1
        );
      };
    },
    // 初始化菜单数据
    initTageView(queryString) {
      // if (this.tagsViewList.length > 0) return false;
      this.tagsViewList = getAllSiderMenu(this.$store.state.user.menuList);
    },
    // 当前菜单选中时
    onHandleSelect(item) {
      let { path, redirect } = item;
      if (redirect) this.$router.push(redirect);
      else this.$router.push(path);
      this.closeSearch();
    },
    // input 失去焦点时
    onSearchBlur() {
      this.closeSearch();
    }
  }
};
</script>

<style scoped lang="scss">
.layout-search-dialog ::v-deep .el-dialog {
  box-shadow: unset !important;
  border-radius: 0 !important;
  background: rgba(0, 0, 0, 0.5);
}
.layout-search-dialog ::v-deep .el-autocomplete {
  width: 560px;
  position: absolute;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
}
::v-deep .el-dialog__header {
  border: none !important;
}
::v-deep .el-input--small .el-input__inner {
  height: 36px;
  line-height: 36px;
}
</style>
