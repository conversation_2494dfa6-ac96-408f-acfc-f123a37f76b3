{"name": "pcShop", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "postinstall": "patch-package"}, "bin": "npm run postinstall", "dependencies": {"@nuxt/generator": "2.14.5", "@nuxtjs/auth": "4.9.1", "@nuxtjs/axios": "5.12.2", "cookie-universal-nuxt": "^2.1.4", "crypto-js": "^4.2.0", "element-theme": "^2.0.1", "element-themex": "^1.0.3", "element-ui": "^2.13.2", "emoji-awesome": "0.0.2", "lodash.debounce": "^4.0.8", "nuxt": "2.14.5", "nuxt-sass-resources-loader": "^2.0.5", "qs": "^6.9.4", "vue-awesome-swiper": "^3.1.3", "vue-clipboard2": "^0.3.1", "vue-layer": "^1.2.1", "vue-qr": "^2.2.1"}, "devDependencies": {"babel-plugin-component": "^1.1.1", "element-theme-chalk": "^2.13.2", "node-sass": "^4.14.1", "patch-package": "^6.4.7", "sass-loader": "^10.0.2"}}