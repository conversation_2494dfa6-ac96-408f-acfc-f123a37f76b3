<template>
  <div class="divBox">
    <div class="selCard mb15">
        <el-form :model="tableFrom" ref="searchForm" size="small" label-width="85px" inline>
          <el-form-item :label="$t('核销时间：')">
            <el-date-picker
              v-model="timeVal"
              value-format="yyyy/MM/dd"
              format="yyyy/MM/dd"
              type="daterange"
              placement="bottom-end"
              :placeholder="$t('自定义时间')"
              style="width: 280px;"
              :picker-options="pickerOptions"
              clearable
              @change="onchangeTime"
            />
          </el-form-item>
          <el-form-item :label="$t('订单号：')" prop="keywords">
            <el-input
              v-model="tableFrom.keywords"
              :placeholder="$t('请输入订单号/收货人/联系方式')"
              class="selWidth"
              clearable
              @keyup.enter.native="getList(1),getCardList()"
            />
          </el-form-item>
          <el-form-item :label="$t('用户信息：')" prop="username">
            <el-input
              v-model="tableFrom.username"
              :placeholder="$t('请输入用户信息/联系电话')"
              class="selWidth"
              @keyup.enter.native="getList(1),getCardList()"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('支付方式：')" prop="pay_type">
            <el-select
              v-model="tableFrom.pay_type"
              clearable
              :placeholder="$t('请选择')"
              class="selWidth"
              @change="getList(1),getCardList()"
            >
              <el-option :label="$t('余额')" value="0" />
              <el-option :label="$t('微信')" value="1" />
              <el-option :label="$t('支付宝')" value="2" />
              <el-option :label="$t('线下支付')" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="getList(1)">{{ $t('搜索') }}</el-button>
            <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
          </el-form-item>
        </el-form>
      </div>
      <cards-data v-if="cardLists.length>0" :card-lists="cardLists" />
      <el-card>
        <el-table
          v-loading="listLoading"
          :data="tableData.data"
          size="small"
          class="table"
          highlight-current-row
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item :label="$t('商品总价：')">
                  <span>{{ props.row.total_price | filterEmpty }}</span>
                </el-form-item>
                <el-form-item :label="$t('下单时间：')">
                  <span>{{ props.row.create_time | filterEmpty }}</span>
                </el-form-item>
                <el-form-item :label="$t('用户备注：')">
                  <span>{{ props.row.mark | filterEmpty }}</span>
                </el-form-item>
                <el-form-item :label="$t('商家备注：')">
                  <span>{{ props.row.remark | filterEmpty }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column :label="$t('订单编号')" min-width="170">
            <template slot-scope="scope">
              <span style="display: block;" v-text="scope.row.order_sn" />
              <span v-show="scope.row.is_del > 0" style="color: #ED4014;display: block;">{{ $t('用户已删除') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('订单类型')" min-width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.order_type == 0 ? "普通订单" : "核销订单" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="real_name" :label="$t('收货人')" min-width="90" />
          <el-table-column :label="$t('商品信息')" min-width="320">
            <template slot-scope="scope">
              <div
                v-for="(val, i ) in scope.row.orderProduct"
                :key="i"
                class="tabBox acea-row row-middle"
              >
                <div class="demo-image__preview">
                  <el-image
                    :src="val.cart_info.product.image"
                    :preview-src-list="[val.cart_info.product.image]"
                  />
                </div>
                <span
                  class="tabBox_tit"
                >{{ val.cart_info.product.store_name + ' | ' }}{{ val.cart_info.productAttr.sku }}</span>
                <span
                  class="tabBox_pice"
                >{{ '￥'+ val.cart_info.productAttr.price + ' x '+ val.product_num }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="pay_price" :label="$t('实际支付')" min-width="90" />
          <el-table-column :label="$t('支付方式')" min-width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.paid === 1">{{ scope.row.pay_type | orderPayType }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="pay_price" :label="$t('核销员')" min-width="90">
            <template slot-scope="scope">
              <span v-if="scope.row.paid">{{ scope.row.verifyService ? scope.row.verifyService.nickname : "管理员核销" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="serviceScore" :label="$t('核销状态')" min-width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.refunding == 2">{{ $t('部分退款') }}</span>
              <span v-else>{{ scope.row.status == -1 ? "已退款" : "已核销" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="verify_time" :label="$t('核销时间')" min-width="150" />
        </el-table>
        <div class="block">
          <el-pagination
            background
            :page-size="tableFrom.limit"
            :current-page="tableFrom.page"
            layout="total, prev, pager, next, jumper"
            :total="tableData.total"
            @size-change="handleSizeChange"
            @current-change="pageChange"
          />
        </div>
      </el-card>
    <!--导出订单列表-->
    <file-list ref="exportList" />
  </div>
</template>
<script>import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { takeOrderListApi, takeChartApi, exportOrderApi, takeCardListApi } from '@/api/order'
import fileList from '@/components/exportFile/fileList'
import cardsData from '@/components/cards/index'
import timeOptions from '@/utils/timeOptions';
export default {
  components: { cardsData, fileList },
  data() {
    return {
      pickerOptions: timeOptions,
      orderId: 0,
      tableData: {
        data: [],
        total: 0
      },
      listLoading: true,
      tableFrom: {
        order_sn: '',
        status: '',
        date: '',
        page: 1,
        limit: 20,
        type: '4',
        order_type: '1',
        username: '',
        pay_type: '',
        keywords: ''
      },
      orderChartType: {},
      timeVal: [],
      fromList: {
        title: leaveuKeyTerms['选择时间'],
        custom: true,
        fromTxt: [
          { text: leaveuKeyTerms['全部'], val: '' },
          { text: leaveuKeyTerms['今天'], val: 'today' },
          { text: leaveuKeyTerms['昨天'], val: 'yesterday' },
          { text: leaveuKeyTerms['最近7天'], val: 'lately7' },
          { text: leaveuKeyTerms['最近30天'], val: 'lately30' },
          { text: leaveuKeyTerms['本月'], val: 'month' },
          { text: leaveuKeyTerms['本年'], val: 'year' }
        ]
      },
      selectionList: [],
      ids: '',
      tableFromLog: {
        page: 1,
        limit: 10
      },
      tableDataLog: {
        data: [],
        total: 0
      },
      LogLoading: false,
      dialogVisible: false,
      fileVisible: false,
      cardLists: [],
      orderDatalist: null,
      headeNum: [
        { type: 1, name: leaveuKeyTerms['全部'], count: 10 },
        { type: 2, name: leaveuKeyTerms['普通订单'], count: 3 },
        { type: 3, name: leaveuKeyTerms['直播订单'], count: 1 },
        { type: 4, name: leaveuKeyTerms['核销订单'], count: 2 },
        { type: 5, name: leaveuKeyTerms['拼团订单'], count: 0 },
        { type: 6, name: leaveuKeyTerms['秒杀订单'], count: 6 },
        { type: 7, name: leaveuKeyTerms['砍价订单'], count: 5 }
      ]
    }
  },
  mounted() {
    this.headerList()
    this.getCardList()
    this.getList(1)
  },
  methods: {
    /**重置 */
    searchReset(){
      this.timeVal = []
      this.tableFrom.date = ""
      this.$refs.searchForm.resetFields()
      this.getList(1)
      this.getCardList()
    },

    // 导出
    exportOrder() {
      exportOrderApi({
        status: this.tableFrom.status,
        date: this.tableFrom.date,
        take_order: 1
      })
        .then((res) => {
          const h = this.$createElement
          this.$msgbox({
            title: leaveuKeyTerms['提示'],
            message: h('p', null, [
              h('span', null, '文件正在生成中，请稍后点击"'),
              h('span', { style: 'color: teal' }, '导出记录'),
              h('span', null, '"查看~ ')
            ]),
            confirmButtonText: leaveuKeyTerms['我知道了']
          }).then(action => {

          })
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    getExportFileList() {
      this.fileVisible = true
      this.$refs.exportList.exportFileList('order')
    },

    pageChangeLog(page) {
      this.tableFromLog.page = page
      this.getList('')
    },
    handleSizeChangeLog(val) {
      this.tableFromLog.limit = val
      this.getList('')
    },
    handleSelectionChange(val) {
      this.selectionList = val
      const data = []
      this.selectionList.map((item) => {
        data.push(item.id)
      })
      this.ids = data.join(',')
    },
    // 选择时间
    selectChange(tab) {
      this.timeVal = []
      this.tableFrom.date = tab
      this.getCardList()
      this.getList(1)
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e
      this.tableFrom.date = e ? this.timeVal.join('-') : ''
      this.getCardList()
      this.getList(1)
    },
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableFrom.page = num || this.tableFrom.page
      takeOrderListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          //   this.cardLists = res.data.stat
          this.listLoading = false
        })
        .catch((res) => {
          this.$message.error(res.message)
          this.listLoading = false
        })
    },
    getCardList() {
      takeCardListApi(this.tableFrom)
        .then((res) => {
          this.cardLists = res.data
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList('')
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList('')
    },
    headerList() {
      takeChartApi()
        .then((res) => {
          this.orderChartType = res.data
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-icon-arrow-down {
  font-size: 12px;
}
.tabBox_tit {
  width: 55%;
  font-size: 12px !important;
  margin: 0 2px 0 10px;
  letter-spacing: 1px;
  padding: 5px 0;
  box-sizing: border-box;
}
</style>
