// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default function ({redirect, $axios, app})  {
  let that = this;
  // 数据访问前缀
  if(process.server){
    // 获取服务端的token
    // var token = getCookie.getcookiesInServer(req).token;
  }
  if(process.client){
    // 获取客户端token
    // var token = getCookie.getcookiesInClient('token');
  }
  let uuid = localStorage.getItem('uuid')
  if(!uuid){
    localStorage.setItem('uuid', Date.now().toString())
    uuid = localStorage.getItem('uuid')
  }
  // 请求拦截器
  $axios.interceptors.request.use(
    config => {
      let local = app.$cookies.get('auth.strategy');
      let token = app.$cookies.get(`auth._token.${local}`);
      if (token) {
        config.headers.common['X-Token'] = token;
      }
      config.headers.common['Form-type'] = 'pc';
      config.headers.common['uuid'] = uuid
      return config
    },
    error => {
      // do something with request error
      return Promise.reject(error)
    }
  )

  // response拦截器，数据返回后，可以先在这里进行一个简单的判断
  $axios.interceptors.response.use(
    response => {
      let code = response.data ? response.data.status : 0;
      switch (code) {
        case 200:
        //   app.$cookies.set("fromPath",location.pathname+location.search);
          return response.data
        // case 400:
        //   return Promise.reject(response.data.msg)
        case 401:
          return Promise.reject(response.data.message)
        case 402:
          return Promise.reject(response.data.message)
        case 410000:
        case 410001:
        case 410002:
        case 40000:
        case 400:
            if(response.data.message !== '请登录'){
                return Promise.reject(response.data.message)
            }else{
                app.$cookies.set("fromPath",location.pathname+location.search);
                let local = app.$cookies.get('auth.strategy');
                app.$cookies.remove(`auth._token.${local}`);
                localStorage.clear();
                app.store.commit("isLogin", true);
                return Promise.reject(response.data.message)
            }
          break
        default:
          break
      }
    },
    error => {
      if(process.client){
      }
      // if(error.response.status == 500){
      //   // http状态500，服务器内部错误，重定向到500页面
      //   redirect("/500.htm")
      // }
      // if(error.response.status == 404){
      //   // http状态500，请求API找不到，重定向到404页面
      //   redirect("/404.html")
      // }
      return Promise.reject(error)   // 返回接口返回的错误信息
    })
}
