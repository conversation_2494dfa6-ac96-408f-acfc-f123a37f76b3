<template>
  <div>
    <div class="header wrapper_1200">
      <span class="home">首页 > 订单详情 > </span>退回商品
    </div>
    <div class="refund wrapper_1200">
      <table class="table">
        <thead>
          <tr>
            <td>商品信息</td>
            <td>退货件数</td>
            <td>退款金额</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in orderData" :key="index">
            <td class="td">
              <table border="0">
                <tr>
                  <td>
                    <img :src="item.product.cart_info.product.image" />
                    <div>
                      <div class="name">
                        {{ item.product.cart_info.product.store_name }}<template v-if="item.product.cart_info.productAttr">
                          {{ item.product.cart_info.productAttr.sku }}</template>
                      </div>
                      <div v-if="item.product.cart_info.productAttr">
                        ￥{{ item.product.cart_info.productAttr.price }}<span>x{{ item.refund_num }}</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
            <td class="td">{{ item.refund_num }}</td>
            <td v-if="item.product.cart_info.productAttr" class="td">¥{{ item.product.cart_info.productAttr.price * item.refund_num }}</td>
          </tr>
        </tbody>
      </table>
      <client-only>
        <el-form ref="form" :model="form" label-width="80px">
            <div class="cont">
                <el-form-item label="物流公司">
                    <el-select v-model="form.express" placeholder="请选择物流公司">
                        <el-option
                        :label="item.label"
                        :value="item.label"
                        v-for="(item, index) in numArray"
                        :key="index"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="物流单号">
                    <el-input
                        placeholder="请输入物流单号"
                        v-model="form.number"
                        class="item-price"
                        style="width: 228px;"
                    ></el-input>
                </el-form-item>
                <el-form-item label="联系电话">
                    <el-input
                        placeholder="请输入联系电话"
                        v-model="form.phone"
                        class="item-price"
                        style="width: 228px;"
                    ></el-input>
                </el-form-item>
            </div>
        </el-form>
      </client-only>
      <div class="refundOrder">
          <el-button type="primary" @click="onSubmit">提交申请</el-button>
      </div>
    </div>
    <!-- 弹窗 -->
    <client-only>
      <el-dialog
        :modal="true"
        :visible.sync="isDialog"
        width="370px"
        :show-close="false"
      >
        <div class="refund-box">
          <img src="~assets/images/refund.png" alt="" />
          <p class="title">{{msg}}</p>
          <el-button type="primary" @click="subBtn">确认</el-button>
        </div>
      </el-dialog>
    </client-only>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message} from "element-ui";
export default {
  auth: "guest",
  data() {
    return {
      orderData: {},
      myHeaders: {},
      id: '',
      type: 1,
      numArray: [],
      isDialog: false,
      form: {
        express: '',
        number: '',
        phone: ''
      },
      msg: ''
    };
  },
  async asyncData({ app, query }) {
    return {
      id: query.id
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "退回商品-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    let local = this.$cookies.get("auth.strategy");
    this.myHeaders = {
      Authorization: this.$cookies.get(`auth._token.${local}`)
    };
    this.getOrderInfo();
    this.expressList();
  },
  methods: {
    getOrderInfo(){
        let that = this;
        that.$axios.get("/api/refund/detail/"+that.id).then(res => {
          that.orderData = res.data.refundProduct;
        }).catch(err => {
          return Message.error(err);
        });
    },
    // 获取物流公司信息
    expressList(){
        let that = this;
        that.$axios.get("/api/common/express").then(res => {
          that.numArray = res.data;
        }).catch(err => {
          return Message.error(err);
        });
    },
    onSubmit() {
      let that = this;
      if(!that.form.express){
        return Message.error("请选择物流公司");
      } else if (!that.form.number) {
        return Message.error("请输入快递单号");
      } else if (!that.form.phone) {
        return Message.error("请输入您的联系电话");
      } else if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.form.phone)) {
        return Message.error("请输入正确的联系电话");
      }else{
        that.$axios.post("/api/refund/back_goods/"+that.id,{
        delivery_type:that.form.express,
        delivery_id:that.form.number,
        delivery_phone:that.form.phone,
      }).then(res => {
              that.msg = res.message
              Message.success(res.message);
              that.isDialog = true;
          }).catch(err => {
              return Message.error(err);
          });
        }
    },
    subBtn() {
      this.isDialog = false;
      return this.$router.replace({
        path: "/user/refund_list",
        query: { type: 2 }
      });
    },
  }
};
</script>
<style lang="scss" scoped>
.header {
  height: 60px;
  line-height: 60px;
  color: #999999;
  .home {
    color: #282828;
  }
}
.refund {
  padding: 40px 20px 46px;
  background: #ffffff;
  .el-icon-plus {
    margin-top: 20px;
  }
  .table {
    width: 100%;
    border: 1px solid #efefef;
    border-collapse: collapse;
    thead {
      background: #efefef;
      td {
        height: 40px;
        font-size: 14px;
        text-align: center;
        color: #282828;
      }
    }
    tbody {
      .td {
        width: 219px;
        border: 1px solid #efefef;
        font-size: 14px;
        text-align: center;
        color: #282828;
        &:first-child {
          width: auto;
          padding: 20px 50px;
          text-align: left;
          span {
            margin-left: 10px;
            font-size: 12px;
            color: #b1b1b1;
          }
        }
      }
    }
    img {
      float: left;
      width: 70px;
      height: 70px;
      + div {
        margin-left: 80px;
      }
    }
    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      margin-bottom: 13px;
      overflow: hidden;
    }
  }
}
.el-form {
  margin-top: 20px;
  .cont {
    border: 1px solid #efefef;
    padding: 25px 20px;
  }
  .item-desc{
    color: #B5B5B5;
  }
  .item-border{
    padding-bottom: 25px;
    border-bottom: 1px dashed #EFEFEF;
  }
  > .el-form-item {
    margin-top: 25px;
    margin-bottom: 0;
    text-align: right;
  }
  .el-textarea {
    width: 820px;
  }
}
.el-form .item-price ::v-deep input{
  padding-right: 0;
}
.el-form ::v-deep .el-form-item__label{
  color: #282828;
}
.el-form ::v-deep .el-textarea__inner {
  border: none;
  background: #f7f7f7;
}
.el-form ::v-deep .el-upload--picture-card {
  width: 70px;
  height: 70px;
  border-style: solid;
  line-height: 68px;
}
.el-form ::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 70px;
  height: 70px;
}
.refundOrder{
  margin-top: 20px;
  text-align: right;
}
.refund-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    color: #e93323;
    font-size: 20px;
    margin-top: 20px;
  }
  span {
    margin: 10px 0 40px;
    color: #939393;
    font-size: 14px;
  }
  button {
    width: 150px;
    margin-top: 10px;
  }
}
</style>
