@charset "utf-8";
/* css reset www.admin10000.com */
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,button,textarea,p,blockquote,th,td { margin:0; padding:0; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;}
body { background: #F9F9F9; color:#282828; font-size:14px;font-family: 'Microsoft YaHei','微软雅黑,Arial', 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'sans-serif';}
/*td,th,caption { font-size:62.5%; }*/
h1, h2, h3, h4, h5, h6 { font-weight:normal; font-size:100%; }
address, caption, cite, code, dfn, em, strong, th, var { font-style:normal; font-weight:normal;}
a { color:#555; text-decoration:none; }
a:hover { text-decoration:none; }
img { border:none; }
ol,ul,li { list-style:none; }
input, textarea, select, button { font:14px Verdana,Helvetica,Arial,sans-serif; }
button{cursor: pointer;outline: none;}
table { border-collapse:collapse; }
html {overflow-y: scroll;}
/* css common */
.clearfix:after, .clearfix:before {content: ""; display: block; height:0; clear:both; visibility: hidden;}
.clearfix { *zoom:1; }
.pull-left{float: left;}
.pull-right{float: right;}
header, section, footer, aside, nav, main, article, figure {
  display: block;
}
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
/*按钮样式1*/
.set_2_button{position: relative;transition: all 1s;overflow:hidden;}
.set_2_button:before{content: "→";position: absolute;left: -100%;transition: all 0.5s;}
.set_2_button span{display: inline-block;transition: all 0.5s;}
.set_2_button:hover:before {left: 48%;transition: all 1s;}
.set_2_button:hover > span {transform: translateX(300%);transition: all 1s;}
/*按钮样式2*/
.set_4_button1 {
  position: relative;
  overflow: hidden;
  z-index: 0;
}
.set_4_button1.raised {
  -moz-transition: all 0.1s;
  -o-transition: all 0.1s;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  background: #666666;
}
.set_4_button1.raised:active {
  background: #515151;
  box-shadow: 0px 1px 1px #063e6b;
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
/*but样式3*/
.set_8_button {
  display: inline-block;
  transition: background 300ms;
}
.set_8_button.outline {
  overflow: hidden;
  position: relative;
}
.set_8_button.outline:before, .set_8_button.outline:after, .set_8_button.outline .lines:before,
.set_8_button.outline .lines:after {
  background-color: #cccccc;
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  top: 0;
  transition: transform 300ms;
  width: 100%;
}
.set_8_button.outline:before {
  bottom: 0;
  top: auto;
}
.set_8_button.outline .lines {
  display: block;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}
.set_8_button.outline .lines:before, .set_8_button.outline .lines:after {
  height: 100%;
  width:1px;
}
.set_8_button.outline .lines:after {
  left: auto;
  right: 0;
}
.set_8_button:hover:before {
  transform: translateX(-100%);
}
.set_8_button:hover:after {
  transform: translateX(100%);
}
.set_8_button:hover .lines:before {
  transform: translateY(-100%);
}
.set_8_button:hover .lines:after {
  transform: translateY(100%);
}
/*but样式4*/
.set_5_button {
  overflow: hidden;
  z-index: 1;
  position: relative;
}
.set_5_button:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 500%;
  height: 1000%;
  background: #000;
  filter:alpha(opacity=10);
  opacity: 0.1;
  z-index: -1;
  transform-origin: 0% 0%;
  transform: translateX(calc(20% - 25px)) translateY(10%) rotate(-45deg);
  transform: translateY(10%) translateX(16%) rotate(-45deg);
  transition: transform .3s;
}
.set_5_button:hover::after {
  transform: translateY(10%) translateX(-25px) rotate(-45deg);
}
.mask{background-color:#000;opacity:0.5;filter:alpha(opacity=50);position:fixed;top:0;right: 0;left:0;bottom:0;z-index:66;}
.line1{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.line2{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;}
.line4{word-break:break-all;display:-webkit-box;-webkit-line-clamp:4;-webkit-box-orient:vertical;overflow:hidden;}
