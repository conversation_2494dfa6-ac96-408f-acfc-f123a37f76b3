<template>
  <div class="logistics wrapper_1200">
    <div class="header"><span class="home"><nuxt-link :to="{path:'/'}">首页</nuxt-link> > </span>物流详情</div>
    <div class="section process" v-if="orderInfo.status >= 0">

      <div class="section-hd" v-if="orderInfo.status === 0 && orderInfo.order_type == 0">
        订单状态：待发货
      </div>
      <div class="section-hd" v-if="orderInfo.status === 1">
        订单状态：待收货
      </div>
      <div class="section-hd" v-if="orderInfo.status === 2">
        订单状态：待评价
      </div>
      <div class="section-hd" v-if="orderInfo.status === 3">
        订单状态：已完成
      </div>
      <div class="section-bd">
        <ul class="">
          <li class="past">
            <div class="line"></div>
            <div class="iconfont icon-webicon318"></div>
            <div class="iconfont icon-fukuan">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待付款</div>
              <div>{{ orderInfo.create_time }}</div>
            </div>
          </li>
          <li
            :class="{
              past:
                orderInfo.status > 0  || orderInfo.status >= -1,
              now:
                orderInfo.status == 0 || orderInfo.status === 9
            }"
          >
            <div class="line"></div>
            <div :class="[
                'iconfont',
                 orderInfo.status == 0 || orderInfo.status == 9 || orderInfo.status >= -1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"></div>
            <div class="iconfont icon-peihuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待发货</div>
            </div>
          </li>
          <li
            :class="{
              past:
                orderInfo.status >1,
              now:
                orderInfo.status == 1
            }"
          >
            <div class="line"></div>
            <div
              :class="[
                'iconfont',
                 orderInfo.status == 1
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-fahuo">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待收货</div>
            </div>
          </li>
          <li
            :class="{
              past:
                orderInfo.status > 2 || orderInfo.status == -1,
              now:
                orderInfo.status == 2
            }"
          >
            <div class="line"></div>
            <div
              :class="[
                'iconfont',
                 orderInfo.status >= 2
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-pingjia1">
              <div class="arrow"></div>
            </div>
            <div class="info">
              <div>待评价</div>
            </div>
          </li>
          <li
            :class="{
              past:
                orderInfo.status > 3 || orderInfo.status == -1,
              now:
                orderInfo.status == 3
            }"
          >
            <div
              :class="[
                'iconfont',
                orderInfo.status == 3
                  ? 'icon-webicon318'
                  : 'icon-weixuan'
              ]"
            ></div>
            <div class="iconfont icon-wancheng"></div>
            <div class="info">
              <div>已完成</div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="express">
        <div class="acea-row row-middle express">
             <div class="iconfont icon-wuliu"></div>
            <div class="text">
                <div>物流公司：{{ orderInfo.delivery_name }}</div>
                <div>
                快递单号：{{ orderInfo.delivery_id }}
                <button
                    v-clipboard:copy="orderInfo.delivery_id"
                    v-clipboard:success="onCopy"
                    v-clipboard:error="onError">
                    复制
                </button>
                </div>
            </div>
        </div>
      <div class="acea-row detail">
        <div class="goods">
          <img v-if="product.cart_info" :src="product.cart_info.product.image" />
          <ul>
            <li class="acea-row">
              <div>订单编号：</div>
              <div>{{ orderInfo.order_sn }}</div>
            </li>
            <li class="acea-row">
              <div>收货地址：</div>
              <div>{{ orderInfo.user_address }}</div>
            </li>
            <li class="acea-row">
              <div>买家留言：</div>
              <div>{{ orderInfo.mark || "-" }}</div>
            </li>
          </ul>
        </div>
        <div class="timeline">
          <ul v-if="expressList && expressList.length">
            <li
              class="acea-row"
              :class="index === 0 ? 'on' : ''"
              v-for="(item, index) in expressList"
              :key="index"
            >
              <div>{{ item.time }}</div>
              <div>{{ item.status }}</div>
            </li>
          </ul>
          <img v-else src="@/assets/images/noExpress.png" />
        </div>
      </div>
    </div>
    <div class="store-recommend">
        <div class="recommend-title">
            <div class="title">为你推荐</div>
        </div>
        <div class="goods acea-row row-middle" v-if="productslist.length">
            <div class="item" v-for="(item, index) in productslist" :key="index" v-if="index<5" @click="goDetail(item.product_id)">
                <div class="pictrue"><img :src="item.image"></div>
                <div class="name line2">{{item.store_name}}</div>
                <div class="money acea-row row-between-wrapper">
                    <div><span class="font-color">¥{{item.price}}</span></div>
                    <div>{{item.sales}}人付款</div>
                </div>
            </div>
        </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  auth: "guest",
  data() {
    return {
      product: {},
      orderInfo: {},
      orderData: {},
      expressList: [],
      productslist: [],
      page: 1,
      limit: 10,
      total: 0
    };
  },
  async asyncData({ app, query, error }) {
    try{
      const [express] = await Promise.all([
        app.$axios.post(`/api/order/express/${query.orderId}`)
      ]);
      return {
        product: express.data.order.orderProduct[0] || {},
        orderInfo: express.data.order,
        expressList: express.data.express
          ? express.data.express
          : []
      };
    }catch (e){
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "物流-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
      this.getRecommendList();
  },
  methods: {
    onCopy(e) {
      this.$message.success("复制成功");
    },
    onError(e) {
      this.$message.error("复制出错");
    },
    getRecommendList(){
        this.$axios.get('/api/product/spu/recommend',{
          params:{
            page: 1,
            limit: 5
          }
        }).then(res => {
          this.productslist = res.data.list
          this.total = res.data.count
        })
    },
    goDetail: function (id) {
        this.$router.push({ path: '/goods_detail/'+ id });
    },
    // 分页点击
    bindPageCur(data){
        this.page = data
        this.getRecommendList();
    }
  }
};
</script>

<style lang="scss" scoped>
.logistics {
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .order-list{
      .img-box {
        width: 120px;
        height: 120px;
        img {
            display: block;
            width: 100%;
            height: 100%;
        }
      }
      .bd {
        padding-right: 40px;
        border-bottom: 1px dashed #E1E1E1;
        cursor: pointer;
        .content {
          margin-top: 20px;
          .goods-item {
            display: flex;
            position: relative;
            margin-bottom: 20px;
            .info-txt {
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: center;
              width: 500px;
              margin-left: 24px;
              font-size: 14px;
              .info{
                font-size: 12px;
                color: #aaa;
                margin-top: 4px;
              }
              .price {
                margin-top: 15px;
              }
              .num {
                position: absolute;
                right: 0;
                top: 60%;
                transform: translateY(-50%);
                color: #999999;
              }
            }
          }
        }
    }
  }
  .express {
    padding: 18px 22px;
    background-color: #ffffff;
    .iconfont {
      font-size: 40px;
      color: #d0d0d0;
    }
    .text {
      flex: 1;
      margin-left: 22px;
      font-size: 13px;
      color: #282828;
      div {
        ~ div {
          margin-top: 14px;
        }

        button {
          width: 38px;
          height: 22px;
          border: 1px solid #d0d0d0;
          border-radius: 4px;
          margin-left: 18px;
          background: none;
          font-size: 12px;
        }
      }
    }
  }
}
.detail {
  margin-top: 14px;
  background-color: #ffffff;

  .goods {
    width: 330px;
    padding: 30px 22px;

    img {
      width: 112px;
      height: 112px;
      border-radius: 4px;
      vertical-align: middleF;
    }

    ul {
      margin-top: 20px;
      font-size: 13px;
      color: #282828;
    }

    li {
      ~ li {
        margin-top: 14px;
      }

      div {
        &:last-child {
          flex: 1;
        }
      }
    }
  }

  .timeline {
    flex: 1;
    padding: 30px 22px;
    border-left: 1px solid #efefef;
    font-size: 13px;
    color: #282828;

    .on {
      div {
        &:last-child {
          &::before {
            width: 12px;
            height: 12px;
            border-width: 3px;
            background-color: #e93323;
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 18px;
            height: 18px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            box-sizing: border-box;
            background-color: #ff877c;
            transform: translate(-50%, -50%);
          }
        }
      }
    }

    div {
      &:first-child {
        width: 233px;
        padding-right: 27px;
        text-align: right;
      }

      &:last-child {
        position: relative;
        flex: 1;
        padding-bottom: 20px;
        padding-left: 27px;
        border-left: 1px solid #d0d0d0;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 8px;
          height: 8px;
          border: 2px solid #ffffff;
          border-radius: 50%;
          box-sizing: border-box;
          background-color: #d0d0d0;
          transform: translate(-50%, -50%);
        }
      }
    }

    > img {
      width: 200px;
      margin: 32px auto 0;
    }
  }
}
.logistics .express .item {
  padding: 0 40px;
  position: relative;
}

.logistics .express .item .circular {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  position: absolute;
  top: -1px;
  left: 51.5px;
  background-color: #ddd;
}

.logistics .express .item .circular.on {
  background-color: #e93323;
}

.logistics .express .item .text.on-font {
  color: #e93323;
}

.logistics .express .item .text .data.on-font {
  color: #e93323;
}

.logistics .express .item .text {
  font-size: 14px;
  color: #666;
  width: 615px;
  border-left: 1px solid #e6e6e6;
  padding: 0 0 60px 38px;
}

.logistics .express .item .text.on {
  border-left-color: #f8c1bd;
}

.logistics .express .item .text .data {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.logistics .express .item .text .data .time {
  margin-left: 15px;
}
.store-recommend{
    background: #fff;
    margin-top: 15px;
    padding-bottom: 30px;
    .recommend-title{
         text-align: center;
         padding: 30px 0 20px 0;
         .title{
             color: #282828;
             font-size: 24px;
             font-weight: bold;
         }
     }
    .item{
        background-color: #fff;
        padding: 16px;
        width: 224px;
        height: 310px;
        margin: 20px 20px 0 0;
        cursor: pointer;
        &:nth-child(4n){
            margin-right: 0;
        }
        &:hover{
            box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
        }
        .pictrue{
            width: 192px;
            height: 192px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .money{
            margin-top: 12px;
            .font-color{
                font-weight: bold;
                font-size: 22px;
            }
            .y_money{
                font-size: 12px;
                color: #AAAAAA;
                text-decoration: line-through;
                margin-left: 8px;
            }
            .label{
                width: 20px;
                height: 20px;
                background: linear-gradient(330deg, rgba(231, 85, 67, 0.15) 0%, rgba(244, 103, 83, 0.15) 100%);
                font-size: 12px;
                text-align: center;
                line-height: 20px;
            }
        }
            .name{
                color: #5A5A5A;
                margin-top: 8px;
                height: 40px;
            }
            .bottom{
                font-size: 12px;
                color: #AAAAAA;
                margin-top: 10px;
            }
    }
}
.process {
  div {

    &.section-hd {
      padding: 26px 22px 0;
    }

    ul {
      padding: 27px 0 94px;

      &::after {
        content: "";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }

    li {
      position: relative;
      float: left;
      margin-top: 0;
      margin-left: 222px;

      &:first-child {
        margin-left: 111px;
      }

      .line {
        position: absolute;
        top: 50%;
        left: 16px;
        width: 226px;
        height: 4px;
        background: #c7c7c7;
        transform: translateY(-50%);
      }

      .iconfont {
        position: relative;
        width: auto;
        font-size: 18px;
        line-height: 1;
        color: #c7c7c7;

        + .iconfont {
          position: absolute;
          top: 50%;
          left: 50%;
          display: none;
          width: 40px;
          height: 40px;
          border: 4px solid #e93323;
          border-radius: 50%;
          background: #ffffff;
          transform: translate(-50%, -50%);
          font-size: 20px;
          line-height: 32px;
          text-align: center;
          color: #e93323;
        }
      }

      .arrow {
        position: absolute;
        top: 50%;
        left: 100%;
        display: none;
        width: 80px;
        height: 16px;
        background: #e93323;
        transform: translateY(-50%);

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 100%;
          border-width: 8px;
          border-style: solid;
          border-color: transparent transparent transparent #e93323;
        }
      }

      .info {
        position: absolute;
        top: 42px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        text-align: center;
        color: #9a9a9a;
        width: 100px;

        div {
          &:first-child {
            margin-bottom: 4px;
            font-size: 16px;
            color: #282828;
          }
        }
      }

      &.past {
        .line {
          background: rgba(233, 51, 35, 0.6);
        }

        .iconfont {
          color: #e93323;
          font-weight: bold;
          font-size: 22px;
          left: -3px;
        }
      }

      &.now {
        .info {
          div {
            &:first-child {
              color: #e93323;
            }
          }
        }

        .iconfont {
          + .iconfont {
            display: block;
          }
        }

        .arrow {
          display: block;
        }
      }
    }
  }
}
</style>
