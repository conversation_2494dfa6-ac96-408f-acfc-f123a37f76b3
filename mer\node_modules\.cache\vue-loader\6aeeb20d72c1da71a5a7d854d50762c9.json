{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue?vue&type=style&index=0&id=7037b1e9&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue", "mtime": 1750488046426}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\css-loader\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n.pictrueBox {\r\n  display: inline-block;\r\n}\r\n.pictrueTab {\r\n  width: 40px !important;\r\n  height: 40px !important;\r\n}\r\n.pictrue {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 1px dotted rgba(0, 0, 0, 0.1);\r\n  margin-right: 15px;\r\n  display: inline-block;\r\n  position: relative;\r\n  cursor: pointer;\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  .btndel {\r\n    position: absolute;\r\n    z-index: 1;\r\n    width: 20px !important;\r\n    height: 20px !important;\r\n    left: 46px;\r\n    top: -4px;\r\n  }\r\n}\r\n\r\n.uploadCont {\r\n  width: 60px;\r\n  height: 60px;\r\n  margin-right: 10px;\r\n}\r\n.upLoadPicBox {\r\n  width: 60px;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  border: 1px dotted rgba(0, 0, 0, 0.1);\r\n  border-radius: 2px;\r\n  cursor: pointer;\r\n}\r\n.upLoad {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.02);\r\n}\r\n.cameraIconfont {\r\n  font-size: 24px;\r\n  color: #898989;\r\n}\r\n.ml14 {\r\n  margin-left: 14px;\r\n}\r\n.proCoupon {\r\n  margin-bottom: 20px;\r\n}\r\n.desc {\r\n  color: #999;\r\n}\r\n.tabNumWidth ::v-deep .el-input-number--medium {\r\n  width: 100px;\r\n}\r\n.tabNumWidth ::v-deep .el-input-number__increase {\r\n  width: 20px !important;\r\n  font-size: 12px !important;\r\n}\r\n.tabNumWidth ::v-deep .el-input-number__increase {\r\n  width: 20px !important;\r\n  font-size: 12px !important;\r\n}\r\n.tabNumWidth ::v-deep .el-input-number__decrease {\r\n  width: 20px !important;\r\n  font-size: 12px !important;\r\n}\r\n.tabNumWidth ::v-deep .el-input-number--medium .el-input__inner {\r\n  padding-left: 25px !important;\r\n  padding-right: 25px !important;\r\n}\r\n.priceBox ::v-deep .el-input__inner {\r\n  padding-right: 15px;\r\n}\r\n.el-input-number.is-controls-right .el-input__inner {\r\n  padding-right: 15px !important;\r\n}\r\n.tabNumWidth ::v-deep .priceBox .el-input-number__decrease,\r\n.tabNumWidth ::v-deep .priceBox .el-input-number__increase {\r\n  display: none;\r\n}\r\n.tabNumWidth ::v-deep .priceBox.el-input-number--small {\r\n  width: 90px !important;\r\n}\r\n.tabNumWidth ::v-deep .el-select {\r\n  width: 120px;\r\n}\r\n.tabNumWidth ::v-deep thead {\r\n  line-height: normal !important;\r\n}\r\n.tabNumWidth ::v-deep .cell {\r\n  line-height: normal !important;\r\n  text-overflow: clip !important;\r\n}\r\n.input-number-with-text {\r\n  position: relative;\r\n}\r\n.input-number-with-text ::v-deep .el-input__inner {\r\n  padding-right: 30px;\r\n}\r\n.input-number-with-text::after {\r\n  content: \"元\";\r\n  display: inline-block;\r\n  color: #ccc;\r\n  font-size: 13px;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 2px;\r\n}\r\n", null]}