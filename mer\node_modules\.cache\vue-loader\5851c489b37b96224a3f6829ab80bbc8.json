{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue?vue&type=template&id=7037b1e9&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productMarket.vue", "mtime": 1750488046426}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<el-row>\n  <el-col :span=\"24\">\n    <el-form-item :label=\"$t('商品推荐：')\">\n      <el-checkbox\n        v-model=\"formValidate.is_good\"\n        :true-label=\"1\"\n        :false-label=\"0\"\n        :label=\"$t('店铺推荐')\"\n      />\n      <div class=\"form-tip\">{{ $t('设置后，该商品会在店铺中其他商品详情店铺推荐备选列表中展示') }}</div>\n    </el-form-item>\n  </el-col>\n  <el-col :span=\"24\">\n    <el-form-item :label=\"$t('关联推荐：')\">\n      <div class=\"acea-row row-middle\">\n        <div class=\"acea-row\">\n          <div\n            v-for=\"(item, index) in goodList\"\n            :key=\"`good-${index}`\"\n            class=\"pictrue\"\n          >\n            <img :src=\"item.image\" />\n            <i class=\"el-icon-error btndel\" @click=\"deleteRecommend(index)\" />\n          </div>\n          <div class=\"uploadCont\" v-if=\"goodList.length < 18\">\n            <div class=\"upLoadPicBox\" @click=\"openRecommend\">\n              <div class=\"upLoad\">\n                <i class=\"el-icon-camera cameraIconfont\" />\n              </div>\n            </div>\n          </div>\n        </div>\n        <el-popover\n          placement=\"bottom\"\n          title=\"\"\n          min-width=\"200\"\n          trigger=\"hover\"\n        >\n          <img\n            :src=\"`${baseUrl}/static/images/store-recommend.png`\"\n            style=\"height:270px;\"\n            alt=\"\"\n          />\n          <el-button\n            type=\"text\"\n            slot=\"reference\"\n            style=\"font-size: 12px;\"\n            class=\"ml14\"\n            >{{ $t('查看示例') }}</el-button\n          >\n        </el-popover>\n      </div>\n      <div class=\"form-tip\">{{ $t('设置后，该商品详情页【店铺推荐】会展示选中的商品') }}</div>\n    </el-form-item>\n  </el-col>\n  <el-col :span=\"24\">\n    <el-form-item v-if=\"deductionStatus > 0\" :label=\"$t('积分抵扣：')\">\n      <el-radio-group\n        v-model=\"deduction_set\"\n        @change=\"changeIntergral(deduction_set)\"\n      >\n        <el-radio :label=\"1\" class=\"radio\">{{ $t('单独设置') }}</el-radio>\n        <el-radio :label=\"-1\">{{ $t('默认设置') }}</el-radio>\n      </el-radio-group>\n      <div v-if=\"deduction_set == 1\">\n        <el-input-number\n          v-model=\"formValidate.integral_rate\"\n          :min=\"0\"\n          size=\"small\"\n          controls-position=\"right\"\n          :placeholder=\"$t('请输入抵扣比例')\"\n        />\n        %\n      </div>\n      <span\n        v-if=\"deduction_set == -1\"\n        class=\"form-tip\"\n        style=\"color: #F56464;\"\n        >（店铺统一设置，抵扣比例{{ deduction_ratio_rate }}%）</span\n      >\n    </el-form-item>\n  </el-col>\n  <el-col :span=\"24\">\n    <el-form-item :label=\"$t('商品赠券：')\" class=\"proCoupon\">\n      <div class=\"acea-row\">\n        <el-tag\n          v-for=\"(tag, index) in formValidate.couponData\"\n          :key=\"index\"\n          class=\"mr10\"\n          closable\n          :disable-transitions=\"false\"\n          @close=\"handleCloseCoupon(tag)\"\n          >{{ $t(tag.title) }}\n        </el-tag>\n        <el-button class=\"mr15\" size=\"mini\" @click=\"addCoupon\"\n          >{{ $t('选择优惠券') }}</el-button\n        >\n        <div class=\"form-tip\">{{ $t('设置购买该商品可默认赠送的优惠券') }}</div>\n      </div>\n    </el-form-item>\n  </el-col>\n  <el-col :span=\"24\">\n    <el-form-item :label=\"$t('开启礼包：')\">\n      <el-radio-group\n        v-model=\"formValidate.is_gift_bag\"\n        :disabled=\"$route.params.id ? true : false\"\n      >\n        <el-radio :label=\"0\" class=\"radio\">{{ $t('否') }}</el-radio>\n        <el-radio :label=\"1\">{{ $t('是') }}</el-radio>\n      </el-radio-group>\n      <div class=\"form-tip\">{{ $t('1. 选择开启礼包后，不可修改') }}</div>\n      <div class=\"form-tip\">\n        2.\n        用户购买该分销礼包商品后，可自动成为分销员（即已成为分销员的用户在移动端看不到该分销礼包商品）\n      </div>\n      <div class=\"form-tip\">\n        3.\n        该商品设置为分销礼包后会展示在平台后台的【分销】-【分销礼包】（即不会展示在平台后台-【商品列表】）\n      </div>\n    </el-form-item>\n  </el-col>\n\n  <el-col :span=\"24\">\n    <el-form-item\n      v-if=\"extensionStatus > 0\"\n      :label=\"$t('佣金设置：')\"\n      props=\"extension_type\"\n    >\n      <el-radio-group\n        v-model=\"formValidate.extension_type\"\n        @change=\"onChangetype(formValidate.extension_type)\"\n      >\n        <el-radio :label=\"1\" class=\"radio\">{{ $t('单独设置') }}</el-radio>\n        <el-radio :label=\"0\">{{ $t('默认设置') }}</el-radio>\n      </el-radio-group>\n      <span\n        v-if=\"formValidate.extension_type == 0\"\n        class=\"form-tip\"\n        style=\"color: #F56464;\"\n        >（平台设置，一级佣金{{ extension_one_rate }}%，二级佣金{{\n          extension_two_rate\n        }}%）</span\n      >\n    </el-form-item>\n  </el-col>\n  <el-col :span=\"24\">\n    <el-form-item\n      v-if=\"open_svip\"\n      :label=\"$t('付费会员价：')\"\n      props=\"svip_price_type\"\n    >\n      <el-radio-group\n        v-model=\"formValidate.svip_price_type\"\n        @change=\"onChangeSpecs(formValidate.svip_price_type)\"\n      >\n        <el-radio :label=\"0\" class=\"radio\">{{ $t('不设置') }}</el-radio>\n        <el-radio :label=\"2\">{{ $t('自定义设置') }}</el-radio>\n        <el-radio :label=\"1\" class=\"radio\">{{ $t('默认设置') }}</el-radio>\n      </el-radio-group>\n      <span class=\"form-tip\" style=\"color: #F56464;\"\n        >（店铺统一设置，{{ svip_rate * 10 }}折）</span\n      >\n    </el-form-item>\n  </el-col>\n  <el-col\n    v-if=\"formValidate.spec_type === 1\"\n    :xl=\"24\"\n    :lg=\"24\"\n    :md=\"24\"\n    :sm=\"24\"\n    :xs=\"24\"\n  >\n    <el-form-item\n      v-if=\"\n        (open_svip && formValidate.svip_price_type == 2) ||\n          (extensionStatus > 0 && formValidate.extension_type == 1)\n      \"\n      :label=\"$t('批量设置：')\"\n    >\n      <div class=\"acea-row\">\n        <div\n          v-if=\"open_svip && formValidate.svip_price_type == 2\"\n          class=\"mr15 acea-row row-middle\"\n        >\n          <span>{{ $t('会员价：') }}</span>\n          <el-input-number\n            v-model=\"manyVipPrice\"\n            :min=\"0\"\n            size=\"small\"\n            style=\"width:150px;\"\n            controls-position=\"right\"\n          />\n        </div>\n        <template\n          v-if=\"extensionStatus > 0 && formValidate.extension_type == 1\"\n        >\n          <div class=\"mr15\">\n            <span>{{ $t('一级返佣：') }}</span>\n            <el-input-number\n              v-model=\"manyBrokerage\"\n              :min=\"0\"\n              size=\"small\"\n              class=\"input-number-with-text\"\n              controls-position=\"right\"\n            />\n          </div>\n          <div class=\"mr15\">\n            <span>{{ $t('二级返佣：') }}</span>\n            <el-input-number\n              v-model=\"manyBrokerageTwo\"\n              :min=\"0\"\n              size=\"small\"\n              class=\"input-number-with-text\"\n              controls-position=\"right\"\n            />\n          </div>\n        </template>\n        <div>\n          <el-button type=\"primary\" size=\"small\" @click=\"batchSet\"\n            >{{ $t('批量设置') }}</el-button\n          >\n        </div>\n      </div>\n    </el-form-item>\n  </el-col>\n  <el-col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n    <!-- 单规格表格-->\n    <el-form-item v-if=\"formValidate.spec_type === 0\">\n      <el-table :data=\"OneattrValue\" border class=\"tabNumWidth\" size=\"mini\">\n        <el-table-column align=\"center\" :label=\"$t('图片')\" min-width=\"80\">\n          <template slot-scope=\"scope\">\n            <div class=\"upLoadPicBoxspecPictrue\">\n              <div class=\"pictrue tabPic\" v-if=\"scope.row.image\">\n                <img :src=\"scope.row.image\" />\n              </div>\n              <div v-else>\n                --\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-for=\"(item, iii) in specValue\"\n          :key=\"iii\"\n          :label=\"formThead[iii] && formThead[iii].title\"\n          align=\"center\"\n          min-width=\"110\"\n        >\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row[iii] }}</div>\n          </template>\n        </el-table-column>\n        <template v-if=\"formValidate.svip_price_type != 0\">\n          <el-table-column align=\"center\" :label=\"$t('付费会员价')\" min-width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.svip_price\"\n                :min=\"0\"\n                size=\"small\"\n                :disabled=\"formValidate.svip_price_type == 1\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n        </template>\n        <template v-if=\"formValidate.extension_type === 1\">\n          <el-table-column\n            align=\"center\"\n            :label=\"$t('一级返佣(元)')\"\n            min-width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.extension_one\"\n                :min=\"0\"\n                size=\"small\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n          <el-table-column\n            align=\"center\"\n            :label=\"$t('二级返佣(元)')\"\n            min-width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.extension_two\"\n                :min=\"0\"\n                size=\"small\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n        </template>\n      </el-table>\n    </el-form-item>\n    <!-- 多规格表格-->\n    <el-form-item\n      v-if=\"formValidate.spec_type === 1\"\n      class=\"labeltop\"\n      :label=\"$t('规格列表：')\"\n    >\n      <el-table\n        ref=\"specsTable\"\n        :data=\"ManyAttrValue.slice(1)\"\n        border\n        class=\"tabNumWidth\"\n        size=\"small\"\n        key=\"2\"\n      >\n        <template v-if=\"manyTabDate\">\n          <el-table-column\n            v-for=\"(item, iii) in manyTabDate\"\n            :key=\"iii\"\n            align=\"center\"\n            :label=\"manyTabTit[iii].title\"\n            min-width=\"80\"\n          >\n            <template slot-scope=\"scope\">\n              <div>\n                <span>{{ scope.row.detail[manyTabTit[iii].title] }}</span>\n              </div>\n            </template>\n          </el-table-column>\n        </template>\n        <el-table-column align=\"center\" :label=\"$t('图片')\" min-width=\"80\">\n          <template slot-scope=\"scope\">\n            <div class=\"upLoadPicBox specPictrue\">\n              <div\n                class=\"pictrue tabPic\"\n                v-if=\"scope.row.image || scope.row.pic\"\n              >\n                <img :src=\"scope.row.image || scope.row.pic\" />\n              </div>\n              <div v-else>\n                --\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-for=\"(item, iii) in specValue\"\n          :key=\"iii\"\n          :label=\"formThead[iii].title\"\n          align=\"center\"\n          min-width=\"110\"\n        >\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row[iii] }}</div>\n          </template>\n        </el-table-column>\n        <template v-if=\"formValidate.svip_price_type != 0\">\n          <el-table-column align=\"center\" :label=\"$t('付费会员价')\" min-width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.svip_price\"\n                :min=\"0\"\n                size=\"small\"\n                :disabled=\"formValidate.svip_price_type == 1\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n        </template>\n        <template v-if=\"formValidate.extension_type === 1\">\n          <el-table-column\n            key=\"1\"\n            align=\"center\"\n            :label=\"$t('一级返佣(元)')\"\n            min-width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.extension_one\"\n                :min=\"0\"\n                size=\"small\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n          <el-table-column\n            key=\"2\"\n            align=\"center\"\n            :label=\"$t('二级返佣(元)')\"\n            min-width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-input-number\n                v-model=\"scope.row.extension_two\"\n                :min=\"0\"\n                size=\"small\"\n                class=\"priceBox\"\n                controls-position=\"right\"\n              />\n            </template>\n          </el-table-column>\n        </template>\n      </el-table>\n    </el-form-item>\n  </el-col>\n</el-row>\n", null]}