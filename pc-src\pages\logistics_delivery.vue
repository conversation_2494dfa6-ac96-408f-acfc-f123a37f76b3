<template>
  <div class="logistics wrapper_1200">
    <div class="header"><span class="home"><nuxt-link :to="{path:'/'}">首页</nuxt-link> > </span>物流详情</div>
    <div class="express">
      <div class="acea-row detail">
        <div class="goods">
          <div>
            <div class="title">订单编号：{{ deliveryInfo.order_sn }}</div>
              
              <div v-if="status == 0 || status == 2" class="order_status">
                <div class="name">{{status == 0 ? '待接单' : '待取货'}}</div>
                <div class="desc">等待配送员接单完成后开始配送</div>
              </div>
              <div v-if="code && (status != 0 && status !=2)" class="order_status">
                <div class="name">{{code}}</div>
                <div class="desc">稍后请将收货码告诉配送员</div>
              </div>
            </div>
        </div>
        <div class="timeline">
          <ul v-if="expressList && expressList.length">
            <li
              class="acea-row"
              :class="index === 0 ? 'on' : ''"
              v-for="(item, index) in expressList"
              :key="index"
            >
              <div>{{ item.change_time }}</div>
              <div>{{ item.change_message }}</div>
            </li>
          </ul>
          <img v-else src="@/assets/images/noExpress.png" />
        </div>
      </div>
    </div>
    <div class="delivery-info">
      <div class="info-item">
        <div class="title">配送信息</div>
        <div class="info">
          <span>
          <img src="../assets/images/delivery_man.png" />
          </span>
        </div>
        <div class="info">
          <span>配送员：</span>
          {{deliveryInfo.delivery_name || '-'}}
        </div>
        <div class="info">
          <span>联系电话：</span>
          {{deliveryInfo.delivery_id || '-'}}
        </div>
      </div>
      <div class="info-item">
        <div class="title">收货信息</div>
        <div class="info">
          <span>收货人：</span>
          {{deliveryInfo.real_name}}
        </div>
        <div class="info">
          <span>联系电话：</span>
          {{deliveryInfo.user_phone}}
        </div>
        <div class="info">
          <span>收货地址：</span>
          {{deliveryInfo.user_address}}
        </div>
      </div>
      <div class="info-item">
        <div class="title">付款信息</div>
        <div class="info">
          <span>付款方式：</span>
          {{ deliveryInfo.pay_type == 0 ? "余额支付" : (deliveryInfo.pay_type == 1 || deliveryInfo.pay_type == 2 || deliveryInfo.pay_type == 3) ? "微信支付" : "支付宝支付" }}
        </div>
        <div class="info">
          <span>下单时间：</span>
          {{ deliveryInfo.create_time }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  auth: "guest",
  data() {
    return {
      deliveryInfo: {},
      orderData: {},
      expressList: [],
      code: '',
      status: 0,
    };
  },
  async asyncData({ app, query, error }) {
    try{
      const [express] = await Promise.all([
        app.$axios.get(`/api/order/delivery/${query.orderId}`)
      ]);
      return {
        deliveryInfo: express.data.storeOrder,
        code: express.data.finish_code,
        status: express.data.status,
        expressList: express.data.storeOrderStatus
          ? express.data.storeOrderStatus
          : []
      };
    }catch (e){
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "物流-"+this.$store.state.titleCon
    }
  },
  beforeMount() {
    
  },
  methods: {
    
  }
};
</script>

<style lang="scss" scoped>
.logistics {
  .header {
    height: 60px;
    line-height: 60px;
    color: #999999;
    .home {
      color: #282828;
    }
  }
  .express {
    padding: 18px 22px;
    background-color: #ffffff;
    .iconfont {
      font-size: 40px;
      color: #d0d0d0;
    }
    .text {
      flex: 1;
      margin-left: 22px;
      font-size: 13px;
      color: #282828;
      div {
        ~ div {
          margin-top: 14px;
        }

        button {
          width: 38px;
          height: 22px;
          border: 1px solid #d0d0d0;
          border-radius: 4px;
          margin-left: 18px;
          background: none;
          font-size: 12px;
        }
      }
    }
  }
}
.delivery-info{
  margin-top: 15px;
  display: flex;
  background: #ffffff;
  padding: 20px 0;
  .info-item{
    width: 33.33%;
    padding: 0 22px;
    position: relative;
    color: #282828;
    &::after{
      content: "";
      display: block;
      width: 1px;
      height: 100%;
      background: #EFEFEF;
      position: absolute;
      top: 0;
      right: 0;
    }
    .title{
      font-size: 16px;
      line-height: 32px;
    }
    .info{
      font-size: 13px;
      margin-top: 16px;
      display: flex;
      span{
        display: inline-block;
        width: 70px;
      }
      img{
        width: 40px;
        height: 40px;
      }
    }
  }
}
.detail {
  margin-top: 14px;
  background-color: #ffffff;
  .goods {
    width: 375px;
    padding: 30px 22px;
    text-align: center;
    display: flex;
    align-items: center;
    .title{
      font-size: 13px;
      color: #999999;
    }
    .order_status{
      margin-top: 50px;
      .name{
        color: #E93323;
        font-size: 36px;
        font-weight: bold;
      }
      .desc{
        color: #282828;
        font-size: 18px;
        margin-top: 15px;
      }
    }
  }

  .timeline {
    flex: 1;
    padding: 30px 22px;
    border-left: 1px solid #efefef;
    font-size: 13px;
    color: #282828;

    .on {
      div {
        &:last-child {
          &::before {
            width: 12px;
            height: 12px;
            border-width: 3px;
            background-color: #e93323;
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 18px;
            height: 18px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            box-sizing: border-box;
            background-color: #ff877c;
            transform: translate(-50%, -50%);
          }
        }
      }
    }

    div {
      &:first-child {
        width: 233px;
        padding-right: 27px;
        text-align: right;
      }

      &:last-child {
        position: relative;
        flex: 1;
        padding-bottom: 20px;
        padding-left: 27px;
        border-left: 1px solid #d0d0d0;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 8px;
          height: 8px;
          border: 2px solid #ffffff;
          border-radius: 50%;
          box-sizing: border-box;
          background-color: #d0d0d0;
          transform: translate(-50%, -50%);
        }
      }
    }

    > img {
      width: 200px;
      margin: 32px auto 0;
    }
  }
}
.logistics .express .item {
  padding: 0 40px;
  position: relative;
}

.logistics .express .item .circular {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  position: absolute;
  top: -1px;
  left: 51.5px;
  background-color: #ddd;
}

.logistics .express .item .circular.on {
  background-color: #e93323;
}

.logistics .express .item .text.on-font {
  color: #e93323;
}

.logistics .express .item .text .data.on-font {
  color: #e93323;
}

.logistics .express .item .text {
  font-size: 14px;
  color: #666;
  width: 615px;
  border-left: 1px solid #e6e6e6;
  padding: 0 0 60px 38px;
}

.logistics .express .item .text.on {
  border-left-color: #f8c1bd;
}

.logistics .express .item .text .data {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.logistics .express .item .text .data .time {
  margin-left: 15px;
}

.process {
  div {

    &.section-hd {
      padding: 26px 22px 0;
    }

    ul {
      padding: 27px 0 94px;

      &::after {
        content: "";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }

    li {
      position: relative;
      float: left;
      margin-top: 0;
      margin-left: 222px;

      &:first-child {
        margin-left: 111px;
      }

      .line {
        position: absolute;
        top: 50%;
        left: 16px;
        width: 226px;
        height: 4px;
        background: #c7c7c7;
        transform: translateY(-50%);
      }

      .iconfont {
        position: relative;
        width: auto;
        font-size: 18px;
        line-height: 1;
        color: #c7c7c7;

        + .iconfont {
          position: absolute;
          top: 50%;
          left: 50%;
          display: none;
          width: 40px;
          height: 40px;
          border: 4px solid #e93323;
          border-radius: 50%;
          background: #ffffff;
          transform: translate(-50%, -50%);
          font-size: 20px;
          line-height: 32px;
          text-align: center;
          color: #e93323;
        }
      }

      .arrow {
        position: absolute;
        top: 50%;
        left: 100%;
        display: none;
        width: 80px;
        height: 16px;
        background: #e93323;
        transform: translateY(-50%);

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 100%;
          border-width: 8px;
          border-style: solid;
          border-color: transparent transparent transparent #e93323;
        }
      }

      .info {
        position: absolute;
        top: 42px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        text-align: center;
        color: #9a9a9a;
        width: 100px;
        div {
          &:first-child {
            margin-bottom: 4px;
            font-size: 16px;
            color: #282828;
          }
        }
     
      }

      &.past {
        .line {
          background: rgba(233, 51, 35, 0.6);
        }

        .iconfont {
          color: #e93323;
          font-weight: bold;
          font-size: 22px;
          left: -3px;
        }
      }

      &.now {
        .info {
          div {
            &:first-child {
              color: #e93323;
            }
          }
        }

        .iconfont {
          + .iconfont {
            display: block;
          }
        }

        .arrow {
          display: block;
        }
      }
    }
  }
}
</style>
