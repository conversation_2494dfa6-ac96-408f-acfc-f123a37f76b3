<template>
  <div class="store-wrapper">
    <div class="store-count">
      <div class="store-banner" v-if="swiperData.length">
        <div v-swiper:mySwiper="swiperOption">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in swiperData" :key="index">
              <img :src="item.img" />
            </div>
          </div>
          <div class="swiper-pagination paginationBanner" slot="pagination"></div>
          <div class="swiper-button-prev" slot="pagination"></div>
          <div class="swiper-button-next" slot="pagination"></div>
        </div>
      </div>
      <div class="store-recommend">
        <div class="recommend-title">
          <div class="title">店铺推荐</div>
          <span class="subTitle">大家都在买，热销榜单TOP15</span>
        </div>
        <div class="goods acea-row row-middle" v-if="productslist.length">
          <div class="item" v-for="(item, index) in productslist" :key="index" @click="goDetail(item)">
            <div class="pictrue">
              <img :src="item.image">
              <div v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></div>
            </div>
            <div class="money acea-row row-between-wrapper">
              <div v-if="item.show_svip_info.show_svip_price && item.svip_price" class="svip acea-row">
                <span class="font-color">¥{{item.svip_price}}</span>
                <img src="@/assets/images/svip.png">
              </div>  
              <div v-else><span class="font-color">¥{{item.price}}</span></div>
              <div class="label" v-if="item.issetCoupon">券</div>
            </div> 
            <div class="name line2"><span v-if="item.product_type == 1" class="trader">秒杀</span>{{item.store_name}}</div>
            <div class="bottom acea-row row-between-wrapper">
              <span>{{item.sales}}人付款</span>
              <span>{{item.rate}}分</span>
            </div>
          </div>
        </div>
        <el-pagination v-if="total > 0" :hide-on-single-page="total <= limit " :page-size="total" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
        <div v-if="!productslist.length">
          <div class="noGoods">
            <div class="pictrue">
              <img src="../../assets/images/noGoods.png">
            </div>
            <div class="name">亲，该店铺暂无推荐商品哦~</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
    export default {
      name: "storeHome",
      auth: "guest",
      data(){
        return {
            swiperData: [],
            swiperOption: {
              pagination: {
                el: ".paginationBanner",
                clickable: true
              },
              navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev"
              },
              autoplay: {
                disableOnInteraction: false,
                delay: 1000
              },
              loop: true,
              speed: 1000,
              observer: true,
              observeParents: true
            },
            swiperScroll: {
                navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev"
                },
                freeMode: true,
                freeModeMomentum: false,
                slidesPerView: "auto",
                observer: true,
                observeParents: true
            },
         productslist:[],
          page: 1, //代表页面的初始页数
          limit:12,
          mer_cate_id: '',
          total: ''
        }
      },
    async asyncData({ query }) {
        return {
            id: query.id,
            mer_cate_id: query.cateId
        };
    },
    watch: {
        // "$route": function(newVal,oldVal){
        //     debugger
        //     this.mer_cate_id = newVal.query.cateId
        //     this.getList();
        // }
      },
      fetch({ store }) {
        store.commit("isBanner", false);
        store.commit("isHeader", true);
        store.commit("isFooter", true);
      },
      head() {
        return {
          title: "店铺-"+this.$store.state.titleCon
        }
      },
      beforeMount() {
        this.getList();
        this.getMerConfig();
      },
      methods:{
        //获取商户基础数据
        getMerConfig: function () {
          this.$axios.get('/api/pc/mer_config/' + this.id).then(res => {
            this.swiperData =  res.data.banner;
          })
        },
        /**获取推荐列表 */
        getList() {
          this.$axios.get('/api/product/spu/merchant/'+this.id,{
            params:{
              page:this.page,
              limit:this.limit,
              common: 1,
              mer_cate_id: this.mer_cate_id ? this.mer_cate_id : ''
            }
          }).then(res => {
            this.productslist = res.data.list
            this.total = res.data.count
          })
        },
        goDetail: function (item) {
          if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
          }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
          }
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getList();
        },
      }
    }
</script>
<style lang="scss" scoped>
.store-banner .swiper-pagination{bottom:14px;text-align: right;padding-right: 20px;}
::v-deep .store-banner .swiper-pagination-bullet{width: 34px;height: 2px;background: #FFFFFF;opacity: 0.8;border-radius: 1px;}
::v-deep .store-banner .swiper-pagination-bullet-active{opacity:1;height: 3px;border-radius: 3px;}
.store-banner .swiper-button-next{background-image:none;width: 46px;height: 46px;border-radius: 100%;background-size: 100% 100%;right: 17px;background-color: rgba(0,0,0,.2);}
.store-banner .swiper-button-prev{background-image:none;width: 46px;height: 46px;border-radius: 100%;background-size: 100% 100%;left:17px;background-color: rgba(0,0,0,.2);}
.store-banner:hover .swiper-button-next{background-image:url("../../assets/images/right.png");}
.store-banner:hover .swiper-button-prev{background-image:url("../../assets/images/left.png");}
.trader{
  color: #fff;
  background-color: #e93323;
  display: inline-block;
  width: 32px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 2px;
  margin-right: 5px;
  font-size: 12px;
}
 .store-count{
     .store-banner,.swiper-container{
         width: 956px;
         height: 330px;
        img{
            width: 100%;
            height: 100%;
        }
     }
     .recommend-title{
         text-align: center;
         padding: 40px 0 30px 0;
         .title{
             color: #282828;
             font-size: 24px;
             font-weight: bold;
         }
         .subTitle{
             display: inline-block;
             color: #999999;
             margin-top: 15px;
         }
     }
     .store-recommend{
        .item{
            background-color: #fff;
            padding: 16px;
            width: 224px;
            margin: 20px 20px 0 0;
            cursor: pointer;
            &:nth-child(4n){
                margin-right: 0;
            }
            &:hover{
                box-shadow: 0 3px 20px rgba(0, 0, 0, 0.06);
            }
            .pictrue{
              width: 192px;
              height: 192px;
              position: relative;
              .border-picture {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: center/cover no-repeat;
              }
              img{
                  width: 100%;
                  height: 100%;
              }
            }
            .top{
              margin-top: 16px;
              .font-color{
                font-size: 22px;
                font-weight: bold;
              }
              .y_money{
                font-size: 12px;
                color: #AAAAAA;
                text-decoration: line-through;
                margin-left: 8px;
              }
              .label{
                width: 20px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                background: linear-gradient(330deg, rgba(231, 85, 67, 0.14) 0%, rgba(244, 103, 83, 0.14) 100%);
                font-size:12px;
                color: #E93323;
              }
            }
            .info{
              margin-top: 8px;
              color: #5A5A5A;
            }
            .bottom{
              font-size: 12px;
              color: #AAAAAA;
              margin-top: 10px;
            }
           .svip{
              align-items: center;
              margin-top: 3px;
              img{
                width: 35px;
                height: 15px;
                margin: 4px 0 0 7px;
              }
            }
            .money{
                margin-top: 12px;
            .font-color{
                font-weight: bold;
                font-size: 22px;
            }
            .y_money{
                font-size: 12px;
                color: #AAAAAA;
                text-decoration: line-through;
                margin-left: 8px;
            }
            .label{
                width: 20px;
                height: 20px;
                background: linear-gradient(330deg, rgba(231, 85, 67, 0.15) 0%, rgba(244, 103, 83, 0.15) 100%);
                font-size: 12px;
                text-align: center;
                line-height: 20px;
                color: #E93323;
            }
            }
            .name{
                color: #5A5A5A;
                margin-top: 8px;
                height: 40px;
            }
            .bottom{
                font-size: 12px;
                color: #AAAAAA;
                margin-top: 10px;
            }
      }
     }
   .noGoods{
     text-align: center;
     .pictrue{
       width: 274px;
       height: 174px;
       margin: 130px auto 0 auto;
       img{
         width: 100%;
         height: 100%;
       }
     }
     .name{
       font-size: 14px;
       color: #969696;
       margin-top: 20px;
       margin-bottom: 290px;
     }
   }
    }
</style>
