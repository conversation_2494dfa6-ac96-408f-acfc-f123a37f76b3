<template>
    <div class="goods_cate">
      <category-List></category-List>
      <div class="shop_street_banner">
          <div class="categoryList" v-if="storeCategoryList.length > 0">
                <div class="swiper-wrapper">
                  <div
                    v-for="(item, index) in storeCategoryList"
                    :key="index"
                    :class="{ on: slideOn === index }"
                    @click="activeClick(item, index)"
                    class="swiper-slide">{{ item.category_name }}</div>
                </div>
          </div>
      </div>
      <div class="store-wrapper">
      <div class="wrapper_1200">
        <div class="store acea-row row-middle" v-if="storelist.length">
          <div class="item acea-row row-middle" v-for="(item, index) in storelist" :key="index">
              <div class="store_main" :class="'street-bg' + index%6">
                  <div class="store_logo">
                      <img :src="item.mer_avatar" alt="">
                  </div>
                  <div class="store_name line1">
                      <span v-if="item.type_name" class="trader">{{item.type_name}}</span>
                      <span v-else-if="item.is_trader" class="trader">自营</span>
                      {{ item.mer_name }}
                  </div>
                  <div class="follow">{{item.care_count < 10000 ? item.care_count : (item.care_count/10000).toFixed(1)+'万'  }}人关注</div>
                  <nuxt-link :to="{path:'/store',query:{id:item.mer_id}}" class="goStore">进店逛逛</nuxt-link>
              </div>
              <div v-if="item.recommend.length > 0" class="store_product acea-row row-middle">
                <template v-for="(itemn, indexn) in item.recommend">
                  <div v-if="indexn<=2" :key="indexn" class="proList" @click="goDetail(itemn)">
                    <div class="image">
                      <img :src="itemn.image" alt="">
                      <div v-if="itemn.border_pic" :style="{ backgroundImage: `url(${itemn.border_pic})` }" class="border-picture"></div>
                    </div>
                    <div class="name line1">{{ itemn.store_name }}</div>
                    <div class="price"><span>¥</span>{{ itemn.price }}</div>
                </div>
                </template>
                
              </div>
              <div v-else class="store_product acea-row row-middle">
                <div class="no-recommend">
                  <div class="pictrue">
                    <img src="../assets/images/noGoods.png">
                  </div>
                  <div class="name">亲，暂无商品哟~</div>
                </div>
              </div>
          </div>
        </div>
        <el-pagination v-if="total > 0" background layout="prev, pager, next" :total="total" :pageSize="limit" @current-change="bindPageCur"></el-pagination>
      </div>
      </div>
        <div class="noGoods" v-if="!storelist.length">
        <div class="pictrue">
          <img src="../assets/images/noGoods.png">
        </div>
        <div class="name">亲，暂无商户哟~</div>
      </div>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import categoryList from "@/components/categoryList";
export default {
      name: "goods_cate",
      auth: false,
      components: {
        categoryList
      },
      data(){
        return {
          swiperOption: {
            observer: true,
            slidesPerView: 'auto',
            observeParents: true,
          },

          categoryList:[],
          categoryCurrent:[],
          current:0,
          moreCurrent:0,
          seen:false,
          titleName:'',
          erCurrent:0,
          iSdefaults:0,
          storelist:[],
          pullRefreshss: true, // 代表可以进行下拉加载，false代表不能
          page: 1, //代表页面的初始页数
          limit:10,
          scollY: null,// 离底部距离有多少
          total: 0, //总页数
          title:'下拉加载更多',
          cid:0,//一级分类
          sid:0,//二级分类
          priceOrder:'',
          news:0,
          merchant_category_id: '',
          storeCategoryList: [
            {
              category_name: '全部',
              merchant_category_id: ''
            }
          ],
          slideOn: 0
        }
      },
      fetch({ store}) {
        store.commit('isHeader', true);
        store.commit('isFooter', true);
      },
      head() {
        return {
          title: "商品分类-"+this.$store.state.titleCon
        }
      },
      beforeMount(){
        this.getStoreCategroy();
        this.getStorelist('');

      },
      mounted(){
        // this.pullRefresh();

      },
      beforeDestroy() {
        window.onscroll = null;
      },
      methods:{
        activeClick(item,index){
          this.merchant_category_id = item.merchant_category_id;
          this.slideOn = index;
          this.getStorelist(1);
        },
        goDetail: function (item) {
          if(item.product_type == 1){
            this.$router.push({ path: '/goods_seckill_detail/'+item.product_id,query: { time: item.stop_time,status: item.seckill_status } });
          }else{
            this.$router.push({ path: '/goods_detail/'+item.product_id });
          }
        },
        getStoreCategroy(){
          let _this = this;
          _this.$axios.get('api/pc/mer_category').then(function (res) {
            _this.storeCategoryList = _this.storeCategoryList.concat(res.data);
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        getStorelist(num){
          let _this = this;_this.page = num ? num : _this.page;
          let currentPage = {page: _this.page,limit:_this.limit,category_id:_this.merchant_category_id};
          _this.$axios.get('/api/store/merchant/lst', {
            params: currentPage
          }).then(function (res) {
            _this.total = res.data.count;
            _this.storelist = res.data.list;
          }).catch(function (err) {
            _this.$message.error(err);
          })
        },
        // 分页点击
        bindPageCur(data){
          this.page = data
          window.scrollTo({
            top: 0,
            left: 0,
          });
          this.getStorelist('');
        },
        enter(){
          this.current = -1;
          this.seen = true
        },
        leave(){
          this.seen = false;
          this.current =  this.moreCurrent;
        }
      }
    }
</script>

<style scoped lang="scss">
  .goods_cate{
    .noGoods{
      text-align: center;
      .pictrue{
        width: 274px;
        height: 174px;
        margin: 130px auto 0 auto;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 14px;
        color: #969696;
        margin-top: 20px;
        margin-bottom: 290px;
      }
    }
    .title{
      color: #999999;
      height: 46px;
      line-height: 46px;
      .home{
        color: #282828;
      }
    }
    .shop_street_banner{
      background-color: #fff;
      box-shadow: 0 5px 5px rgba(0, 0, 0, .1);
      .categoryList{
          width: 1200px;
          padding-top: 10px;
          padding-bottom: 15px;
          margin: 0 auto;
          
          .swiper-slide.on {
            border-color: #e93323;
            background-color: #e93323;
            color: #fff;
          }
      }
    }
    .store{
        margin-top: 40px;
        .item{
            width: 590px;
            margin-right: 20px;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-bottom: 30px;
            &:nth-child(2n){
              margin-right: 0;
            }
            .store_main{
              &.street-bg1{
                background-image:url('../assets/images/street-bg1.jpg');
              }
              &.street-bg2{
                background-image:url('../assets/images/street-bg2.jpg');
              }
              &.street-bg3{
                background-image:url('../assets/images/street-bg3.jpg');
              }
              &.street-bg4{
                background-image:url('../assets/images/street-bg4.jpg');
              }
              &.street-bg5{
                background-image:url('../assets/images/street-bg5.jpg');
              }
              width: 180px;
              height: 224px;
              background-size: cover;
              text-align: center;
              color: #fff;
              background-image:url('../assets/images/street-bg0.jpg');
              .store_logo{
                display: inline-block;
                margin-top: 35px;
                width: 61px;
                height: 61px;
                border-radius: 100%;
                img{
                  width: 100%;
                  height: 100%;
                }
              }
              .store_name{
                margin: 10px 0;
                font-weight: bold;
                .trader{
                  display: inline-block;
                  font-family: 'PingFang SC';
                  font-size: 12px;
                  padding: 0 4px;
                  width: auto;
                  height: 18px;
                  line-height: 18px;
                  background-color: #E93323;
                  font-weight: normal;
                  border-radius: 2px;
                }
              }
              .follow{
                opacity: .7;
                margin-bottom: 12px;
              }
              .goStore{
                display: inline-block;
                font-size: 12px;
                line-height: 24px;
                border: 1px solid #fff;
                padding: 0 9px;
                color: #fff;
                border-radius: 2px;
              }
            }
            .store_product{
              width: 410px;
              height: 224px;
              padding: 0 17px;
              background: #fff;
              text-align: center;
              cursor: pointer;
              .no-recommend{
                margin: 0 auto;
              }
              .proList{
                width: 112px;
                float: left;
                margin-right: 20px;
                position: relative;
                &:nth-child(3n){
                  margin-right: 0;
                }
                .image{
                  width: 112px;
                  height: 112px;
                  position: relative;
                  .border-picture {
                    position: absolute;
                    top: 0;
                    width: 112px;
                    height: 112px;
                    background: center/cover no-repeat;
                  }
                  img{
                    width: 112px;
                    height: 112px;
                  }
                }
                .name{
                  margin-top: 7px;
                  color: #5A5A5A;
                  line-height: 16px;
                }
                .price{
                  color: #E93323;
                  font-weight: bold;
                  margin-top: 4px;
                  span{
                    font-size: 10px;
                  }
                }
              }
            }
        }
    }
  }
  .shop_street_banner .categoryList ::v-deep .swiper-wrapper{
    flex-wrap: wrap;
  }
  .shop_street_banner .categoryList ::v-deep .el-tabs__item, 
  .shop_street_banner .categoryList ::v-deep .swiper-slide{
    width: auto;
    height: 26px;
    padding: 0 12px;
    border: 1px solid #e4e4e4;
    font-size: 14px;
    line-height: 26px;
    color: #666;
    border-radius: 13px;
    margin-right: 15px;
    cursor: pointer;
    margin-top: 15px;
  }
</style>
