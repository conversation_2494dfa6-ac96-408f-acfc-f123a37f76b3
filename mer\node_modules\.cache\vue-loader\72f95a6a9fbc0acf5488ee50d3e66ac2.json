{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750490523753}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\r\n\r\nimport vuedraggable from \"vuedraggable\";\r\nimport Sortable from \"sortablejs\";\r\n\r\nimport { templateLsitApi, attrCreatApi } from \"@/api/product\";\r\n\r\nexport default {\r\n  props: {\r\n    formValidate: {\r\n      type: Object,\r\n      default: () => ({\r\n        spec_type: 0 // 默认值\r\n      })\r\n    },\r\n    ManyAttrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    changeAttrValue: {\r\n      type: String,\r\n      default: () => \"\"\r\n    },\r\n    attrValue: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    formThead: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    oneFormBatch: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    OneattrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    formDynamic: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    product_id: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    attrs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    cdkeyLibraryList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    selectedLibrary: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  components: {\r\n    draggable: vuedraggable\r\n  },\r\n  data() {\r\n    return {\r\n      ruleList: [],\r\n      timeCheckAll: [],\r\n      reservationTime: [],\r\n      timeCheckAllGroup: [], //自动划分当前选中的元素\r\n      timeCheckAllClone: [], //自动划分克隆全部选中的元素\r\n      timeDataClone: [], //自定义划分时的库存（为了切换时段划分时，可以复原之前选中的数据）\r\n      canSel: true, // 规格图片添加判断\r\n      tableKey: 0,\r\n      selectRule: \"\",\r\n      // 服务包相关数据\r\n      showAdvancedPackages: false,\r\n      packageConfig: {\r\n        basic: {\r\n          enabled: true,\r\n          name: 'Basic',\r\n          price: 0,\r\n          delivery_time: '3天',\r\n          revisions: '2',\r\n          description: ''\r\n        },\r\n        standard: {\r\n          enabled: false,\r\n          name: 'Standard',\r\n          price: 0,\r\n          delivery_time: '5天',\r\n          revisions: '5',\r\n          description: ''\r\n        },\r\n        premium: {\r\n          enabled: false,\r\n          name: 'Premium',\r\n          price: 0,\r\n          delivery_time: '7天',\r\n          revisions: '无限',\r\n          description: ''\r\n        }\r\n      },\r\n      extraServices: {\r\n        fastDelivery: {\r\n          enabled: false,\r\n          name: '加急处理',\r\n          price: 0,\r\n          options: []\r\n        },\r\n        additionalRevisions: {\r\n          enabled: false,\r\n          name: '额外修改',\r\n          price: 0,\r\n          options: []\r\n        },\r\n        warranty: {\r\n          enabled: false,\r\n          name: '延长保障',\r\n          price: 0,\r\n          options: []\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    this.productGetRule();\r\n    this.showSpecsByType();\r\n  },\r\n  methods: {\r\n    /**根据商品类型判断是否显示重量体积 */\r\n    showSpecsByType() {\r\n      if (this.formValidate.type == 2 || this.formValidate.type == 3) {\r\n        delete this.attrValue.weight;\r\n        delete this.attrValue.volume;\r\n      } else {\r\n        this.attrValue.weight = \"\";\r\n        this.attrValue.volume = \"\";\r\n      }\r\n    },\r\n    // 规格名称改变\r\n    attrChangeValue(i, val) {\r\n      this.$emit(\"attrChangeValue\", i, val);\r\n    },\r\n    handleChange(event, index, name) {\r\n      let result = this.cdkeyLibraryList.find(item => item.id === event);\r\n      this.$set(\r\n        this[name][index],\r\n        \"stock\",\r\n        event ? Number(result.total_num - result.used_num) : 0\r\n      );\r\n      if (name == \"ManyAttrValue\")\r\n        this.getSelectedLiarbry(this[name][index], this.ManyAttrValue);\r\n    },\r\n    attrDetailChangeValue(val, i) {\r\n      this.$emit(\"attrDetailChangeValue\", val, i);\r\n    },\r\n\r\n    //添加云盘链接\r\n    addVirtual(type, index, name) {\r\n      this.$emit(\"addVirtual\", type, index, name);\r\n    },\r\n    handleFocus(val) {\r\n      this.$emit(\"handleFocus\", val);\r\n    },\r\n    handleBlur() {\r\n      this.$emit(\"handleBlur\");\r\n    },\r\n    // 规格图片添加开关\r\n    addPic(e, i) {\r\n      if (e) {\r\n        this.attrs.map((item, ii) => {\r\n          if (ii !== i) {\r\n            this.$set(item, \"add_pic\", 0);\r\n          }\r\n        });\r\n        this.canSel = false;\r\n      } else {\r\n        this.canSel = true;\r\n      }\r\n    },\r\n    handleShowPop(index) {\r\n      this.$refs[\"inputRef_\" + index][0].focus();\r\n    },\r\n\r\n    // 删除规格\r\n    handleRemoveRole(index) {\r\n      this.$emit(\"handleRemoveRole\", index);\r\n    },\r\n\r\n    // 删除属性\r\n    handleRemove2(item, index, val) {\r\n      item.splice(index, 1);\r\n      this.$emit(\"delAttrTable\", val);\r\n    },\r\n\r\n    handleSelImg(item, index, indexn) {\r\n      let that = this;\r\n      this.$modalUpload(function(img) {\r\n        item.pic = img[0];\r\n        that.changeSpecImg([item.value], img[0], index, indexn);\r\n      });\r\n    },\r\n    changeSpecImg(arr, img, index, indexn) {\r\n      // 判断是否存在规格图\r\n      let isHas = false;\r\n      for (let i = 1; i < this.ManyAttrValue.length; i++) {\r\n        let item = this.ManyAttrValue[i];\r\n        if (item.image && this.isSubset(item.attr_arr, arr)) {\r\n          isHas = true;\r\n          break;\r\n        }\r\n      }\r\n      if (isHas) {\r\n        this.$confirm(\"可以同步修改下方该规格图片，确定要替换吗？\", \"提示\", {\r\n          confirmButtonText: leaveuKeyTerms['替换'],\r\n          cancelButtonText: leaveuKeyTerms['暂不'],\r\n          type: \"warning\"\r\n        })\r\n          .then(() => {\r\n            for (let val of this.ManyAttrValue) {\r\n              if (this.isSubset(val.attr_arr, arr)) {\r\n                this.$set(val, \"image\", img);\r\n              }\r\n            }\r\n\r\n            this.$emit(\"setAttrs\", this.attrs);\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        for (let val of this.ManyAttrValue) {\r\n          if (this.isSubset(val.attr_arr, arr)) {\r\n            this.$set(val, \"image\", img);\r\n          }\r\n        }\r\n        this.$emit(\"setAttrs\", this.attrs);\r\n      }\r\n    },\r\n\r\n    isSubset(arr1, arr2) {\r\n      // 将数组转换为 Set，以便进行高效的包含检查\r\n      const set1 = new Set(arr1);\r\n      const set2 = new Set(arr2);\r\n      // 检查 set2 中的每个元素是否都在 set1 中\r\n      for (let elem of set2) {\r\n        if (!set1.has(elem)) {\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    // 切换默认选中规格\r\n    changeDefaultSelect(e, index) {\r\n      // 一个开启 其他关闭\r\n      this.ManyAttrValue.map((item, i) => {\r\n        if (i !== index) {\r\n          item.is_default_select = 0;\r\n        }\r\n      });\r\n      if (e) this.ManyAttrValue[index].is_show = 1;\r\n    },\r\n\r\n    // 添加属性\r\n    createAttr(num, idx) {\r\n      if (num) {\r\n        // 判断是否存在同样熟悉\r\n        var isExist = this.attrs[idx].detail.some(item => item.value === num);\r\n        if (isExist) {\r\n          this.$message.error(\"规格值已存在\");\r\n          return;\r\n        }\r\n        this.attrs[idx].detail.push({ value: num, image: \"\" });\r\n        this.formValidate.attr = this.attrs;\r\n        if (this.ManyAttrValue.length) {\r\n          this.addOneAttr(this.attrs[idx].value, num);\r\n        } else {\r\n          this.$emit(\"generateAttr\", this.attrs);\r\n        }\r\n        this.$refs[\"popoverRef_\" + idx][0].doClose(); //关闭的\r\n        this.clearAttr();\r\n        setTimeout(() => {\r\n          if (this.$refs[\"popoverRef_\" + idx]) {\r\n            //重点是以下两句\r\n            this.$refs[\"popoverRef_\" + idx][0].doShow(); //打开的\r\n            //重点是以上两句\r\n          }\r\n        }, 20);\r\n      } else {\r\n        this.$refs[\"popoverRef_\" + idx][0].doClose(); //关闭的\r\n      }\r\n      // 监听多规格值变化，在新增时候默认选中规格要自动默认第一个数据\r\n      let exists = this.ManyAttrValue.some(item => item.is_default_select == 0);\r\n      if (exists) {\r\n        this.ManyAttrValue[1].is_default_select = 1;\r\n      }\r\n    },\r\n    // 新增一条属性\r\n    addOneAttr(val, val2) {\r\n      this.$emit(\"generateAttr\", this.attrs, val2);\r\n    },\r\n    handleRemoveImg(val, index, indexn) {\r\n      this.$emit(\"delManyImg\", val, index, indexn);\r\n    },\r\n\r\n    clearAttr() {\r\n      this.formDynamic.attrsName = \"\";\r\n      this.formDynamic.attrsVal = \"\";\r\n    },\r\n\r\n    // 清空批量规格信息\r\n    batchDel() {\r\n      this.$emit(\"batchDel\");\r\n    },\r\n    // 生成列表 行 列 数据\r\n    tableCellClassName({ row, column, rowIndex, columnIndex }) {\r\n      //注意这里是解构\r\n      //利用单元格的 className 的回调方法，给行列索引赋值\r\n      row.index = rowIndex || \"\";\r\n      column.index = columnIndex;\r\n    },\r\n    handleSaveAsTemplate() {\r\n      this.$prompt(\"\", \"请输入模板名称\", {\r\n        confirmButtonText: leaveuKeyTerms['确定'],\r\n        cancelButtonText: leaveuKeyTerms['取消']\r\n      })\r\n        .then(({ value }) => {\r\n          let template_value = this.attrs.map(item => {\r\n            return {\r\n              value: item.value,\r\n              detail: item.detail.map(e => e.value)\r\n            };\r\n          });\r\n          let formDynamic = {\r\n            template_name: value,\r\n            template_value: template_value\r\n          };\r\n          attrCreatApi(formDynamic, 0)\r\n            .then(res => {\r\n              this.$message.success(res.message);\r\n              this.productGetRule();\r\n            })\r\n            .catch(res => {\r\n              this.$message.error(res.message);\r\n            });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 选择规格\r\n    onChangeSpec(num) {\r\n      if (num === 1) this.productGetRule();\r\n    },\r\n    changeCurrentIndex(i) {\r\n      this.currentIndex = i;\r\n    },\r\n    // 获取商品属性模板；\r\n    productGetRule() {\r\n      templateLsitApi().then(res => {\r\n        this.ruleList = res.data;\r\n      });\r\n    },\r\n    // 新增规格\r\n    handleAddRole() {\r\n      this.$emit(\"handleAddRole\");\r\n    },\r\n\r\n    // 批量添加\r\n    batchAdd() {\r\n      let arr = [];\r\n      for (let val of this.attrs) {\r\n        if (this.oneFormBatch[0][val.value]) {\r\n          arr.push(this.oneFormBatch[0][val.value]);\r\n        }\r\n      }\r\n      for (let val of this.ManyAttrValue) {\r\n        if (arr.length) {\r\n          if (this.isSubset(val.attr_arr, arr)) {\r\n            if (this.oneFormBatch[0].image) {\r\n              this.$set(val, \"image\", this.oneFormBatch[0].image);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].price != undefined &&\r\n              this.oneFormBatch[0].price != \"\"\r\n            ) {\r\n              this.$set(val, \"price\", this.oneFormBatch[0].price);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].cost != undefined &&\r\n              this.oneFormBatch[0].cost != \"\"\r\n            ) {\r\n              this.$set(val, \"cost\", this.oneFormBatch[0].cost);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].ot_price != undefined &&\r\n              this.oneFormBatch[0].ot_price != \"\"\r\n            ) {\r\n              this.$set(val, \"ot_price\", this.oneFormBatch[0].ot_price);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].stock != undefined &&\r\n              this.oneFormBatch[0].stock != \"\"\r\n            ) {\r\n              this.$set(val, \"stock\", this.oneFormBatch[0].stock);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].bar_code != undefined &&\r\n              this.oneFormBatch[0].bar_code != \"\"\r\n            ) {\r\n              this.$set(val, \"bar_code\", this.oneFormBatch[0].bar_code);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].bar_code_number != undefined &&\r\n              this.oneFormBatch[0].bar_code_number != \"\"\r\n            ) {\r\n              this.$set(\r\n                val,\r\n                \"bar_code_number\",\r\n                this.oneFormBatch[0].bar_code_number\r\n              );\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].weight != undefined &&\r\n              this.oneFormBatch[0].weight != \"\"\r\n            ) {\r\n              this.$set(val, \"weight\", this.oneFormBatch[0].weight);\r\n            }\r\n            if (\r\n              this.oneFormBatch[0].volume != undefined &&\r\n              this.oneFormBatch[0].volume != \"\"\r\n            ) {\r\n              this.$set(val, \"volume\", this.oneFormBatch[0].volume);\r\n            }\r\n          }\r\n        } else {\r\n          if (this.oneFormBatch[0].image) {\r\n            this.$set(val, \"image\", this.oneFormBatch[0].image);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].price != undefined &&\r\n            this.oneFormBatch[0].price != \"\"\r\n          ) {\r\n            this.$set(val, \"price\", this.oneFormBatch[0].price);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].cost != undefined &&\r\n            this.oneFormBatch[0].cost != \"\"\r\n          ) {\r\n            this.$set(val, \"cost\", this.oneFormBatch[0].cost);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].ot_price != undefined &&\r\n            this.oneFormBatch[0].ot_price != \"\"\r\n          ) {\r\n            this.$set(val, \"ot_price\", this.oneFormBatch[0].ot_price);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].stock != undefined &&\r\n            this.oneFormBatch[0].stock != \"\"\r\n          ) {\r\n            this.$set(val, \"stock\", this.oneFormBatch[0].stock);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].weight != undefined &&\r\n            this.oneFormBatch[0].weight != \"\"\r\n          ) {\r\n            this.$set(val, \"weight\", this.oneFormBatch[0].weight);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].volume != undefined &&\r\n            this.oneFormBatch[0].volume != \"\"\r\n          ) {\r\n            this.$set(val, \"volume\", this.oneFormBatch[0].volume);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].bar_code != undefined &&\r\n            this.oneFormBatch[0].bar_code != \"\"\r\n          ) {\r\n            this.$set(val, \"bar_code\", this.oneFormBatch[0].bar_code);\r\n          }\r\n          if (\r\n            this.oneFormBatch[0].bar_code_number != undefined &&\r\n            this.oneFormBatch[0].bar_code_number != \"\"\r\n          ) {\r\n            this.$set(\r\n              val,\r\n              \"bar_code_number\",\r\n              this.oneFormBatch[0].bar_code_number\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 合并单元格\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0 && rowIndex > 0) {\r\n        let lable = column.label;\r\n        //这里判断第几列需要合并\r\n\r\n        // 添加安全检查，确保数据结构存在\r\n        if (!this.ManyAttrValue[rowIndex] || !this.ManyAttrValue[rowIndex].detail) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 1\r\n          };\r\n        }\r\n\r\n        const tagFamily = this.ManyAttrValue[rowIndex].detail[lable];\r\n        const index = this.ManyAttrValue.findIndex((item, index) => {\r\n          if (index > 0 && item && item.detail) {\r\n            return item.detail[lable] == tagFamily;\r\n          }\r\n          return false;\r\n        });\r\n        if (rowIndex == index) {\r\n          let len = 1;\r\n          for (let i = index + 1; i < this.ManyAttrValue.length; i++) {\r\n            if (!this.ManyAttrValue[i] || !this.ManyAttrValue[i].detail || this.ManyAttrValue[i].detail[lable] !== tagFamily) {\r\n              break;\r\n            }\r\n            len++;\r\n          }\r\n          return {\r\n            rowspan: len,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n    // 点击商品图\r\n    modalPicTap(tit, num, i) {\r\n      const _this = this;\r\n      const attr = [];\r\n      this.$modalUpload(function(img) {\r\n        if (tit === \"1\" && !num) {\r\n          _this.formValidate.image = img[0];\r\n          _this.OneattrValue[0].image = img[0];\r\n        }\r\n        if (tit === \"2\" && !num) {\r\n          img.map(item => {\r\n            attr.push(item.attachment_src);\r\n            _this.formValidate.slider_image.push(item);\r\n            if (_this.formValidate.slider_image.length > 10) {\r\n              _this.formValidate.slider_image.length = 10;\r\n            }\r\n          });\r\n        }\r\n        if (tit === \"1\" && num === \"dan\") {\r\n          _this.OneattrValue[0].image = img[0];\r\n        }\r\n        if (tit === \"1\" && num === \"duo\") {\r\n          _this.ManyAttrValue[i].image = img[0];\r\n        }\r\n        if (tit === \"1\" && num === \"pi\") {\r\n          _this.oneFormBatch[0].image = img[0];\r\n        }\r\n      }, tit);\r\n    },\r\n    // 规格拖拽排序后\r\n    onMoveSpec() {\r\n      this.$emit(\"generateAttr\", this.attrs);\r\n    },\r\n    //清空卡密\r\n    virtualListClear() {\r\n      this.virtualList = [\r\n        {\r\n          is_type: 0,\r\n          key: \"\",\r\n          stock: \"\"\r\n        }\r\n      ];\r\n    },\r\n    seeVirtual(type, data, name, index) {\r\n      this.$emit(\"seeVirtual\", type, data, name, index);\r\n    },\r\n\r\n    getSelectedLiarbry(data, array) {\r\n      this.$emit(\"getSelectedLiarbry\", data, array);\r\n    },\r\n\r\n    changeCurrentIndex(i) {\r\n      this.currentIndex = i;\r\n    },\r\n    // 选择属性确认\r\n    confirm(name) {\r\n      this.selectRule = name;\r\n      this.createBnt = true;\r\n      if (!this.selectRule) {\r\n        return this.$message.warning(\"请选择属性\");\r\n      }\r\n      this.ruleList.forEach(item => {\r\n        if (item.attr_template_id === this.selectRule) {\r\n          item.template_value.forEach((value, index) => {\r\n            value.add_pic = 0;\r\n          });\r\n          this.canSel = true;\r\n          this.$emit(\"setAttrs\", [...item.template_value]);\r\n          this.formValidate.attr = item.template_value;\r\n        }\r\n      });\r\n      // this.$emit('generateAttr', this.attrs)\r\n    },\r\n\r\n    // 生成规格组合\r\n    generateCombinations(arr, prefix = []) {\r\n      if (arr.length === 0) {\r\n        return [prefix];\r\n      }\r\n      const [first, ...rest] = arr;\r\n      return first.detail.flatMap(detail =>\r\n        this.generateCombinations(rest, [...prefix, detail.value])\r\n      );\r\n    },\r\n\r\n    // 服务包相关方法\r\n    expandToAdvancedPackages() {\r\n      this.showAdvancedPackages = true;\r\n      // 启用Standard和Premium套餐\r\n      this.packageConfig.standard.enabled = true;\r\n      this.packageConfig.premium.enabled = true;\r\n    },\r\n\r\n    collapseToBasicPackage() {\r\n      this.showAdvancedPackages = false;\r\n      // 可选择性禁用其他套餐\r\n      // this.packageConfig.standard.enabled = false;\r\n      // this.packageConfig.premium.enabled = false;\r\n    },\r\n\r\n    generateServicePackageSpecs() {\r\n      // 验证配置\r\n      const enabledPackages = [];\r\n      if (this.packageConfig.basic.enabled) {\r\n        if (!this.packageConfig.basic.name || this.packageConfig.basic.price <= 0) {\r\n          return this.$message.error('请完善Basic套餐的名称和价格');\r\n        }\r\n        enabledPackages.push({\r\n          value: this.packageConfig.basic.name,\r\n          image: ''\r\n        });\r\n      }\r\n      if (this.packageConfig.standard.enabled) {\r\n        if (!this.packageConfig.standard.name || this.packageConfig.standard.price <= 0) {\r\n          return this.$message.error('请完善Standard套餐的名称和价格');\r\n        }\r\n        enabledPackages.push({\r\n          value: this.packageConfig.standard.name,\r\n          image: ''\r\n        });\r\n      }\r\n      if (this.packageConfig.premium.enabled) {\r\n        if (!this.packageConfig.premium.name || this.packageConfig.premium.price <= 0) {\r\n          return this.$message.error('请完善Premium套餐的名称和价格');\r\n        }\r\n        enabledPackages.push({\r\n          value: this.packageConfig.premium.name,\r\n          image: ''\r\n        });\r\n      }\r\n\r\n      if (enabledPackages.length === 0) {\r\n        return this.$message.error('请至少启用一个服务包');\r\n      }\r\n\r\n      // 生成服务包规格数据，使用与原始多规格兼容的格式\r\n      const attrs = [];\r\n\r\n      // 1. 生成主服务包规格\r\n      attrs.push({\r\n        value: 'Packages',\r\n        detail: enabledPackages,\r\n        add_pic: 0\r\n      });\r\n\r\n      // 2. 生成额外服务规格\r\n      if (this.extraServices.fastDelivery.enabled && this.extraServices.fastDelivery.options.length > 0) {\r\n        attrs.push({\r\n          value: 'extra services (交付时间)',\r\n          detail: this.extraServices.fastDelivery.options.map(option => ({\r\n            value: option,\r\n            image: ''\r\n          })),\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      if (this.extraServices.additionalRevisions.enabled && this.extraServices.additionalRevisions.options.length > 0) {\r\n        attrs.push({\r\n          value: 'extra services (追加修改次数)',\r\n          detail: this.extraServices.additionalRevisions.options.map(option => ({\r\n            value: option,\r\n            image: ''\r\n          })),\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      if (this.extraServices.warranty.enabled && this.extraServices.warranty.options.length > 0) {\r\n        attrs.push({\r\n          value: 'extra services (保过期)',\r\n          detail: this.extraServices.warranty.options.map(option => ({\r\n            value: option,\r\n            image: ''\r\n          })),\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      // 通知父组件更新规格数据，并传递服务包配置\r\n      this.$emit('setAttrs', attrs, this.packageConfig);\r\n      // 切换到多规格模式以兼容后端\r\n      this.formValidate.spec_type = 1;\r\n      this.$message.success('服务包规格生成成功！请查看下方的规格列表。');\r\n    },\r\n\r\n    previewServicePackage() {\r\n      // 预览服务包配置\r\n      const config = {\r\n        packages: this.packageConfig,\r\n        extraServices: this.extraServices\r\n      };\r\n\r\n      this.$alert(\r\n        `<pre>${JSON.stringify(config, null, 2)}</pre>`,\r\n        '服务包配置预览',\r\n        {\r\n          dangerouslyUseHTMLString: true,\r\n          customClass: 'preview-dialog'\r\n        }\r\n      );\r\n    }\r\n  }\r\n};\r\n", null]}