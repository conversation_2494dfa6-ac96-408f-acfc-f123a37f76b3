<template>
  <div class="user-order-list">
    <div v-if="orderList.length > 0" class="order-list">
      <ul>
        <li v-for="(item,index) in orderList" :key="index">
          <div class="bd">
            <div class="order-txt">
              订单日期: {{item.create_time}}
              <nuxt-link class="mer_name" :to="{path:'/store',query:{id:item.mer_id}}">{{item.merchant.mer_name}}</nuxt-link>
              <span v-if="item.presellOrder.activeStatus === 1" class="status">等待买家付尾款</span>
              <span v-else-if="item.presellOrder.activeStatus === 0" class="status">未开始</span>
              <span v-else-if="item.presellOrder.activeStatus === 2" class="status">交易已关闭</span>
            </div>
            <div class="content" @click="goDetail(item)">
              <div class="goods-item acea-row row-between-wrapper" v-for="(goods, index) in item.orderProduct" :key="index">
                <div class="acea-row">
                  <div class="img-box" v-if="goods.cart_info.productAttr.image">
                    <img :src='(goods.cart_info.productAttr && goods.cart_info.productAttr.image) || goods.cart_info.product.image' alt="">
                    <span class="presell_img">预售</span>
                  </div>
                  <div class="info-txt">
                    <div class="title line2">{{goods.cart_info.product.store_name}}</div>
                    <div class="info" v-if="goods.cart_info.productAttr.sku">{{goods.cart_info.productAttr.sku}}</div>
                    <div class="final_date">支付尾款时间：{{item.presellOrder.final_start_time}} ~ {{item.presellOrder.final_end_time}}</div>
                   
                  </div>
                </div>
               <div class="price_count acea-row">
                 <div class="price" v-if="goods.cart_info.productPresellAttr.presell_price">￥{{ goods.cart_info.productPresellAttr.presell_price }}</div>
                  <span class="num">x{{ goods.product_num }}</span>
               </div>
              </div>
            </div>
          </div>
          <div class="foot">
            <p>定金已支付 <span class="price">￥{{ item.pay_price }} </span>
            尾款待支付 <span class="price">￥{{item.presellOrder.pay_price}}</span></p>
            <div class="btn-wrapper">
              <div v-if="item.presellOrder.activeStatus === 2" class="rest" @click="cancelOrder(item,index)">取消订单</div>
              <div v-if="item.presellOrder.activeStatus === 1" class="pay" @click="goBuy(item)">立即付款</div>
              <div v-if="item.presellOrder.activeStatus === 0" class="pay">未开始</div>
              <div v-if="item.presellOrder.activeStatus === 2" class="pay">交易已关闭</div>
            </div>
          </div>
        </li>
      </ul>   
    </div>
    <div class="pages-box" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="bindPageCur"
          :total="total">
        </el-pagination>
      </div>
      <div class="empty-box" v-if="orderList.length == 0">
        <img src="~assets/images/noorder.png" alt="">
        <p>亲，暂无订单信息哟~</p>
      </div>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {Message, MessageBox} from "element-ui";
export default {
    name: "orderList",
    auth: "guest",
    scrollToTop: true,
    data() {
      return {
        tabCur: '',
        orderList: [
        ],
        orderData: {},
        orderStatus: -1,
        total:0,
        page: 1,
        limit: 10
      }
    },
    fetch({ store }) {
      store.commit("isBanner", false);
      store.commit("isHeader", true);
      store.commit("isFooter", true);
    },
    head() {
      return {
        title: "预售订单-"+this.$store.state.titleCon
      }
    },
    beforeMount(){
    },
    mounted() {
      Promise.all([this.getList()])
    },
    methods: {
      // 获取订单列表
      getList() {
        this.$axios.get('/api/order/list',{
          params:{
            status:10,
            page:this.page,
            limit:this.limit
          }
        }).then(res => {
          this.orderList = res.data.list
          this.total = res.data.count
        })
      },   
      // 取消订单
      cancelOrder(item,index){
        MessageBox.confirm('确定取消该订单吗？','提示').then(res=>{
          this.$axios.post('/api/order/del/'+item.order_id).then(data=>{
            this.getList()
            return Message.success('订单已取消！')
          })
        })
      },
      // 查看详情
      goDetail(item){
        if(this.orderStatus === 0){
          this.$router.push({
            path: `/order_stay_detail`,
            query:{
              orderId:item.group_order_id
            }
          })
        }else{
          this.$router.push({
            path: `/order_detail`,
            query:{
              orderId:item.order_id
            }
          })
        }
      },    
      // 分页点击
      bindPageCur(data){
        this.page = data
        if(this.orderStatus == 0){
            this.getUnPayList();
        }else{
            this.getList()
        }
      },
      // 立即支付
      goBuy(item){
        this.$router.push({
          path:'/payment',
          query:{
            result:item.order_id,
            type: 2
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .user-com-tab {
    .item {
      padding: 0 10px;
    }
  }
  .user-order-list {
    li {
      position: relative;
      padding: 30px 0 26px;
      border-bottom: 1px solid #ECECEC;
      .refund-icon{
        position: absolute;
        right: 50px;
        top: 40px;
      }
      .bd {
        padding-right: 40px;
        border-bottom: 1px dashed #E1E1E1;
        cursor: pointer;
        .order-txt {
          color: #282828;
          font-size: 14px;
          .mer_name{
            display: inline-block;
            margin-left: 40px;
            color: #666666;
            &:hover{
              color: #E93323;
            }
          }
          .status {
            float: right;
            color: #E93323;
          }
        }
        .content {
          margin-top: 20px;
          .goods-item {
            display: flex;
            position: relative;
            margin-bottom: 20px;
            .img-box {
              width: 120px;
              height: 120px;
              position: relative;
              .presell_img{
                position: absolute;
                width: 100%;
                color: #fff;
                text-align: center;
                bottom: 0;
                left: 0;
                line-height: 24px;
                height: 24px;
                background: rgba(0,0,0,.5);
                font-size: 12px;
                border-radius: 0 0 4px 4px;
              }
              img {
                display: block;
                width: 100%;
                height: 100%;
              }
            }
            .info-txt {
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: center;
              width: 500px;
              margin-left: 24px;
              font-size: 14px;
              .info{
                font-size: 12px;
                color: #aaa;
                margin-top: 4px;
              }
              
            }
            .price {
                color: #E93323;
                margin-right: 10px;
              }
              .num {
                
                color: #999999;
              }
            .final_date{
              margin-top: 9px;
              color: #FD6523;
            }
          }
        }
      }
      .foot {
        padding-top: 26px;
        padding-right: 32px;
        p {
          text-align: right;
          color: #666;
          font-size: 14px;
          .price {
            color: #E93323;
          }
        }
        .btn-wrapper {
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
          div {
            width: 110px;
            height: 36px;
            text-align: center;
            line-height: 34px;
            margin-left: 20px;
            border: 1px solid #999999;
            border-radius: 4px;
            font-size: 14px;
            color: #666666;
            cursor: pointer;
            &.pay {
              border-color: #E93323;
              background: #E93323;
              color: #fff;
            }
          }
        }
      }
    }
  }
  .pages-box{
    margin-top: 30px;
    text-align: right;
  }
</style>
