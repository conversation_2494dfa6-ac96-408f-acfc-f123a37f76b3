<template>
  <div class="divBox">
    <el-card class="box-card">
      <div class="mb20">
        <el-button size="small" type="primary" @click="onAdd">{{ $t('添加用户标签') }}</el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
        highlight-current-row
      >
        <el-table-column
          label="ID"
          min-width="60"
        >
          <template slot-scope="{row}">
            <span v-text="$route.path.indexOf('group') !== -1?row.group_id:row.label_id" />
          </template>
        </el-table-column>
        <el-table-column
          :label="$route.path.indexOf('group') !== -1 ? '分组名称' : '标签名称'"
          min-width="180"
        >
          <template slot-scope="{row}">
            <span v-text="$route.path.indexOf('group') !== -1?row.group_name:row.label_name" />
          </template>
        </el-table-column>
        <el-table-column
          prop="create_time"
          :label="$t('创建时间')"
          min-width="150"
        />
        <el-table-column :label="$t('操作')" min-width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="onEdit($route.path.indexOf('group') !== -1?scope.row.group_id:scope.row.label_id)">{{ $t('编辑') }}</el-button>
            <el-button type="text" size="small" @click="handleDelete($route.path.indexOf('group') !== -1?scope.row.group_id:scope.row.label_id, scope.$index)">{{ $t('删除') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import {
  labelLstApi,
  labelFormApi,
  labelEditApi,
  labelDeleteApi
} from '@/api/user'
export default {
  name: 'UserGroup',
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20
      },
      tableData: {
        data: [],
        total: 0
      },
      listLoading: true
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 列表
    getList() {
      this.listLoading = true
      labelLstApi(this.tableFrom).then(res => {
        this.tableData.data = res.data.list
        this.tableData.total = res.data.count
        this.listLoading = false
      }).catch(res => {
        this.listLoading = false
        this.$message.error(res.message)
      })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    // 添加
    onAdd() {
      this.$modalForm(labelFormApi()).then(() => this.getList())
    },
    // 编辑
    onEdit(id) {
      this.$modalForm(labelEditApi(id)).then(() => this.getList())
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure('删除该标签').then(() => {
        labelDeleteApi(id).then(({ message }) => {
          this.$message.success(message)
          this.tableData.data.splice(idx, 1)
        }).catch(({ message }) => {
          this.$message.error(message)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
