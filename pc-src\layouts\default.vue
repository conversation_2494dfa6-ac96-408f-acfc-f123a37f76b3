<template>
  <div ref="tsRoot">
    <divBanner ref="tsHeader" v-if="$store.state.banner"></divBanner>
    <divHeader ref="tsHeader" v-if="$store.state.headers"></divHeader>
    <divLogin ref="tsLogin" v-if="$store.state.login"></divLogin>
    <nuxt ref="tsNuxt"/>
    <divFooter v-if="$store.state.footers"></divFooter>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
  import divBanner from '~/components/banner.vue'
  import divHeader from '~/components/headers.vue'
  import divFooter from '~/components/footers.vue'
  import divLogin from '~/components/login.vue'
  export default {
    components: {
      divBanner,
      divHeader,
      divFooter,
      divLogin
    }
  }
</script>
