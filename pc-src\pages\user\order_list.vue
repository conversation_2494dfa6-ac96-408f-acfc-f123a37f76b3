<template>
  <div class="user-order-list">
    <div class="user-com-tab acea-row row-between row-middle">
      <div>
        <span
          class="item"
          :class="orderStatus == -1 ? 'on' : ''"
          @click="bindTab(-1)"
          >全部({{ orderData.all || 0 }})</span
        >
        <span
          class="item"
          :class="orderStatus == 0 ? 'on' : ''"
          @click="bindTab(0)"
          >待付款({{ orderData.noPay || 0 }})</span
        >
        <span
          class="item"
          :class="orderStatus == 1 ? 'on' : ''"
          @click="bindTab(1)"
          >待发货({{ orderData.noPostage || 0 }})</span
        >
        <span
          class="item"
          :class="orderStatus == 2 ? 'on' : ''"
          @click="bindTab(2)"
          >待收货({{ orderData.noDeliver || 0 }})</span
        >
        <span
          class="item"
          :class="orderStatus == 3 ? 'on' : ''"
          @click="bindTab(3)"
          >待评价({{ orderData.noComment || 0 }})</span
        >
        <span
          class="item"
          :class="orderStatus == 4 ? 'on' : ''"
          @click="bindTab(4)"
          >已完成({{ orderData.done || 0 }})</span
        >
      </div>
      <div class="order-search acea-row row-middle">
        <span class="iconfont icon-xiazai5"></span>
        <input class="input" v-model="keyword" @keydown.enter.stop="search" placeholder="搜索商品名称或订单号" />
      </div>
    </div>
    <div v-if="orderList.length > 0" class="order-list">
      <ul v-if="orderStatus != 0">
        <li v-for="(item, index) in orderList" :key="index">
          <div class="bd">
            <div class="order-txt acea-row row-between row-middle">
              <div class="acea-row row-middle">
                <template v-if="item.status == 0">
                  <span v-if="item.is_virtual ==4" class="status">派单中</span>
                  <template v-else>
                     <span v-if="item.order_type == 1" class="status">
                      {{ item.takeOrderCount > 0 ? "部分核销" : "待核销" }}
                    </span>
                    <span v-else class="status">待发货</span>
                  </template>
                </template>
                <template v-if="(item.status == 20 || (item.status == 1 && item.order_type == 1)) && item.is_virtual ==4">
                  <span v-if="item.status == 20 || (item.status == 1 && item.order_type == 1)" class="status">待核销</span>
                </template>
                <span v-else-if="item.status == 1" class="status">{{item.is_virtual ==4 ? '待服务' : '待收货'}}</span>
                <span v-else-if="item.status == 2" class="status">待评价</span>
                <span v-else-if="item.status == 3" class="status">已完成</span>
                <span v-else-if="item.status == -1" class="status">已退款</span>
                <span class="order-time">{{ item.create_time }}</span>
                <nuxt-link
                  v-if="item.merchant"
                  class="mer_name acea-row row-center row-middle"
                  :to="{ path: '/store', query: { id: item.mer_id } }"
                  >
                  <span class="iconfont-h5 icon-ic_mall"></span>
                  <span class="name">{{ item.merchant.mer_name }}</span>
                </nuxt-link
                >
              </div>
              <div class="order-price">
                ¥{{item.pay_price}}
                <!-- <priceFormat :price="item.pay_price" weight intSize="20" floatSize="14" labelSize="12"></priceFormat> -->
              </div>
            </div>
            <div class="content" @click.stop="goDetail(item)">
              <div v-for="(goods, index) in item.orderProduct" :key="index" class="acea-row row-between">
                <template>
                  <div v-if="item.activity_type === 2" class="goods-item">
                    <div class="img-box">
                      <img
                        :src="
                          (goods.cart_info.productAttr &&
                            goods.cart_info.productAttr.image) ||
                            goods.cart_info.product.image
                        "
                        alt=""
                      />
                      <span>预售</span>
                    </div>
                    <div class="info-txt">
                      <div class="title line1">
                        {{ goods.cart_info.product.store_name }}
                      </div>
                      <div class="info" v-if="goods.cart_info.productAttr.sku">
                        {{ goods.cart_info.productAttr.sku }}
                      </div>
                      <div
                        class="price"
                        v-if="goods.cart_info.productPresellAttr.presell_price"
                      >
                        ¥{{ goods.cart_info.productPresellAttr.presell_price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                      <div class="price" v-else>
                        ¥{{ goods.cart_info.product.price }}
                      </div>
                      <div class="develity_date">
                        <!--全款预售-->
                        <span
                          v-if="goods.cart_info.productPresell.presell_type === 1"
                        >
                          发货时间：{{
                            goods.cart_info.productPresell.delivery_type === 1
                              ? "支付成功后"
                              : "预售结束后"
                          }}{{ goods.cart_info.productPresell.delivery_day }}天内
                        </span>
                        <!--定金预售-->
                        <span
                          v-if="goods.cart_info.productPresell.presell_type === 2"
                        >
                          发货时间：{{
                            goods.cart_info.productPresell.delivery_type === 1
                              ? "支付尾款后"
                              : "预售结束后"
                          }}{{ goods.cart_info.productPresell.delivery_day }}天内
                        </span>
                      </div>
                    </div>
                    <div style="color: red;">
                      {{
                        goods.is_refund == 1
                          ? "退款中"
                          : goods.is_refund == 2
                          ? "部分退款"
                          : goods.is_refund == 3
                          ? "全部退款"
                          : ""
                      }}
                    </div>
                    <!-- <span class="num">x{{ goods.product_num }}</span> -->
                  </div>
                  <div v-else class="goods-item">
                    <div class="img-box">
                      <img
                        :src="
                          (goods.cart_info.productAttr &&
                            goods.cart_info.productAttr.image) ||
                            goods.cart_info.product.image
                        "
                        alt=""
                      />
                    </div>
                    <div class="info-txt">
                      <div class="title line1">
                        <span
                          v-if="
                            goods.product_type != 0 && goods.product_type != 10
                          "
                          :class="'font_bg-red type' + goods.product_type"
                          >{{
                            goods.product_type == 1
                              ? "秒杀"
                              : goods.product_type == 2
                              ? "预售"
                              : goods.product_type == 3
                              ? "助力"
                              : goods.product_type == 4
                              ? "拼团"
                              : ""
                          }}</span
                        >
                        {{ goods.cart_info.product.store_name }}
                      </div>
                      <div class="info" v-if="goods.cart_info.productAttr.sku">
                        {{ goods.cart_info.productAttr.sku }}
                      </div>
                      <div class="price" v-if="goods.cart_info.productAttr.price">
                        ¥{{ goods.cart_info.productAttr.price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                      <div class="price" v-else>
                        ¥{{ goods.cart_info.product.price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                    </div>
                    <div style="color: red;">
                      {{
                        goods.is_refund == 1
                          ? "退款中"
                          : goods.is_refund == 2
                          ? "部分退款"
                          : goods.is_refund == 3
                          ? "全部退款"
                          : ""
                      }}
                    </div>
                    <!-- <span class="num">x{{ goods.product_num }}</span> -->
                  </div>
                </template>
                <div class="btn-wrapper">
                  <div class="pay" @click.stop="goDetail(item)">查看详情</div>
                  <div class="pay" v-if="item.status == 2" @click.stop="goEvaluate(item)">
                    去评价
                  </div>
                  <div
                    class="pay"
                    v-if="item.status == 1 && item.is_virtual != 4"
                    @click.stop="confirmOrder(item, index)"
                  >
                    确认收货
                  </div>
                  <div
                    class="rest"
                    v-if="!item.receipt 
                    && item.status != -1 
                    && item.open_receipt == 1 
                    && item.is_virtual !== 1"
                    @click.stop="showInvoicePupon(item)"
                   >
                   申请开票
                   </div>
                  <div
                    class="rest"
                    v-if="item.status == 3 && item.product_type == 0 && item.is_virtual != 4"
                    @click.stop="goOrderConfirm(item)"
                  >
                    再次购买
                  </div>
                  <div
                    class="rest"
                    v-if="item.status == 1 && item.delivery_type == 1 && item.is_virtual != 4"
                    @click.stop="goLogistics(item)"
                  >
                    查看物流
                  </div>
                  
                 
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="foot">
            <p>
              共{{ item.total_num || 0 }}件商品，实付金额
              <span class="price">￥{{ item.pay_price }} </span>
              <span v-if="item.pay_postage > 0"
                >(含运费{{ item.pay_postage }})</span
              >
            </p>

          </div> -->
        </li>
      </ul>
      <ul v-else-if="orderStatus == 0">
        <li v-for="(item, index) in orderList" :key="index">
          <div class="bd">
            <div class="order-txt acea-row row-middle">
              <span class="status">待付款</span>
              <span class="order-time">订单日期: {{ item.create_time }}</span>
              <nuxt-link
                v-if="item.merchant"
                class="mer_name acea-row row-center row-middle"
                :to="{ path: '/store', query: { id: item.mer_id } }"
                >
                <span class="iconfont-h5 icon-ic_mall"></span>
                <span class="name">{{ item.merchant.mer_name }}</span>
              </nuxt-link
              >
            </div>
            <div class="content" @click="goDetail(item)" >
              <div
                v-for="(order, j) in item.orderList"
                :key="order.order_id + j"
              >
                <div v-for="(goods, g) in order.orderProduct" :key="g" class="acea-row row-between">
                  <template v-if="order.activity_type === 2">
                    <div class="goods-item">
                    <div
                      class="img-box"
                      v-if="
                        goods.cart_info.productAttr || goods.cart_info.product
                      "
                    >
                      <img
                        :src="
                          (goods.cart_info.productAttr &&
                            goods.cart_info.productAttr.image) ||
                            goods.cart_info.product.image
                        "
                        alt=""
                      />
                      <span>预售</span>
                    </div>
                    <div class="info-txt">
                      <div class="title line2">
                        {{ goods.cart_info.product.store_name }}
                      </div>
                      <div class="info" v-if="goods.cart_info.productAttr.sku">
                        {{ goods.cart_info.productAttr.sku }}
                      </div>
                      <div
                        class="price"
                        v-if="goods.cart_info.productAttr.price"
                      >
                        ¥ {{ goods.cart_info.productAttr.price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                      <div class="price" v-else>
                        ¥ {{ goods.cart_info.productPresellAttr.presell_price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                      <div class="develity_date">
                        <!--全款预售-->
                        <span
                          v-if="
                            goods.cart_info.productPresell.presell_type === 1
                          "
                        >
                          发货时间：{{
                            goods.cart_info.productPresell.delivery_type === 1
                              ? "支付成功后"
                              : "预售结束后"
                          }}{{
                            goods.cart_info.productPresell.delivery_day
                          }}天内
                        </span>
                        <!--定金预售-->
                        <span
                          v-if="
                            goods.cart_info.productPresell.presell_type === 2
                          "
                        >
                          发货时间：{{
                            goods.cart_info.productPresell.delivery_type === 1
                              ? "支付尾款后"
                              : "预售结束后"
                          }}{{
                            goods.cart_info.productPresell.delivery_day
                          }}天内
                        </span>
                      </div>
                    </div>
                  </div>
                  </template>
                  
                  <div v-else class="goods-item">
                    <div
                      class="img-box"
                      v-if="
                        goods.cart_info.productAttr || goods.cart_info.product
                      "
                    >
                      <img
                        :src="
                          (goods.cart_info.productAttr &&
                            goods.cart_info.productAttr.product &&
                            goods.cart_info.productAttr.product.image) ||
                            goods.cart_info.product.image
                        "
                        alt=""
                      />
                    </div>
                    <div class="info-txt">
                      <div class="title line2">
                        {{ goods.cart_info.product.store_name }}
                      </div>
                      <div class="info" v-if="goods.cart_info.productAttr.sku">
                        {{ goods.cart_info.productAttr.sku }}
                      </div>
                      <div
                        class="price"
                        v-if="goods.cart_info.productAttr.price"
                      >
                        ¥ {{ goods.cart_info.productAttr.price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                      <div class="price" v-else>
                        ¥ {{ goods.cart_info.product.price }}
                        <span class="num">×{{ goods.product_num }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="btn-wrapper">
                    <div class="rest" @click.stop="cancelOrder(item, index)">取消订单</div>
                    <div class="pay" @click.stop="goBuy(item)">立即支付</div>
                    <div class="pay" @click.stop="goDetail(item)">查看详情</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="foot">
            <p>
              共{{ item.total_num || 0 }}件商品，总金额
              <span>￥{{ item.pay_price }} </span>
            </p>
            
          </div> -->
        </li>
      </ul>
    </div>
    <div class="pages-box" v-if="total > 0 && orderList.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        @current-change="bindPageCur"
        :total="total"
      >
      </el-pagination>
    </div>
    <div class="empty-box" v-if="orderList.length == 0">
      <img src="~assets/images/noorder.png" alt="" />
      <p>亲，暂无订单信息哟~</p>
    </div>
    <add-invoicing
      ref="addInvoicing"
      @applyInvoice="applyInvoice"
    ></add-invoicing>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { Message, MessageBox } from "element-ui";
import priceFormat from "@/components/priceFormat";
import addInvoicing from "@/components/addInvoicing";
export default {
  name: "orderList",
  auth: "guest",
  components: { addInvoicing, priceFormat },
  scrollToTop: true,
  data() {
    return {
      tabCur: "",
      tabList: [
        {
          title: "全部订单",
          key: "-2"
        },
        {
          title: "待付款",
          key: "0",
          numberName: "unpaid_count"
        },
        {
          title: "待发货",
          key: "0",
          numberName: "unshipped_count"
        },
        {
          title: "待收货",
          key: "1",
          numberName: "received_count"
        },
        {
          title: "待评论",
          key: "2",
          numberName: "evaluated_count"
        }
      ],
      orderList: [],
      orderData: {},
      orderStatus: 0,
      invoiceDialog: false,
      invoice_order_id: "",
      total: 0,
      page: 1,
      limit: 10,
      keyword: "",
    };
  },
  fetch({ store }) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  head() {
    return {
      title: "我的订单-" + this.$store.state.titleCon
    };
  },
  beforeMount() {},
  mounted() {
    this.orderStatus = this.$route.query.type || -1;
    if (this.orderStatus == 0) {
      this.getUnPayList();
    } else {
      this.getList();
    }
    Promise.all([this.getNumber()]);
  },
  methods: {
    //  再次购买
    goOrderConfirm(order) {
      const data = order.orderProduct.map(pro => {
        return {
          cart_num: pro.product_num,
          product_attr_unique: pro.product_sku,
          product_id: pro.product_id
        };
      });
      let that = this;
      that.$axios
        .post("/api/user/cart/again", {
          data
        })
        .then(res => {
          that.$router.replace({
            path: "/order_confirm",
            query: { new: 1, cartId: res.data.cart_id.join(",") }
          });
        });
    },
    // 申请开票
    showInvoicePupon(item) {
      this.invoice_order_id = item.order_id;
      this.$refs.addInvoicing.showInvoicePupon();
    },
    // 开票
    applyInvoice(data) {
      let that = this;
      that.$axios
        .post("/api/order/receipt/" + that.invoice_order_id, data)
        .then(res => {
          this.getList();
          return Message.success(res.message);
        })
        .catch(err => {
          return Message.error(err.message);
        });
    },
    // 选项卡
    bindTab(status) {
      this.orderStatus = status;
      this.page = 1;
      this.$set(this, "orderList", []);
      if (status == 0) {
        this.getUnPayList();
      } else {
        this.getList();
      }
    },
    search() {
      if(!this.keyword){
        this.$message.error("请输入商品名称或订单号!");
        return
      }else {
        if (this.orderStatus == 0) {
          this.getUnPayList();
        } else {
          this.getList();
        }
      }
    },
    // 获取订单个数
    getNumber() {
      this.$axios.get("/api/order/number").then(res => {
        this.orderData = res.data;
      });
    },
    // 获取订单列表
    getList() {
      this.$axios
        .get("/api/order/list", {
          params: {
            status: this.orderStatus - 1,
            page: this.page,
            limit: this.limit,
            store_name: this.keyword
          }
        })
        .then(res => {
          this.orderList = res.data.list;
          this.total = res.data.count;
        });
    },
    //待付款订单列表
    getUnPayList() {
      this.$axios
        .get("/api/order/group_order_list", {
          params: {
            page: this.page,
            limit: this.limit,
            store_name: this.keyword
          }
        })
        .then(res => {
          this.orderList = res.data.list;
          this.total = res.data.count;
        });
    },
    // 取消订单
    cancelOrder(item, index) {
      MessageBox.confirm("确定取消该订单吗？", "提示").then(res => {
        this.$axios
          .post("/api/order/cancel/" + item.group_order_id)
          .then(data => {
            this.getUnPayList();
            this.getNumber();
            return Message.success(data.message);
          });
      });
    },
    
    // 查看详情
    goDetail(item) {
      
      if (this.orderStatus == 0) {
        this.$router.push({
          path: `/order_stay_detail`,
          query: {
            orderId: item.group_order_id
          }
        });
      } else {
        this.$router.push({
          path: `/order_detail`,
          query: {
            orderId: item.order_id
          }
        });
      }
    },
    // 去评价
    goEvaluation(item) {
      this.$router.push({
        path: `/evaluation`,
        query: {
          unique: item.unique
        }
      });
    },
    //  去评价
    goEvaluate(item) {
      this.$router.push({
        path: "/evaluation",
        query: {
          unique: item.orderProduct[0].order_product_id,
          order_id: item.order_id
        }
      });
    },
    // 去退款
    goRefound(item) {
      this.$router.push({
        path: `/refund_confirm`,
        query: {
          orderId: item.order_id
        }
      });
    },
    // 去物流
    goLogistics(item) {
      this.$router.push({
        path: `/logistics`,
        query: {
          orderId: item.order_id
        }
      });
    },
    // 确认收货
    confirmOrder(item, index) {
      let that = this;
      MessageBox.confirm(
        "为保障权益，请收到货确认无误后，再确认收货",
        "提示"
      ).then(res => {
        this.$axios.post("/api/order/take/" + item.order_id).then(data => {
          that.orderList.splice(index, 1);
          that.getNumber();
          that.getList();
          return Message.success(data.message);
        });
      });
    },
    // 分页点击
    bindPageCur(data) {
      this.page = data;
      if (this.orderStatus == 0) {
        this.getUnPayList();
      } else {
        this.getList();
      }
    },
    // 立即支付
    goBuy(item) {
      this.$router.push({
        path: "/payment",
        query: {
          result: item.group_order_id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.user-com-tab {
  border: none;
  .order-search {
    width: 220px;
    height: 32px;
    padding: 0 10px 0 40px;
    position: relative;
    background: #F7F7F7;
    border-radius: 20px;
    .iconfont {
      height: 20px;
      line-height: 20px;
      position: absolute;
      padding: 0;
      left: 10px;
      top: 6px;
    }
    .input {
      border: none;
      outline: none;
      background: transparent;
 
    }
  }
  .item {
    color: #666;
    padding: 0;
    margin-right: 40px;
    &.on {
      color: #E93323;
      font-weight: 500;
      &::after {
        display: none;
      }
    }
  }
}
.font_bg-red {
  background-color: #e93323;
  border: 1px solid #e93323;
  display: inline-block;
  align-items: center;
  color: #fff;
  font-size: 10px;
  text-align: center;
  border-radius: 2px;
  padding: 0 3px;
  line-height: 15px;
  &.type2 {
    background-color: #fd6523;
    border: 1px solid #fd6523;
  }
}
.user-order-list {
  .order-list {
    margin-top: 10px;
  }
  li {
    margin-bottom: 20px;
    position: relative;
    border: 1px solid #EEEEEE;
    .refund-icon {
      position: absolute;
      right: 50px;
      top: 40px;
    }
    .bd {
      cursor: pointer;
      .order-txt {
        font-size: 14px;
        color: #333;
        height: 56px;
        background: #FAFAFA;
        padding: 0 20px;
        .mer_name {
          display: inline-block;
          margin-left: 60px;
          
          &:hover {
            color: #e93323;
          }
          .iconfont-h5 {
            color: #333;
            margin-right: 3px;
          }
        }
        .status {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 46px;
          height: 20px;
          background: #e93323;
          color: #fff;
          border-radius: 2px;
          font-size: 12px;
          margin-right: 16px;
        }
        .order-time {
          color: #999999;
        }
        .order-price {
         font-size: 16px;
        //  font-weight: bold;
        }
      }
      .content {
        margin-top: 20px;
        padding: 0 44px 20px 20px;
        .goods-item {
          display: flex;
          position: relative;
          align-items: center;
          
          .img-box {
            width: 100px;
            height: 100px;
            position: relative;
            border-radius: 4px;
            span {
              display: block;
              width: 100%;
              text-align: center;
              font-size: 12px;
              line-height: 18px;
              background: rgba(0, 0, 0, 0.5);
              position: absolute;
              left: 0;
              bottom: 0;
              color: #fff;
            }
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
          }
          .info-txt {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 500px;
            margin-left: 20px;
            font-size: 14px;
            color: #333;
            .title {
              font-weight: bold;
            }
            .info {
              margin-top: 16px;
            }
            .price {
              margin-top: 15px;
              
            }
          }
          .develity_date {
            color: #fd6523;
            margin-top: 6px;
            font-size: 14px;
          }
          .num {
            display: inline-block;
            margin-left: 5px;
          }
        }
      }
    }
    .btn-wrapper {
        div {
          width: 86px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #ccc;
          border-radius: 15px;
          font-size: 12px;
          color: #333;
          cursor: pointer;
          margin-bottom: 14px;
          &:last-child {
            margin-bottom: 0;
          }
          &.pay {
            border-color: #e93323;
            color: #e93323;
          }
        }
      }
    .foot {
      padding-right: 32px;
      p {
        text-align: right;
        color: #666;
        font-size: 14px;
        .price {
          color: #e93323;
        }
      }
      
    }
  }
}
.pages-box {
  margin-top: 30px;
  text-align: right;
}
</style>
