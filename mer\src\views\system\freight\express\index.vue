<template>
  <div class="divBox">
    <div class="selCard">
      <el-form :model="tableFrom" ref="searchForm" inline size="small" label-width="55px" @submit.native.prevent>
        <el-form-item :label="$t('搜索:')" prop="keyword">
          <el-input
            v-model="tableFrom.keyword"
            :placeholder="$t('请输入物流公司名称或者编码')"
            class="selWidth"
            @input="getList(1)"

          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="getList(1)">{{ $t('搜索') }}</el-button>
          <el-button size="small" @click="searchReset()">{{ $t('重置') }}</el-button> 
        </el-form-item>
      </el-form>
    </div>
    <el-card class="mt14">
      
      <el-table v-loading="listLoading" :data="tableData.data" size="small">
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="name" :label="$t('物流公司名称')" min-width="120" />
        <el-table-column prop="code" :label="$t('编码')" min-width="120" />
        <el-table-column
          :label="$t('是否显示')"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.mer_status"
              :active-value="1"
              :inactive-value="0"
              active-text="显示"
              inactive-text="隐藏"
              :width="55"
              @change="onchangeIsShow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="$t('操作')" min-width="80" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.partner_id == 1 || scope.row.partner_key == 1 || scope.row.net_name == 1" type="text" size="small" @click="handleEdit(scope.row.id)">{{ $t('月结账号编辑') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { expressLst, accountUpdate, expressStatuseApi } from '@/api/system'
export default {
  name: 'ExpressFreight',
  data() {
    return {
      tableData: {
        data: [],
        total: 0
      },
      listLoading: true,
      tableFrom: {
        page: 1,
        limit: 20,
        date: '',
        keyword: ''
      },
      tabClickIndex: ''
    }
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    /**重置 */
    searchReset(){
      this.$refs.searchForm.resetFields()
      this.getList(1)
    },
    // 编辑
    handleEdit(id) {
      this.$modalForm(accountUpdate(id)).then(() => this.getList(''))
    },
    // 列表
    getList(num) {
      this.listLoading = true
      this.tableFrom.page = num || this.tableFrom.page
      expressLst(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.data.list
          this.tableData.total = res.data.count
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList('')
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList('')
    },
    onchangeIsShow(row) {
      expressStatuseApi(row.id, {mer_status:row.mer_status}).then(({ message }) => {
        this.$message.success(message)
      }).catch(({ message }) => {
        this.$message.error(message)
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form.scss';
</style>
