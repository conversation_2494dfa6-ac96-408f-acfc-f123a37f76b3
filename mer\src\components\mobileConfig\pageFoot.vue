<template>
    <div class="mobile-config">
        <Form ref="formInline">
            <div v-for="(item,key) in rCom" :key="key">
                <component :is="item.components.name" ref="childData" :configObj="configObj"
                    :configNme="item.configNme"></component>
            </div>
        </Form>
    </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import toolCom from '@/components/mobileConfigRight/index.js'
import rightBtn from '@/components/rightBtn/index.vue';
export default {
    name: 'pageFoot',
    cname: this.$t(this.$t('底部菜单')),
    components: {
        ...toolCom,
        rightBtn
    },
    data() {
        return {
            hotIndex: 1,
            configObj: {

            }, // 配置对象
            rCom: [
                {
                    components: toolCom.c_set_up,
                    configNme: 'setUp'
                }
            ] // 当前页面组件
        }
    },
    watch: {
        'configObj.setUp.tabVal': {
            handler (nVal, oVal) {
                var arr = [this.rCom[0]]
                if (nVal == 0) {
                    let tempArr = [
                        {
                            components: toolCom.c_status,
                            configNme: 'status'
                        },
                        {
                            components: toolCom.c_foot,
                            configNme: 'menuList'
                        },
                    ]
                    this.rCom = arr.concat(tempArr)
                } else {
                    let tempArr = [
                        {
                            components: toolCom.c_bg_color,
                            configNme: 'txtColor'
                        },
                        {
                            components: toolCom.c_bg_color,
                            configNme: 'activeTxtColor'
                        },
                        {
                            components: toolCom.c_bg_color,
                            configNme: 'bgColor'
                        },
                    ]
                    this.rCom = arr.concat(tempArr)
                }
            },
            deep: true
        }
    },
    mounted() {
        this.configObj = this.$store.state.mobildConfig.pageFooter
    },
    methods: {}
}
</script>

<style scoped lang="scss">
.title-tips{
    padding-bottom: 10px;
    font-size: 14px;
    color: #333;
    span{
        margin-right: 14px;
        color: #999;
    }
}       
            
</style>
